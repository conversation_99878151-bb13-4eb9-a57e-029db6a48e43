<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

$applicationBuilder = Application::configure(basePath: dirname(__DIR__));

/**
 * --------------------------------------------------------------------------
 * Application Routing Configuration
 * --------------------------------------------------------------------------
 *
 * Here you may specify the route files that should be loaded by your
 * application. By default, the API routes are loaded from the
 * routes/api.php file. You are free to add additional route files
 * as required by your application's structure.
 */

$applicationBuilder->withRouting(
    api: __DIR__ . '/../routes/api.php',
);

/**
 * --------------------------------------------------------------------------
 * Middleware Registration
 * --------------------------------------------------------------------------
 *
 * Here you may register middleware for your application. Middleware provide
 * a convenient mechanism for filtering HTTP requests entering your
 * application. You may add global or route-specific middleware here.
 */

$applicationBuilder->withMiddleware(function (Middleware $middleware): void {
    $middleware->api([
        \App\Http\Middleware\MaintenanceModeMiddleware::class,
    ]);

    $baseMiddlewareGroup = [
        \App\Http\Middleware\ApiKeyMiddleware::class,
        \App\Http\Middleware\LogApiMiddleware::class,
        \App\Http\Middleware\CleanRequestMiddleware::class,
    ];

    $middleware->group('session', [
        ...$baseMiddlewareGroup,
        \App\Http\Middleware\ValidateTokenForUserMiddleware::class,
        \App\Http\Middleware\AuthUserTokenMiddleware::class,
    ]);

    $middleware->group('apikey.without.biometry', [...$baseMiddlewareGroup,]);

    $middleware->group('apikey.with.biometry', [
        ...$baseMiddlewareGroup,
        \App\Http\Middleware\ValidateTokenForBiometryMiddleware::class,
    ]);

    $middleware->group('global', [
        ...$baseMiddlewareGroup,
        \App\Http\Middleware\ValidateTokenForUserOrBiometryMiddleware::class,
        \App\Http\Middleware\AuthUserTokenIfAnyMiddleware::class,
    ]);

    $middleware->alias([
        'log' => \App\Http\Middleware\LogJwtMiddleware::class,
        'clean.api' => \App\Http\Middleware\CleanRequestMiddleware::class,
        'maintenanceApiKey' => \App\Http\Middleware\ApiKeyMaintainanceMode::class,
        'token.external.user.auth' => \App\Http\Middleware\AuthUserTokenMiddleware::class,
        'update.customer.contact.interceptor' => \App\Http\Middleware\InterceptUpdateCustomerContactMiddleware::class,
    ]);
});

/**
 * --------------------------------------------------------------------------
 * Exception Handling Configuration
 * --------------------------------------------------------------------------
 *
 * Here you may register custom exception handlers for your application.
 * This allows you to define how different exceptions are rendered or
 * reported, providing a centralized location for exception handling.
 */

$applicationBuilder->withExceptions(function (Exceptions $exceptions): void {
    //
});

return $applicationBuilder->create();
