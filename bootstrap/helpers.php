<?php

declare(strict_types=1);

use Illuminate\Support\Facades\DB;

if (!function_exists('requireRouteFiles')) {
    function requireRouteFiles(string|array $path)
    {
        $paths = is_string($path) ? [$path] : $path;

        return array_map(function (string $path) {
            if (!file_exists($path)) {
                throw new Exception("Route file could not be found.");
            }

            require $path;
        }, $paths);
    }
}

if (!function_exists('dropConstraintIfExists')) {
    /**
     * Helper to drop database constraints if it exists.
     */
    function dropConstraintIfExists(string $table, string $constraintName): void
    {
        DB::statement("IF EXISTS (SELECT * FROM sys.objects WHERE name = '$constraintName' AND type = 'UQ') 
        ALTER TABLE $table DROP CONSTRAINT $constraintName");
    }
}

if (!function_exists('createUniqueNullableFilteredIndex')) {
    /**
     * Helper to create a filtered unique nullable index.
     */
    function createUniqueFilteredIndex($table, $indexName, $column)
    {
        DB::statement("CREATE UNIQUE INDEX {$indexName} ON $table({$column}) WHERE {$column} IS NOT NULL");
    }
}

if (!function_exists('downgradeApiVersionInUrl')) {
    function downgradeApiVersionInUrl(string $url): string
    {
        return str_replace('/v1', '/v0', $url);
    }
}
