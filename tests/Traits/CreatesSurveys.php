<?php

namespace Tests\Traits;

use App\Models\Option;
use App\Models\Question;
use App\Models\Survey;
use Illuminate\Support\Arr;

trait CreatesSurveys
{
    protected function createSurveyWithQuestionsAndOptions(
        int $questionNumber = 3,
        array $optionNumber = [2, 3],
    ): array {
        $survey = Survey::factory()->create();

        $questions = collect();
        foreach (range(1, $questionNumber) as $_) {
            $questions->push(Question::factory()->create(['survey_id' => $survey->id]));
        }

        $options = collect();
        foreach ($questions as $question) {
            foreach (range(1, Arr::random($optionNumber)) as $_) {
                $options->push(Option::factory()->create(['question_id' => $question->id]));
            }
        }

        return [
            $survey,
            $questions,
            $options,
        ];
    }
}
