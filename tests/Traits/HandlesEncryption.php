<?php

namespace Tests\Traits;

use App\Services\Crypt\Rsa;

trait HandlesEncryption
{
    private ?Rsa $rsa = null;

    protected function encrypt(string $data): string
    {
        $this->initService();

        return $this->rsa->encrypt($data);
    }

    protected function decrypt(string $data): string
    {
        $this->initService();

        return $this->rsa->decrypt($data);
    }

    private function initService(): void
    {
        if (!is_null($this->rsa)) {
            return;
        }

        $this->rsa = app(Rsa::class);
    }
}
