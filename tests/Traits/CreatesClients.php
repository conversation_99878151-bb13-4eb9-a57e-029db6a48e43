<?php

namespace Tests\Traits;

use App\Models\Client;
use App\Models\PasswordHistories;
use App\Models\Systems;

trait CreatesClients
{
    protected function createClientWithDefaultSystem(?Client $defaultUser = null, array $attributes = []): Client
    {
        $user = $defaultUser ?? Client::factory()->create($attributes);

        $user->systems()->attach(Systems::default()->id);

        return $user;
    }

    protected function createClientWithSystemAndPassword(?Client $defaultUser = null): Client
    {
        $user = $this->createClientWithDefaultSystem($defaultUser);
        PasswordHistories::factory()->create(['user_id' => $user->id]);

        return $user;
    }

    protected function createClientWithPassword(): Client
    {
        $user = Client::factory()->create();
        PasswordHistories::factory()->create(['user_id' => $user->id]);

        return $user;
    }
}
