<?php

namespace Tests\Unit\Dtos;

use App\Dtos\Dto;
use PHPUnit\Framework\TestCase;

class DtoTest extends TestCase
{
    /**
     * Base DTO for testing.
     */
    private Dto $dummyDto;

    /**
     * Base DTO for testing.
     */
    private Dto $longDummyDto;

    protected function setUp(): void
    {
        parent::setUp();

        $this->dummyDto = new class (1, 2) extends Dto {
            public function __construct(
                public readonly int $a,
                public readonly int $b,
            ) {
            }
        };

        $this->longDummyDto = new class ('value of first', 'value of second') extends Dto {
            public function __construct(
                public readonly string $firstProp,
                public readonly string $secondProp,
            ) {
            }
        };
    }

    /**
     * @test
     */
    public function itMakesADtoFromAList(): void
    {
        $dto = $this->dummyDto::class::fromArray([6, 7]);

        $this->assertEquals(6, $dto->a);
        $this->assertEquals(7, $dto->b);
    }

    /**
     * @test
     */
    public function itMakesADtoFromAMap(): void
    {
        $dto = $this->dummyDto::class::fromArray([
            'b' => 6,
            'a' => 7,
        ]);

        $this->assertEquals(7, $dto->a);
        $this->assertEquals(6, $dto->b);
    }

    /**
     * @test
     */
    public function itMakesADtoFromASnakeCaseMap(): void
    {
        $dto = $this->longDummyDto::class::fromArray([
            'first_prop' => 'a',
            'secondProp' => 'b',
        ]);

        $this->assertEquals('a', $dto->firstProp);
        $this->assertEquals('b', $dto->secondProp);
    }

    /**
     * @test
     */
    public function itMakesADto(): void
    {
        $dto = $this->dummyDto::class::make(6, 7);

        $this->assertEquals(6, $dto->a);
        $this->assertEquals(7, $dto->b);
    }

    /**
     * @test
     */
    public function itReturnsDtoAsArray(): void
    {
        $this->assertEquals(['a' => 1, 'b' => 2], $this->dummyDto->toArray());
    }

    /**
     * @test
     */
    public function itReturnsDtoAsSnakeCaseArray(): void
    {
        $this->assertEquals([
            'first_prop' => 'value of first',
            'second_prop' => 'value of second',
        ], $this->longDummyDto->toSnakeCaseArray());
    }

    /**
     * @test
     */
    public function itReturnsDtoAsCamelCaseArray(): void
    {
        $dto = new class ('value') extends Dto {
            public function __construct(
                public readonly string $only_prop,
            ) {
            }
        };

        $this->assertEquals(['onlyProp' => 'value'], $dto->toCamelCaseArray());
    }

    /**
     * @test
     */
    public function itTransformsValuesIntoACustomArray(): void
    {
        $result = $this->longDummyDto->toCustomArray([
            'secondProp' => 'two',
            'firstProp' => 'one',
            'nonexistentProp' => 'unknown',
        ]);

        $this->assertEquals([
            'two' => 'value of second',
            'one' => 'value of first',
        ], $result);
    }

    /**
     * @test
     */
    public function itReturnsEmptyWhenTransformingValuesIntoCustomArrayWithUnkeyedArray(): void
    {
        $result = $this->dummyDto->toCustomArray([
            'unkeyedArray',
            'secondValue',
        ]);

        $this->assertEquals([], $result);
    }

    /**
     * @test
     */
    public function itMergesExtraValuesWhenCallingToCustomArrayWith(): void
    {
        $result = $this->longDummyDto->toCustomArrayWith(
            [
                'secondProp' => 'two',
                'firstProp' => 'one',
                'nonexistentProp' => 'unknown',
            ],
            [
                'extraProp' => 'extra value',
            ],
        );

        $this->assertEquals([
            'two' => 'value of second',
            'one' => 'value of first',
            'extraProp' => 'extra value',
        ], $result);
    }

    /**
     * @test
     */
    public function itMapsTheValuesOfADtoIntoANewOne(): void
    {
        $dto = $this->dummyDto->map(fn ($n) => $n + 1);

        $this->assertEquals(2, $dto->a);
        $this->assertEquals(3, $dto->b);
    }

    /**
     * @test
     */
    public function itTransformsTheValuesOfADtoIntoANewOne(): void
    {
        $dto = $this->dummyDto->transform(fn ($_) => [
            'a' => 100,
            'b' => 200,
        ]);

        $this->assertEquals(100, $dto->a);
        $this->assertEquals(200, $dto->b);
    }

    /**
     * @test
     */
    public function itDuplicatesADto(): void
    {
        $dto = $this->dummyDto->duplicate();

        $this->assertEquals(1, $dto->a);
        $this->assertEquals(2, $dto->b);
    }
}
