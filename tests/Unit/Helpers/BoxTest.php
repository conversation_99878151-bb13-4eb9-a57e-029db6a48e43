<?php

namespace Tests\Unit\Helpers;

use App\Helpers\Box;
use Exception;
use PHPUnit\Framework\TestCase;

class BoxTest extends TestCase
{
    /**
     * @test
     */
    public function itCallsAFunctionOnNonNullValue(): void
    {
        $box = Box::put('some value')
            ->peek(fn ($string) => $string . '!');

        $this->assertEquals('some value!', $box->andOpen());
    }

    /**
     * @test
     */
    public function itDoesNotCallAFunctionOnNullValue(): void
    {
        $box = Box::put('some value')
            ->peek(fn ($string) => null)
            ->peek(fn ($maybeString) => $maybeString->thisFailsIfCalled());

        $this->assertNull($box->andOpen());
    }

    /**
     * @test
     */
    public function itReturnsDefaultOnOpenWhenValueIsNull(): void
    {
        $result = Box::put(null)
            ->andOpen('some other value');

        $this->assertEquals('some other value', $result);

        $result = Box::put(null)
            ->andOpen(fn () => 'some other value');

        $this->assertEquals('some other value', $result);
    }

    /**
     * @test
     */
    public function itReturnsValueOnOpenWhenDefaultIsProvided(): void
    {
        $result = Box::put('some value')
            ->andOpen('some other value');

        $this->assertEquals('some value', $result);

        $result = Box::put('some value')
            ->andOpen(fn ($_) => 'some other value');

        $this->assertEquals('some value', $result);
    }

    /**
     * @test
     */
    public function itReturnsDefaultOnOpenWhenValueIsEmpty(): void
    {
        $result = Box::put('')
            ->andOpenOnEmpty('some other value');

        $this->assertEquals('some other value', $result);

        $result = Box::put(0)
            ->andOpenOnEmpty(fn () => 'some other value');

        $this->assertEquals('some other value', $result);
    }

    /**
     * @test
     */
    public function itReturnsValueOnOpenOnEmptyWhenDefaultIsProvided(): void
    {
        $result = Box::put('some value')
            ->andOpenOnEmpty('some other value');

        $this->assertEquals('some value', $result);

        $result = Box::put('some value')
            ->andOpenOnEmpty(fn ($_) => 'some other value');

        $this->assertEquals('some value', $result);
    }

    /**
     * @test
     */
    public function itThrowsOnNullValue(): void
    {
        $exception = new class () extends Exception {};
        $box = Box::put(null);

        $this->expectException(get_class($exception));

        $box->andOpenAndThrow($exception);
    }

    /**
     * @test
     */
    public function itDoesNotThrowOnNonNullValue(): void
    {
        $this->assertTrue(Box::put(true)->andOpenAndThrow(new Exception()));
    }

    /**
     * @test
     */
    public function itGetsAPropertyBoxedUp(): void
    {
        $object = new class () {
            public string $property = 'some value';
        };

        $this->assertEquals('some value', Box::put($object)->property->andOpen());
    }

    /**
     * @test
     */
    public function itDoesNotGetAnInexistentProperty(): void
    {
        $object = new class () {};

        $this->assertNull(Box::put($object)->property->andOpen());
    }

    /**
     * @test
     */
    public function itCallsAMethodBoxedUp(): void
    {
        $object = new class () {
            public function someFn(): string
            {
                return 'some value';
            }
        };

        $this->assertEquals('some value', Box::put($object)->someFn()->andOpen());
    }

    /**
     * @test
     */
    public function itDoesNotCallAnInexistentMethod(): void
    {
        $this->assertEquals('some value', Box::put('not an object')->someFn()->andOpen('some value'));
    }

    /**
     * @test
     */
    public function itGetsAValueFromAnArray(): void
    {
        $array = ['a' => 'some value'];

        $this->assertEquals('some value', Box::put($array)['a']->andOpen());
    }
}
