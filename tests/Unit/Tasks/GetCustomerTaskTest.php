<?php

namespace Tests\Unit\Tasks;

use App\Dtos\Customer;
use App\Repos\GetCustomerRepo;
use App\Tasks\GetCustomerTask;
use Mockery;
use PHPUnit\Framework\TestCase;
use Tests\Traits\ClosesMockery;

class GetCustomerTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itReturnsACustomer(): void
    {
        $customerId = random_int(100, 999);
        $customer = Customer::fromArray([
            'id' => $customerId,
            'firstName' => 'firstName',
            'secondName' => 'secondName',
            'firstSurname' => 'firstSurname',
            'secondSurname' => 'secondSurname',
            'marriedName' => 'marriedName',
            'birthDate' => 'birthDate',
            'dui' => 'dui',
            'nit' => 'nit',
        ]);

        $repo = Mockery::mock(GetCustomerRepo::class);
        $repo
            ->shouldReceive('prepare')
            ->with($customerId)
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($customer);

        $result = (new GetCustomerTask($repo))
            ->withCustomerId($customerId)
            ->do();

        $this->assertSame($customer, $result);
    }
}
