<?php

namespace Tests\Unit\Tasks\Twilio;

use App\Tasks\Twilio\CreateConversationTask;
use Mockery;
use PHPUnit\Framework\TestCase;
use stdClass;
use Tests\Traits\ClosesMockery;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class CreateConversationTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itCreatesAConversation(): void
    {
        // No type, twilio uses anonymous classes.
        $conversations = Mockery::mock();
        $conversations
            ->shouldReceive('create')
            ->with(['friendlyName' => 'Conversation'])
            ->once()
            ->andReturn(Mockery::mock(TwilioConversation::class));

        $v1 = new stdClass();
        $v1->conversations = $conversations;
        $firstConversations = new stdClass();
        $firstConversations->v1 = $v1;
        $twilioClient = Mockery::mock(TwilioClient::class);
        $twilioClient->conversations = $firstConversations;

        $result = (new CreateConversationTask())
            ->withTwilioClient($twilioClient)
            ->do();

        $this->assertInstanceOf(TwilioConversation::class, $result);
    }
}
