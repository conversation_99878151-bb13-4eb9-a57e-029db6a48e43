<?php

namespace Tests\Unit\Tasks\Twilio;

use App\Tasks\Twilio\AddParticipantTask;
use Illuminate\Support\Str;
use Mockery;
use PHPUnit\Framework\TestCase;
use stdClass;
use Tests\Traits\ClosesMockery;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Rest\Conversations\V1\Conversation\ParticipantInstance as TwilioParticipant;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class AddParticipantTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itAddsAParticipant(): void
    {
        $identity = Str::random(10);

        $conversation = Mockery::mock(TwilioConversation::class);
        $conversation->sid = Str::random();

        // No type, twilio uses anonymous classes.
        $participants = Mockery::mock();
        $participants
            ->shouldReceive('create')
            ->with(['identity' => $identity])
            ->once()
            ->andReturn(Mockery::mock(TwilioParticipant::class));

        $conversations = new stdClass();
        $conversations->participants = $participants;

        // No type, twilio uses anonymous classes.
        $v1 = Mockery::mock();
        $v1
            ->shouldReceive('conversations')
            ->with($conversation->sid)
            ->once()
            ->andReturn($conversations);

        $firstConversations = new stdClass();
        $firstConversations->v1 = $v1;
        $twilioClient = Mockery::mock(TwilioClient::class);
        $twilioClient->conversations = $firstConversations;

        $result = (new AddParticipantTask())
            ->withTwilioClient($twilioClient)
            ->withConversation($conversation)
            ->withIdentity($identity)
            ->do();

        $this->assertInstanceOf(TwilioParticipant::class, $result);
    }
}
