<?php

namespace Tests\Unit\Tasks;

use App\Repos\ValidateContactRepo;
use App\Tasks\CheckEmailOrPhoneIsNewTask;
use Mockery;
use PHPUnit\Framework\TestCase;
use Tests\Traits\ClosesMockery;

class CheckEmailOrPhoneIsNewTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itCheckWhetherAContactIsNew(): void
    {
        $email = '<EMAIL>';
        $repo = Mockery::mock(ValidateContactRepo::class);
        $repo
            ->shouldReceive('contactExists')
            ->with($email)
            ->once()
            ->andReturn(false);

        $this->assertTrue(
            (new CheckEmailOrPhoneIsNewTask($repo))
                ->withEmailOrPhone($email)
                ->do()
        );
    }
}
