<?php

namespace Tests\Unit\Tasks\Auth;

use App\Dtos\ExternalToken;
use App\Dtos\GenerateExternalToken;
use App\Repos\GenerateTokenRepo;
use App\Tasks\Auth\GenerateTokenForUserTask;
use Illuminate\Support\Str;
use Mockery;
use PHPUnit\Framework\TestCase;
use Tests\Traits\ClosesMockery;

class GenerateTokenForUserTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itReturnsANewToken(): void
    {
        $token = ExternalToken::make(Str::random(), Str::random());

        $repo = Mockery::mock(GenerateTokenRepo::class);
        $repo->shouldReceive('prepare')
            ->once()
            ->andReturn($repo);
        $repo->shouldReceive('fetch')
            ->once()
            ->andReturn($token);

        $result = (new GenerateTokenForUserTask($repo))
            ->withDeviceId(Str::random())
            ->withNickname(Str::random())
            ->withClientId(Str::random())
            ->do();

        $this->assertSame($token, $result);
    }

    /**
     * @test
     */
    public function itReturnsANewTokenWhenPreparedWithDto(): void
    {
        $token = ExternalToken::make(Str::random(), Str::random());

        $repo = Mockery::mock(GenerateTokenRepo::class);
        $repo->shouldReceive('prepare')
            ->once()
            ->andReturn($repo);
        $repo->shouldReceive('fetch')
            ->once()
            ->andReturn($token);

        $dto = GenerateExternalToken::make(
            Str::random(),
            Str::random(),
            Str::random(),
        );
        $result = (new GenerateTokenForUserTask($repo))
            ->withGenerateExternalToken($dto)
            ->do();

        $this->assertSame($token, $result);
    }
}
