<?php

namespace Tests\Unit\Tasks\Auth;

use App\Repos\ValidateTokenRepo;
use App\Tasks\Auth\ValidateTokenTask;
use Mockery;
use PHPUnit\Framework\TestCase;
use Tests\Traits\ClosesMockery;

class ValidateTokenTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itReturnsTrueOnValidToken(): void
    {
        $repo = Mockery::mock(ValidateTokenRepo::class);
        $repo
            ->shouldReceive('prepare')
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn(true);

        $this->assertTrue((new ValidateTokenTask($repo))->do());
    }

    /**
     * @test
     */
    public function itReturnsFalseOnInvalidToken(): void
    {
        $repo = Mockery::mock(ValidateTokenRepo::class);
        $repo
            ->shouldReceive('prepare')
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn(false);

        $this->assertFalse((new ValidateTokenTask($repo))->do());
    }
}
