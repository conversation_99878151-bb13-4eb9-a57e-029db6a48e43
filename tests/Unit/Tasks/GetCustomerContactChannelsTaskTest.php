<?php

namespace Tests\Unit\Tasks;

use App\Dtos\ContactChannels;
use App\Dtos\Phone;
use App\Repos\GetCustomerContactChannelsRepo;
use App\Tasks\GetCustomerContactChannelsTask;
use Mockery;
use PHPUnit\Framework\TestCase;
use Tests\Traits\ClosesMockery;

class GetCustomerContactChannelsTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itReturnsContactChannels(): void
    {
        $customerId = random_int(100, 999);
        $channels = ContactChannels::make(
            Phone::make((string) random_int(7000000, 7999999))
        );

        $repo = Mockery::mock(GetCustomerContactChannelsRepo::class);
        $repo
            ->shouldReceive('prepare')
            ->with($customerId)
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($channels);

        $result = (new GetCustomerContactChannelsTask($repo))
            ->withCustomerId($customerId)
            ->do();

        $this->assertSame($channels, $result);
    }
}
