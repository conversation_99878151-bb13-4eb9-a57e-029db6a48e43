<?php

namespace Tests\Unit\Services;

use App\Services\PasswordGenerator;
use PHPUnit\Framework\TestCase;
use Random\Randomizer;

class PasswordGeneratorTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected PasswordGenerator $passwords;

    protected function setUp(): void
    {
        parent::setUp();

        $this->passwords = new PasswordGenerator(new Randomizer());
    }

    /**
     * @test
     */
    public function itGeneratesValidPasswords(): void
    {
        $passwords = [];
        foreach (range(1, 10) as $_) {
            $passwords[] = $this->passwords->generate();
        }

        $this->assertEquals(
            count($passwords),
            count(array_unique($passwords)),
            'Passwords are repeated in a small sample',
        );

        $allowedChars = array_merge(...PasswordGenerator::ALLOWED_CHARS);
        foreach ($passwords as $password) {
            foreach (str_split($password) as $char) {
                $this->assertTrue(in_array($char, $allowedChars, true), "'{$char}' should not be in a password");
            }
        }
    }
}
