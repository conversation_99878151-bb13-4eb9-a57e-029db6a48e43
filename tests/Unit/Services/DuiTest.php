<?php

namespace Tests\Unit\Services;

use App\Services\Dui;
use PHPUnit\Framework\TestCase;

class DuiTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected Dui $dui;

    protected function setUp(): void
    {
        parent::setUp();

        $this->dui = new Dui();
    }

    /**
     * @test
     */
    public function itValidatesDuis(): void
    {
        $invalidDuis = [
            null,
            '',
            1,
            214789,
            1.75,
            [],
            'no actual numbers',
            '1234567890',
            '12-456-890',
            '00000000-0',
            '00000002-5',
        ];
        foreach ($invalidDuis as $dui) {
            $this->assertFalse($this->dui->isValid($dui));
        }

        $validDuis = [
            '00016297-5',
        ];
        foreach ($validDuis as $dui) {
            $this->assertTrue($this->dui->isValid($dui), "{$dui} is invalid");
        }
    }

    /**
     * @test
     */
    public function itGeneratesValidDuis(): void
    {
        foreach (range(1, 100) as $_) {
            $dui = $this->dui->generate();
            $this->assertTrue($this->dui->isValid($dui), "{$dui} is invalid");
        }
    }
}
