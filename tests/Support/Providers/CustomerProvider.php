<?php

declare(strict_types=1);

namespace Tests\Support\Providers;

use Illuminate\Support\Str;
use App\Repos\GetCustomerContactChannelsRepo;
use Faker\Factory as FakerFactory;
use Faker\Generator;

class CustomerProvider
{
    protected static Generator $faker;

    public static function init(): void
    {
        if (empty(self::$faker)) {
            self::$faker = FakerFactory::create();
        }
    }

    public static function getValidCustomerDTOArray(): array
    {
        self::init();

        return [
            'id' => random_int(1000, 9999),
            'firstName' => Str::random(),
            'secondName' => Str::random(),
            'firstSurname' => Str::random(),
            'secondSurname' => Str::random(),
            'marriedName' => Str::random(),
            'birthDate' => Str::random(),
            'dui' => self::$faker->numerify('#######-#'),
            'nit' => Str::random(),
        ];
    }

    public static function getValidExternalCustomerData(): array
    {
        self::init();

        return [
            'CustomerId' =>  random_int(1000, 9999),
            'FirstName' => Str::random(),
            'SecondName' => Str::random(),
            'FirstSurname' => Str::random(),
            'SecondSurname' => Str::random(),
            'MarriedName' => Str::random(),
            'BirthDate' => Str::random(),
            'DUI' => self::$faker->numerify('#######-#'),
            'NIT' => Str::random(),
        ];
    }

    public static function getValidExternalCustomerContactChannel(?string $type = null): array
    {
        self::init();

        if (empty($type)) {
            $type = rand(0, 1)
                ? GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE
                : GetCustomerContactChannelsRepo::PHONE_TYPE_CODE;
        }

        $value = $type === GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE
            ? self::$faker->email()
            : self::$faker->numerify('#######');

        return [
            'ContactTypeCode' => $type,
            'CustomerContacts' => [
                ['ContactValue' => $value],
            ],
        ];
    }
}
