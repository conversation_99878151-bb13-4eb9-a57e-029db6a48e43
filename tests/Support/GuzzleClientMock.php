<?php

declare(strict_types=1);

namespace Tests\Support;

use Guzzle<PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\ClientInterface;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;

class GuzzleClientMock
{
    /**
     * Create a new instance of the Guzzle client with a mock handler
     * 
     * @property string $abstraction The abstractions to be used for the HTTP client should implement GuzzleHttp\ClientInterface.
     */
    public static function create(string $abstraction = Client::class, array $responses = []): ClientInterface
    {
        $mockHandler = new MockHandler($responses);

        $handlerStack = HandlerStack::create($mockHandler);

        return new $abstraction(['handler' => $handlerStack]);
    }
}
