<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Dtos\OtpValidation;
use App\Repos\ValidateOtpRepo;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;

class ValidateOtpRepoTest extends TestCase
{
    private const URL_PATH = '/OTPCodes';

    public function setUp(): void
    {
        parent::setUp();
    }

    #[Test]
    public function it_sends_request_to_validate_otp_code_to_core_api(): void
    {
        Http::fake();

        /** @var ValidateOtpRepo */
        $repo = $this->app->make(ValidateOtpRepo::class);

        $data = new OtpValidation('shared-key', '123-456');

        $repo
            ->prepare($data)
            ->fetch();

        Http::assertSent(
            fn($request) => parse_url($request->url(), PHP_URL_PATH) === self::URL_PATH
        );
    }

    #[Test]
    #[\PHPUnit\Framework\Attributes\TestWith([ValidateOtpRepo::VALID_OTP_CODE, true])]
    #[\PHPUnit\Framework\Attributes\TestWith([999, false])]
    public function it_validates_otp_code_base_on_the_response_from_the_core_api($responseCode, $expected): void
    {
        Http::fake([
            '*' => Http::response([
                'Status' => [
                    'ResponseStatus' => [
                        'Code' => $responseCode,
                    ],
                ],
            ]),
        ]);

        $repo = $this->app->make(ValidateOtpRepo::class);
        
        $data = new OtpValidation('shared-key', '123-456');

        $this->assertSame($expected, $repo->isValidOtp($data));
    }
}
