<?php

namespace Tests\Feature\Helpers;

use Tests\TestCase;
use App\Helpers\ToolKit;
use App\Enums\SecurityType;
use App\Models\ServicesRouter;
use Illuminate\Foundation\Testing\LazilyRefreshDatabase;
use Illuminate\Support\Facades\Route;
use PHPUnit\Framework\Attributes\Test;

class ToolKitTest extends TestCase
{
    use LazilyRefreshDatabase;

    private ServicesRouter $sampleExternalRoute;

    public function setUp(): void
    {
        parent::setUp();

        $this->sampleExternalRoute = ServicesRouter::create([
            'service_name' => 'TestRoute',
            'description' => 'TestRoute.',
            'exposed_service_url' => 'TestRoute',
            'has_token' => true,
            'method' => 'POST',
            'security' => 'APIKEY',
            'group' => 'Atlantida',
            'third_parties_url' => config('services.baes.base_url') . 'TestRoute',
            'token' => config('services.baes.key'),
        ]);
    }

    #[Test]
    public function can_map_routes(): void
    {
        ToolKit::mapRoutes(
            security: SecurityType::NONE,
            routes: $this->sampleExternalRoute->exposed_service_url,
        );

        $mappedRoutes = ToolKit::getAllMappedRoutes();

        $this->assertContains($this->sampleExternalRoute->exposed_service_url, $mappedRoutes);
    }

    #[Test]
    public function can_get_all_mapped_routes_flattened(): void
    {
        ToolKit::mapRoutes(
            security: SecurityType::NONE,
            routes: 'SampleRoute',
        );

        ToolKit::mapRoutes(
            security: SecurityType::APIKEY_WITH_BIOMETRY,
            routes: 'AnotherRoute',
        );

        $mappedRoutes = ToolKit::getAllMappedRoutes();

        $this->assertContains('SampleRoute', $mappedRoutes);
        $this->assertContains('AnotherRoute', $mappedRoutes);
    }

    #[Test]
    public function can_get_routes_by_their_security_type(): void
    {
        ToolKit::mapRoutes(
            security: SecurityType::NONE,
            routes: 'SampleRoute',
        );

        ToolKit::mapRoutes(
            security: SecurityType::APIKEY_WITH_BIOMETRY,
            routes: 'AnotherRoute',
        );

        $mappedRoutes = ToolKit::getMappedRoutes(SecurityType::NONE);

        $this->assertContains('SampleRoute', $mappedRoutes);
        $this->assertNotContains('AnotherRoute', $mappedRoutes);
    }

    #[Test]
    public function can_stablish_services_router_records_as_routes_by_their_security_type(): void
    {
        $security = SecurityType::from($this->sampleExternalRoute->security);

        ToolKit::servicesRoutes($security);

        $routeUris = $this->getDefinedRoutesUris();

        $this->assertContains($this->sampleExternalRoute->exposed_service_url, $routeUris);
    }

    #[Test]
    public function when_stablishing_services_routes_as_formal_routes_we_can_exclude_some_routes(): void
    {
        $security = SecurityType::from($this->sampleExternalRoute->security);

        ToolKit::servicesRoutes(security: $security, exclude: [$this->sampleExternalRoute->exposed_service_url]);

        $routeUris = $this->getDefinedRoutesUris();

        $this->assertNotContains($this->sampleExternalRoute->exposed_service_url, $routeUris);
    }

    #[Test]
    public function can_stablish_only_wanted_services_routes_as_formal_routes(): void
    {
        ToolKit::only([$this->sampleExternalRoute->exposed_service_url]);

        $routeUris = $this->getDefinedRoutesUris();

        $this->assertContainsEquals($this->sampleExternalRoute->exposed_service_url, $routeUris);
    }

    private function getDefinedRoutesUris(): array
    {
        $definedRoutes = Route::getRoutes()->getRoutes();

        return array_map(
            callback: fn($route) => str_replace(
                search: 'api/',
                replace: '',
                subject: $route->uri()
            ),
            array: $definedRoutes
        );
    }
}
