<?php

namespace Tests\Feature\Services\Auth;

use App\DTO\VBankAccountDTO;
use App\Dtos\Login;
use Tests\TestCase;
use App\Models\Client;
use App\Models\Device;
use App\Dtos\Coordinates;
use App\Dtos\DeviceLogin;
use Illuminate\Http\Request;
use App\Dtos\LoginCredentials;
use App\Enums\Code\HomologatedLoginCode;
use App\Repos\GenerateTokenRepo;
use App\Services\Configurations;
use Illuminate\Auth\AuthManager;
use Tests\Traits\CreatesClients;
use Tests\Traits\HandlesEncryption;
use App\Events\SuccessfulLoginEvent;
use App\Repos\DummyGenerateTokenRepo;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use App\Exceptions\Auth\UserIsDisabled;
use App\Exceptions\Auth\WrongCredentials;
use PHPUnit\Framework\Attributes\TestDox;
use App\Exceptions\Auth\MaxDevicesReached;
use App\Listeners\Login\NotifyUserOfLogin;
use App\Exceptions\Auth\UserIsBlockedByOtp;
use App\Listeners\Login\LogSuccessfulLogin;
use App\Services\Auth\PasswordLoginService;
use App\Exceptions\Auth\UserCantAccessSystem;
use App\Exceptions\Auth\InvalidPasswordPeriod;
use App\Listeners\Login\RegisterOrUpdateDevice;
use App\Exceptions\Auth\UserIsBlockedByFaceAuth;
use App\Tasks\ValidateVBankAccountTask;
use Mockery\MockInterface;

class PasswordLoginServiceTest extends TestCase
{
    use HandlesEncryption;
    use CreatesClients;

    private PasswordLoginService $service;

    private Client $client;

    private Device $device;

    private Request $request;

    private MockInterface|ValidateVBankAccountTask $validateVBankAccountTask;

    protected function setUp(): void
    {
        parent::setUp();

        $dummyGenerateTokenRepo = app()->make(DummyGenerateTokenRepo::class);

        $this->app->instance(GenerateTokenRepo::class, $dummyGenerateTokenRepo);

        $this->validateVBankAccountTask = $this->mock(ValidateVBankAccountTask::class);

        $this->validateVBankAccountTask
            ->shouldReceive('withCustomerId')
            ->withAnyArgs()
            ->andReturn($this->validateVBankAccountTask);

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);

        $this->service = $this->app->make(PasswordLoginService::class);

        // Password "123456"
        $this->client = $this->createClientWithSystemAndPassword();

        $this->device = Device::factory()->create(['client_id' => $this->client->id]);

        $this->request = $this->app->make(Request::class);
    }

    #[Test]
    public function can_login_user(): void
    {
        $credentials = new LoginCredentials(nickname: $this->client->nickname, password: '123456');

        $deviceData = new DeviceLogin(
            id: $this->device->device_id,
            name: $this->device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $loginData = new Login(
            credentials: $credentials,
            request: $this->request,
            device: $deviceData
        );

        Event::fake();

        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn(new VBankAccountDTO($credentials->nickname, false));

        $this->service->login($loginData, $this->client);

        $authenticatedUser = app()->make(AuthManager::class)->guard('api')->user()->id;

        $this->assertEquals($this->client->id, $authenticatedUser);

        Event::assertDispatched(SuccessfulLoginEvent::class);

        Event::assertListening(SuccessfulLoginEvent::class, NotifyUserOfLogin::class);

        Event::assertListening(SuccessfulLoginEvent::class, RegisterOrUpdateDevice::class);

        Event::assertListening(SuccessfulLoginEvent::class, LogSuccessfulLogin::class);
    }

    #[Test]
    public function returns_code_for_non_homologated_user_without_vbank(): void
    {
        $credentials = new LoginCredentials($this->client->nickname, '123456');

        $deviceData = new DeviceLogin(
            id: $this->device->device_id,
            name: $this->device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $loginData = new Login(
            credentials: $credentials,
            request: $this->request,
            device: $deviceData
        );

        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn(null);

        $session = $this->service->login($loginData, $this->client);

        $this->assertSame(HomologatedLoginCode::NON_HOMOLOGATED_USER_NO_VBANK->value, $session->homologationCode);
    }

    #[Test]
    public function returns_code_for_non_homologated_user_with_vbank(): void
    {
        $credentials = new LoginCredentials($this->client->nickname, '123456');

        $deviceData = new DeviceLogin(
            id: $this->device->device_id,
            name: $this->device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $loginData = new Login(
            credentials: $credentials,
            request: $this->request,
            device: $deviceData
        );

        $vBankAccountResult = new VBankAccountDTO($credentials->nickname, false);

        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($vBankAccountResult);

        $session = $this->service->login($loginData, $this->client);

        $this->assertSame(HomologatedLoginCode::NON_HOMOLOGATED_USER_VBANK->value, $session->homologationCode);
    }

    #[Test]
    public function catches_if_user_is_using_wrong_credentials(): void
    {
        $credentials = new LoginCredentials(nickname: $this->client->nickname, password: 'wrong_credentials');

        $deviceData = new DeviceLogin(
            id: $this->device->device_id,
            name: $this->device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $this->expectException(WrongCredentials::class);

        $currentPasswordLoginAttempts = $this->client->password_login_attempts;

        $loginData = Login::make($credentials, $this->request, $deviceData);

        $this->service->login($loginData, $this->client);

        $this->client->refresh();

        // Assert that password login attempts are incremented when login fails due to wrong credentials
        $this->assertSame($this->client->password_login_attempts, $currentPasswordLoginAttempts + 1);
    }


    #[Test]
    #[TestDox('When user with status BLOCK_BY_FACE_AUTH tries to login it throws UserIsBlockedByFaceAuth')]
    public function catches_if_user_is_blocked_by_face_auth(): void
    {
        $client = Client::factory()->create(['status' => Client::BLOCK_BY_FACE_AUTH]);

        $device = Device::factory()->create(['client_id' => $client->id]);

        $deviceData = new DeviceLogin(
            id: $device->device_id,
            name: $device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $credentials = new LoginCredentials(nickname: $client->nickname, password: '123456');

        $this->expectException(UserIsBlockedByFaceAuth::class);

        $loginData = new Login($credentials, $this->request, $deviceData);

        $this->service->login($loginData, $client);
    }

    #[Test]
    #[TestDox('When user with status BLOCKOTP tries to login it throws UserIsBlockedByOtp')]
    public function catches_if_user_is_blocked_by_otp(): void
    {
        $client = Client::factory()->create(['status' => Client::BLOCKOTP]);

        $device = Device::factory()->create(['client_id' => $client->id]);

        $deviceData = new DeviceLogin(
            id: $device->device_id,
            name: $device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $credentials = new LoginCredentials(nickname: $client->nickname, password: '123456');

        $this->expectException(UserIsBlockedByOtp::class);

        $loginData = new Login($credentials, $this->request, $deviceData);

        $this->service->login($loginData, $client);
    }

    #[Test]
    #[TestDox('When user with status DISABLE tries to login it throws UserIsDisabled')]
    public function catches_if_user_is_disabled(): void
    {
        $client = Client::factory()->create(['status' => Client::DISABLE]);

        $device = Device::factory()->create(['client_id' => $client->id]);

        $deviceData = new DeviceLogin(
            id: $device->device_id,
            name: $device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $credentials = new LoginCredentials(nickname: $client->nickname, password: '123456');

        $this->expectException(UserIsDisabled::class);

        $loginData = new Login($credentials, $this->request, $deviceData);

        $this->service->login($loginData, $client);
    }

    #[Test]
    public function catches_if_user_password_is_expired(): void
    {
        $expirationDays = (app()->make(Configurations::class))
            ->getConfigurations('PERIODO_VALIDEZ_PASSWORD') + 1;

        // Take client out of first period grace
        $this->client->created_at = now()->subDays($expirationDays);
        $this->client->save();
        $this->client->passwords()->update(['created_at' => now()->subDays($expirationDays)]);

        $credentials = new LoginCredentials(nickname: $this->client->nickname, password: '123456');

        $deviceData = new DeviceLogin(
            id: $this->device->device_id,
            name: $this->device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $loginData = new Login($credentials, $this->request, $deviceData);

        $this->expectException(InvalidPasswordPeriod::class);

        $this->service->login($loginData, $this->client);
    }

    #[Test]
    public function catches_if_user_does_not_have_access_to_default_system(): void
    {
        $client = Client::factory()->create();

        $device = Device::factory()->create(['client_id' => $client->id]);

        $deviceData = new DeviceLogin(
            id: $device->device_id,
            name: $device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $credentials = new LoginCredentials(nickname: $client->nickname, password: '123456');

        $this->expectException(UserCantAccessSystem::class);

        $loginData = new Login($credentials, $this->request, $deviceData);

        $this->service->login($loginData, $client);
    }

    #[Test]
    public function catches_if_user_cant_use_a_new_device_if_they_already_reached_maximum_devices_registered(): void
    {
        $credentials = new LoginCredentials(nickname: $this->client->nickname, password: '123456');

        $maximumDevices = (app()->make(Configurations::class))->maximunDevices() + 1;

        Device::factory($maximumDevices)->create(['client_id' => $this->client->id]);

        $deviceData = new DeviceLogin(
            id: 'new_device_id',
            name: 'new_device_name',
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $this->expectException(MaxDevicesReached::class);

        $loginData = new Login($credentials, $this->request, $deviceData);

        $this->service->login($loginData, $this->client);
    }
}
