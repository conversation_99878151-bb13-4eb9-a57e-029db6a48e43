<?php

namespace Tests\Feature\Services\Auth;

use Tests\TestCase;
use App\Models\Device;
use Mockery\MockInterface;
use App\Services\Crypt\Rsa;
use Illuminate\Support\Str;
use App\DTO\VBankAccountDTO;
use App\Dtos\BiometricLogin;
use Illuminate\Http\Request;
use App\Repos\GenerateTokenRepo;
use Illuminate\Auth\AuthManager;
use Tests\Traits\CreatesClients;
use Tests\Traits\CreatesRsaKeys;
use Illuminate\Support\Facades\App;
use App\Repos\DummyGenerateTokenRepo;
use Illuminate\Support\Facades\Event;
use App\Tasks\ValidateVBankAccountTask;
use App\Listeners\Login\ExternalLogLogin;
use App\Services\Auth\BiometricLoginService;
use App\Events\SuccessfulBiometricLoginEvent;
use App\Listeners\Login\LogSuccessfulBiometricLogin;

class BiometricLoginServiceTest extends TestCase
{
    use CreatesClients;
    use CreatesRsaKeys;

    /**
     * Subject to test.
     */
    protected BiometricLoginService $service;

    private MockInterface|ValidateVBankAccountTask $validateVBankAccountTask;

    protected function setUp(): void
    {
        parent::setUp();

        // Temporal binding for testing.
        App::instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));

        $this->validateVBankAccountTask = $this->mock(ValidateVBankAccountTask::class);

        $this->validateVBankAccountTask
            ->shouldReceive('withCustomerId')
            ->andReturnSelf();

        $vBankAccount = new VBankAccountDTO(username: 'test', requiresPasswordReset: false);

        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($vBankAccount);

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);

        $this->service = App::make(BiometricLoginService::class);
    }

    /**
     * @test
     */
    public function itLogins(): void
    {
        [$privateKey, $publicKey] = $this->createKeyPair();
        $challenge = Str::random();
        $user = $this->createClientWithSystemAndPassword();
        $device = Device::factory()->active()->create([
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $challenge,
            'client_id' => $user->id,
        ]);
        $signature = Rsa::signWith($challenge, $privateKey);

        $loginData = BiometricLogin::make(
            $signature,
            $device->device_id,
            $device->device_name,
            App::make(Request::class),
        );

        Event::fake();
        $this->service->login($loginData);

        $this->assertEquals($user->id, App::make(AuthManager::class)->guard('api')->user()->id);
        Event::assertDispatched(SuccessfulBiometricLoginEvent::class);
        Event::assertListening(SuccessfulBiometricLoginEvent::class, ExternalLogLogin::class);
        Event::assertListening(SuccessfulBiometricLoginEvent::class, LogSuccessfulBiometricLogin::class);
    }
}
