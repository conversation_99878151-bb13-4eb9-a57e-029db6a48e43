<?php

namespace Tests\Feature\Services\Auth;

use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Event;
use App\Services\RegisterNewUserService;
use App\Repos\GetCustomerContactChannelsRepo;
use Tests\Support\Providers\CustomerProvider;
use Symfony\Component\HttpFoundation\Response;
use App\Events\NewUserRegistrationRequestEvent;

class RegisterNewUserServiceTest extends TestCase
{
    protected string $baseUrl;

    public function setUp(): void
    {
        parent::setUp();

        $this->baseUrl = config('services.baes.base_url');
    }

    public function testItStartTheProcessToRegisterANewUser(): void
    {
        Event::fake();

        $creditcardApplicationId = random_int(min: 5000, max: 5999);

        $customerData = CustomerProvider::getValidExternalCustomerData();
        $customerId = data_get($customerData, 'CustomerId');

        $contactChannels = [
            CustomerProvider::getValidExternalCustomerContactChannel(GetCustomerContactChannelsRepo::PHONE_TYPE_CODE),
            CustomerProvider::getValidExternalCustomerContactChannel(GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE)
        ];

        Http::fake([
            $this->baseUrl . 'CustomerData*' => Http::response([
                'Data' => $customerData
            ], Response::HTTP_OK),
            $this->baseUrl . 'CustomerContacts*' => Http::response([
                'Data' => [
                    'ContactTypes' => $contactChannels,
                ],
            ], Response::HTTP_OK),
        ]);

        $this->app->make(RegisterNewUserService::class)
            ->register($customerId, $creditcardApplicationId);

        Event::assertDispatched(NewUserRegistrationRequestEvent::class);
    }
}
