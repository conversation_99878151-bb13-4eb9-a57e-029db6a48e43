<?php

namespace Tests\Feature\Services\Auth;

use App\Dtos\Login;
use Tests\TestCase;
use App\Models\Client;
use App\Models\Device;
use App\Dtos\Coordinates;
use App\Dtos\DeviceLogin;
use Mockery\MockInterface;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Dtos\LoginCredentials;
use App\Enums\Code\HomologatedLoginCode;
use App\Repos\GenerateTokenRepo;
use Tests\Traits\CreatesClients;
use App\Events\SuccessfulLoginEvent;
use App\Repos\DummyGenerateTokenRepo;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use App\Services\Auth\VBankLoginService;
use App\Exceptions\Auth\WrongCredentials;
use App\Enums\Code\VBankLoginResponseCodes;
use App\Exceptions\Auth\UserIsDisabled;
use Symfony\Component\HttpFoundation\Response;
use App\Tasks\Auth\ValidateVBankCredentialsTask;
use PHPUnit\Framework\Attributes\TestWith;
use App\Exceptions\Auth\UnknownAuthenticationError;

class VBankLoginServiceTest extends TestCase
{
    use CreatesClients;

    private Client $client;

    private Device $device;

    private DeviceLogin $deviceLogin;

    private Request $request;

    private VBankLoginService $vBankLoginService;

    private MockInterface&ValidateVBankCredentialsTask $validateVBankCredentialsTask;

    public function setUp(): void
    {
        parent::setUp();

        $dummyGenerateTokenRepo = app()->make(DummyGenerateTokenRepo::class);

        app()->instance(GenerateTokenRepo::class, $dummyGenerateTokenRepo);

        // Default password for this client is 123456
        $this->client = $this->createClientWithSystemAndPassword();

        $this->client->update([
            'nickname_vbank' => Str::random(),
            'homologated' => true
        ]);

        $this->validateVBankCredentialsTask = $this->mock(ValidateVBankCredentialsTask::class);

        $this->validateVBankCredentialsTask->shouldReceive('withCredentials')
            ->withAnyArgs()
            ->once()
            ->andReturn($this->validateVBankCredentialsTask);

        $this->device = Device::factory()->create(['client_id' => $this->client->id]);

        $this->deviceLogin = new DeviceLogin(
            id: $this->device->device_id,
            name: $this->device->device_name,
            firebaseToken: '',
            coordinates: Coordinates::zero(),
            isHuawei: false,
        );

        $this->request = app()->make(Request::class);
    }

    #[Test]
    public function can_login_homologated_user(): void
    {
        $credentials = new LoginCredentials(
            nickname: $this->client->nickname_vbank,
            password: '123456'
        );

        $loginData = new Login(
            credentials: $credentials,
            request: $this->request,
            device: $this->deviceLogin
        );

        Event::fake();

        $this->validateVBankCredentialsTask->shouldReceive('do')
            ->once()
            ->andReturn([
                'Data' => [
                    'RequirePasswordReset' => false
                ],
                'Status' => [
                    'ResponseStatus' => [
                        'Code' => Response::HTTP_OK
                    ]
                ]
            ]);

        app()->instance(ValidateVBankCredentialsTask::class, $this->validateVBankCredentialsTask);

        $this->vBankLoginService = app()->make(VBankLoginService::class);

        $session = $this->vBankLoginService->login($loginData, $this->client);

        $this->assertNotNull($session->token);

        Event::assertDispatched(SuccessfulLoginEvent::class);
    }

    #[Test]
    #[TestWith([VBankLoginResponseCodes::USER_NOT_FOUND])]
    #[TestWith([VBankLoginResponseCodes::WRONG_PASSWORD])]
    #[TestWith([VBankLoginResponseCodes::USER_WITHOUT_EBANK_CONTRACT])]
    #[TestWith([VBankLoginResponseCodes::USER_MIGRATED])]
    public function catches_if_user_entered_wrong_credentials(VBankLoginResponseCodes $responseCode): void
    {
        $nickname = $responseCode === VBankLoginResponseCodes::USER_NOT_FOUND
            ? 'wrong_nickname'
            : $this->client->nickname_vbank;

        $credentials = new LoginCredentials(nickname: $nickname, password: 'wrong_password');

        $loginData = new Login(
            $credentials,
            $this->request,
            $this->deviceLogin
        );

        $this->validateVBankCredentialsTask->shouldReceive('do')
            ->once()
            ->andReturn([
                'Data' => [
                    'RequirePasswordReset' => false
                ],
                'Status' => [
                    'ResponseStatus' => [
                        'Code' => $responseCode->value
                    ]
                ]
            ]);

        app()->instance(ValidateVBankCredentialsTask::class, $this->validateVBankCredentialsTask);

        $this->vBankLoginService = app()->make(VBankLoginService::class);

        $this->expectException(WrongCredentials::class);

        $response = $this->vBankLoginService->login($loginData, $this->client);

        $this->assertEquals($response->homologationCode, HomologatedLoginCode::WRONG_CREDENTIALS->value);
    }

    #[Test]
    #[TestWith([VBankLoginResponseCodes::USER_DISABLED])]
    #[TestWith([VBankLoginResponseCodes::USER_BLOCKED])]
    #[TestWith([VBankLoginResponseCodes::USER_BLOCK_BY_TOO_MANY_ATTEMPTS])]
    public function catches_if_user_is_disabled_or_blocked(VBankLoginResponseCodes $responseCode): void
    {
        $nickname = $responseCode === VBankLoginResponseCodes::USER_NOT_FOUND
            ? 'wrong_nickname'
            : $this->client->nickname_vbank;

        $credentials = new LoginCredentials(nickname: $nickname, password: 'wrong_password');

        $loginData = new Login(
            $credentials,
            $this->request,
            $this->deviceLogin
        );

        $this->validateVBankCredentialsTask->shouldReceive('do')
            ->once()
            ->andReturn([
                'Data' => [
                    'RequirePasswordReset' => false
                ],
                'Status' => [
                    'ResponseStatus' => [
                        'Code' => $responseCode->value
                    ]
                ]
            ]);

        app()->instance(ValidateVBankCredentialsTask::class, $this->validateVBankCredentialsTask);

        $this->vBankLoginService = app()->make(VBankLoginService::class);

        $this->expectException(UserIsDisabled::class);

        $response = $this->vBankLoginService->login($loginData, $this->client);

        $this->assertEquals($response->homologationCode, HomologatedLoginCode::DISABLED_USER->value);
    }

    #[Test]
    public function catches_unexpected_response_codes(): void
    {
        $credentials = new LoginCredentials(
            nickname: $this->client->nickname_vbank,
            password: '123456'
        );

        $loginData = new Login(
            credentials: $credentials,
            request: $this->request,
            device: $this->deviceLogin
        );

        Event::fake();

        $this->validateVBankCredentialsTask->shouldReceive('do')
            ->once()
            ->andReturn([
                'Status' => [
                    'ResponseStatus' => [
                        'Code' => Response::HTTP_OK
                    ]
                ]
            ]);


        app()->instance(ValidateVBankCredentialsTask::class, $this->validateVBankCredentialsTask);

        $this->vBankLoginService = app()->make(VBankLoginService::class);

        $this->expectException(UnknownAuthenticationError::class);

        $response = $this->vBankLoginService->login($loginData, $this->client);

        $this->assertEquals($response->homologationCode, HomologatedLoginCode::UNEXPECTED_ERROR->value);
    }
}
