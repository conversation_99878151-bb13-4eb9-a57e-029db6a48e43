<?php

namespace Tests\Feature\Services;

use App\Models\Client;
use App\Services\UsernameGenerator;
use Tests\TestCase;

class UsernameGeneratorTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected UsernameGenerator $generator;

    protected function setUp(): void
    {
        parent::setUp();

        $this->generator = new UsernameGenerator();
    }

    /**
     * @test
     */
    public function itGeneratesUniqueUsernames(): void
    {
        foreach (range(1, 1000) as $_) {
            $this->assertDatabaseMissing(
                'clients',
                ['nickname' => $this->generator->generate('a', 'b')],
            );
        }
    }

    /**
     * @test
     */
    public function itConcatenatesTwoPartsAndANumber(): void
    {
        $username = $this->generator->generate('abc', 'def');

        $firstPart = substr($username, 0, 3);
        $this->assertSame('abc', $firstPart);

        $secondPart = substr($username, 3, 3);
        $this->assertSame('def', $secondPart);

        $thirdPart = substr($username, 6);
        $this->assertTrue(is_numeric($thirdPart));
    }

    /**
     * @test
     */
    public function itConvertsToAsciiAndRemovesSpecialChars(): void
    {
        $this->assertSame('nnaaa', substr($this->generator->generate('nñaàá'), 0, 5));
        $this->assertSame('sacbe', substr($this->generator->generate("sac'bé"), 0, 5));
        $this->assertSame('nnaa', substr($this->generator->generate('nñaà  '), 0, 4));
        $this->assertSame('abbc', substr($this->generator->generate('〹  abʂbʷc'), 0, 4));
        $this->assertSame('ouiwe', substr($this->generator->generate(' o8u ΐwe'), 0, 5));
        $this->assertSame('abcdef', substr($this->generator->generate('ABC DEF'), 0, 6));
    }
}
