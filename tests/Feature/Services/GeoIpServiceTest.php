<?php

namespace Tests\Feature\Services;

use App\Services\GeoIpService;
use Tests\TestCase;

/**
 * @coversDefaultClass \App\Services\GeoIpService
 */
class GeoIpServiceTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected GeoIpService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new GeoIpService();
    }

    /**
     * @test
     * @covers ::getCoordinates
     */
    public function shouldFoundCoordinatesFromAValidIp(): void
    {
        $coordinates = $this->service->getCoordinates("*******");

        $this->assertNotEquals("0", $coordinates->longitude);
        $this->assertNotEquals("0", $coordinates->latitude);
    }

    /**
     * @test
     * @dataProvider ipProvider
     * @covers ::getCoordinates
     */
    public function shouldReturnZeroCoordinatesFromIpNotValid(string $ip): void
    {
        $coordinates = $this->service->getCoordinates($ip);

        $this->assertEquals("0", $coordinates->longitude);
        $this->assertEquals("0", $coordinates->longitude);
    }

    public static function ipProvider()
    {
        return [
            'Invalid IP' => ["8.0.0.256"],
            'Absent IP' => ["127.0.0.1"],
            'Valid IP withou DB info' => ["*******"],
        ];
    }
}
