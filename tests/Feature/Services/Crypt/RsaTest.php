<?php

namespace Tests\Feature\Services\Crypt;

use App\Models\Config;
use App\Services\Crypt\Rsa;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Tests\TestCase;
use Tests\Traits\CreatesRsaKeys;

class RsaTest extends TestCase
{
    use CreatesRsaKeys;

    /**
     * Subject to test.
     */
    protected Rsa $rsa;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rsa = new Rsa();
    }

    /**
     * @test
     */
    public function itEncryptsAndDecryptsData(): void
    {
        $dataToEncrypt = 'Some data';

        $encrypted = $this->rsa->encrypt($dataToEncrypt);
        $this->assertIsString($encrypted);
        $this->assertNotEmpty($encrypted);
        $this->assertNotEquals($dataToEncrypt, $encrypted);

        $decrypted = $this->rsa->decrypt($encrypted);
        $this->assertIsString($decrypted);
        $this->assertNotEmpty($decrypted);
        $this->assertEquals($dataToEncrypt, $decrypted);
    }

    /**
     * @test
     */
    public function itChecksWhetherDataIsDecryptable(): void
    {
        $decryptable = $this->rsa->encrypt('Some data');
        $this->assertTrue($this->rsa->canDecrypt($decryptable));

        $this->assertFalse($this->rsa->canDecrypt(null));
        $this->assertFalse($this->rsa->canDecrypt(7));
        $this->assertFalse($this->rsa->canDecrypt([]));
        $this->assertFalse($this->rsa->canDecrypt('Undecryptable'));
    }

    /**
     * @test
     */
    public function itFailsToInitOnAbsentPublicKey(): void
    {
        $rsa = new Rsa();

        $publicKeyConfig = Config::find('PASSWORD_PUBLIC_KEY');
        $publicKeyConfig->delete();

        try {
            $rsa->encrypt('Some data');
            $this->assertTrue(false, 'No exception thrown');
        } catch (ModelNotFoundException) {
            $this->assertTrue(true);
        } finally {
            $publicKeyConfig->save();
        }
    }

    /**
     * @test
     */
    public function itFailsToInitOnAbsentPrivateKey(): void
    {
        $rsa = new Rsa();

        $privateKeyConfig = Config::find('PASSWORD_PRIVATE_KEY');
        $privateKeyConfig->delete();

        try {
            $rsa->encrypt('Some data');
            $this->assertTrue(false, 'No exception thrown');
        } catch (ModelNotFoundException) {
            $this->assertTrue(true);
        } finally {
            $privateKeyConfig->save();
        }
    }

    /**
     * @test
     */
    public function itEncryptsAndDecryptsDataWithCustomKeys(): void
    {
        [$privateKey, $publicKey] = $this->createKeyPair();
        $dataToEncrypt = 'Some data';

        $encrypted = $this->rsa->encryptWith($dataToEncrypt, $publicKey);
        $this->assertIsString($encrypted);
        $this->assertNotEmpty($encrypted);
        $this->assertNotEquals($dataToEncrypt, $encrypted);

        $decrypted = $this->rsa->decryptWith($encrypted, $privateKey);
        $this->assertIsString($decrypted);
        $this->assertNotEmpty($decrypted);
        $this->assertEquals($dataToEncrypt, $decrypted);
    }
}
