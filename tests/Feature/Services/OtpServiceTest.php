<?php

namespace Tests\Feature\Services;

use App\Models\Client;
use App\Models\OTPBlackList;
use App\Models\OtpLogs;
use App\Services\Configurations;
use App\Services\OTPService as Service;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Tests\TestCase;

class OtpServiceTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected Service $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new Service();
    }

    /**
     * @test
     *
     * Tests part of unrefactored code.
     */
    public function itDoesNotBlockIfUserIsRegistered(): void
    {
        $user = Client::factory()->create();

        $this->assertNotEquals(Client::BLOCKOTP, $user->status);

        $this->service->blockEmitter(
            OtpLogs::SESSION,
            Str::random(),
            $user->id,
        );

        $this->assertNotEquals(Client::BLOCKOTP, $user->fresh()->status);
    }

    /**
     * @test
     *
     * Tests part of partially refactored code.
     */
    public function itDoesBlockUserIfItIsInOtpBlackListAndBlockDateTimeIsGreaterThanNow(): void
    {
        $record = OTPBlackList::factory()->create();
        
        $this->assertTrue($this->service->emitterInBlackList($record->emitter));
    }

    /**
     * @test
     *
     * Tests part of partially refactored code.
     */
    public function itDoesNotBlockUserIfItIsInOtpBlackListAndBlockDateTimeIsGreaterThanNow(): void
    {
        $now = Carbon::now();
        $record = OTPBlackList::factory()
            ->create([
                'block_time_start' => $now->copy()->subMinutes(6),
                'block_time_end' => $now->subMinutes(1),
            ]);
        
        $this->assertFalse($this->service->emitterInBlackList($record->emitter));
    }

    /**
     * @test
     *
     * Tests part of unrefactored code.
     */
    public function itSavesAnEmitterInOtpBlackListAndBlocksItForSomeTime(): void
    {
        $mail = fake()->userName.'@mail.com';
        $blockTime = Configurations::getInstance()->getConfigurations('OTP_TIEMPO_BLOQUEO_VALIDACION') ?: 5;
        
        $this->service->blockEmitter(OtpLogs::APIKEY, $mail, random_int(300, 400));

        $record = OTPBlackList::where('emitter', $mail)->first();
        $this->assertEquals(
            $blockTime,
            Carbon::parse($record->block_time_start)->diffInMinutes(Carbon::parse($record->block_time_end))
        );
        $this->assertEquals(1, OTPBlackList::where('emitter', $mail)->count());
    }
}
