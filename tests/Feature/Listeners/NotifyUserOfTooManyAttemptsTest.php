<?php

namespace Tests\Feature\Listeners;

use App\Events\TooManyLoginAttemptsEvent;
use App\Listeners\NotifyUserOfTooManyAttempts;
use App\Tasks\NotifyUserOfTooManyAttemptsTask;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class NotifyUserOfTooManyAttemptsTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itTriggersWithTooManyLoginAttemptsEvent(): void
    {
        Event::fake();
        Event::assertListening(
            TooManyLoginAttemptsEvent::class,
            NotifyUserOfTooManyAttempts::class,
        );
    }

    /**
     * @test
     */
    public function itCallsTheTask(): void
    {
        $nickname = Str::random();
        $event = new TooManyLoginAttemptsEvent(new Request(), $nickname);

        $task = Mockery::mock(NotifyUserOfTooManyAttemptsTask::class);
        $task
            ->shouldReceive('withNickname')
            ->once()
            ->with($nickname)
            ->andReturn($task)
            ->shouldReceive('do')
            ->once();

        $listener = new NotifyUserOfTooManyAttempts($task);
        $this->assertEmpty($listener->handle($event));
    }
}
