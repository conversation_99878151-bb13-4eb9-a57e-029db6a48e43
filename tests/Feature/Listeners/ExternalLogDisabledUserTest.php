<?php

namespace Tests\Feature\Listeners;

use App\Enums\LogApplicationCode;
use Tests\TestCase;
use App\Models\Client;
use App\Events\UserIsDisabledEvent;
use Illuminate\Support\Facades\Event;
use App\Listeners\ExternalLogDisabledUser;
use App\Services\Configurations;
use PHPUnit\Framework\MockObject\MockObject;

class ExternalLogDisabledUserTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithUserCantAccessSystemEvent(): void
    {
        Event::fake();

        Event::assertListening(
            UserIsDisabledEvent::class,
            ExternalLogDisabledUser::class,
        );
    }

    /**
     * @test
     */
    public function itCallsLogBankMethod(): void
    {
        $client = Client::factory()->create();

        $config = $this->app->make(Configurations::class);

        /**
         * @var MockObject|ExternalLogDisabledUser
         */
        $mockedListener = $this->getMockBuilder(ExternalLogDisabledUser::class)
            ->setConstructorArgs([$config])
            ->onlyMethods(['logBank'])
            ->getMock();

        $mockedListener
            ->expects($this->once())
            ->method('logBank')
            ->with(
                $client->client_id,
                LogApplicationCode::USER_DISABLED,
                $config->getConfigurations('MENSAJE_USUARIO_BLOQUEADO_ADMINISTRACION')
            );

        $event = new UserIsDisabledEvent($client);

        $mockedListener->handle($event);
    }
}
