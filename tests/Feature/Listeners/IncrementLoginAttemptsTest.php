<?php

namespace Tests\Feature\Listeners;

use App\Events\CantDecryptCredentialsOnLoginEvent;
use App\Events\TooManyLoginAttemptsEvent;
use App\Events\UserCantAccessSystemEvent;
use App\Events\WrongCredentialsEvent;
use App\Listeners\IncrementLoginAttempts;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class IncrementLoginAttemptsTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithCantDecryptCredentialsOnLoginEvent(): void
    {
        Event::fake();
        Event::assertListening(
            CantDecryptCredentialsOnLoginEvent::class,
            IncrementLoginAttempts::class,
        );
    }

    /**
     * @test
     */
    public function itTriggersWithTooManyLoginAttemptsEvent(): void
    {
        Event::fake();
        Event::assertListening(
            TooManyLoginAttemptsEvent::class,
            IncrementLoginAttempts::class,
        );
    }

    /**
     * @test
     */
    public function itTriggersWithWrongCredentialsEvent(): void
    {
        Event::fake();
        Event::assertListening(
            WrongCredentialsEvent::class,
            IncrementLoginAttempts::class,
        );
    }

    /**
     * @test
     */
    public function itTriggersWithUserCantAccessSystemEvent(): void
    {
        Event::fake();
        Event::assertListening(
            UserCantAccessSystemEvent::class,
            IncrementLoginAttempts::class,
        );
    }
}
