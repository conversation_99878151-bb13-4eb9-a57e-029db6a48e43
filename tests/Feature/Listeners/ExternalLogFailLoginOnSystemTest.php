<?php

namespace Tests\Feature\Listeners;

use Tests\TestCase;
use App\Models\Client;
use Illuminate\Http\Request;
use App\Enums\LogApplicationCode;
use App\Enums\Message\AuthMessage;
use Illuminate\Support\Facades\Event;
use App\Events\UserCantAccessSystemEvent;
use PHPUnit\Framework\MockObject\MockObject;
use App\Listeners\ExternalLogFailLoginOnSystem;

class ExternalLogFailLoginOnSystemTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithUserCantAccessSystemEvent(): void
    {
        Event::fake();

        Event::assertListening(
            UserCantAccessSystemEvent::class,
            ExternalLogFailLoginOnSystem::class,
        );
    }

    /**
     * @test
     */
    public function itCallsLogBankMethod(): void
    {
        $client = Client::factory()->create();

        /**
         * @var MockObject|ExternalLogFailLoginOnSystem
         */
        $mockedListener = $this->getMockBuilder(ExternalLogFailLoginOnSystem::class)
            ->onlyMethods(['logBank'])
            ->getMock();

        $mockedListener
            ->expects($this->once())
            ->method('logBank')
            ->with(
                $client->client_id,
                LogApplicationCode::NO_SYSTEM_ACCESS,
                AuthMessage::NOT_PERMISSIONS_SYSTEM->value
            );

        $event = new UserCantAccessSystemEvent(new Request(), $client);

        $mockedListener->handle($event);
    }
}
