<?php

namespace Tests\Feature\Listeners;

use Tests\TestCase;
use App\Models\Client;
use App\Enums\LogApplicationCode;
use App\Enums\Message\AuthMessage;
use Illuminate\Support\Facades\Event;
use App\Events\MaxDevicesReachedEvent;
use PHPUnit\Framework\MockObject\MockObject;
use App\Listeners\ExternalLogMaxDevicesReached;

class ExternalLogMaxDevicesReachedTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithMaxDevicesReachedEvent(): void
    {
        Event::fake();

        Event::assertListening(
            MaxDevicesReachedEvent::class,
            ExternalLogMaxDevicesReached::class,
        );
    }

    /**
     * @test
     */
    public function itCallsLogBankMethod(): void
    {
        $client = Client::factory()->create();

        /**
         * @var MockObject|ExternalLogMaxDevicesReached
         */
        $mockedListener = $this->getMockBuilder(ExternalLogMaxDevicesReached::class)
            ->onlyMethods(['logBank'])
            ->getMock();

        $mockedListener
            ->expects($this->once())
            ->method('logBank')
            ->with(
                $client->client_id,
                LogApplicationCode::MAX_DEVICES_REACHED,
                AuthMessage::MAXIMUM_REGISTERED_DEVICES->value
            );

        $event = new MaxDevicesReachedEvent($client);

        $mockedListener->handle($event);
    }
}
