<?php

namespace Tests\Feature\Listeners;

use App\Dtos\Coordinates;
use App\Dtos\DeviceLogin;
use App\Dtos\ExternalToken;
use App\Dtos\Login;
use App\Dtos\LoginCredentials;
use App\Events\SuccessfulLoginEvent;
use App\Events\UpdatedTokenForUserEvent;
use App\Listeners\SetNewTokenForUser;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Tests\TestCase;

class SetNewTokenForUserTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithSuccessfulLoginEvent(): void
    {
        Event::fake();
        Event::assertListening(
            SuccessfulLoginEvent::class,
            SetNewTokenForUser::class,
        );
    }

    /**
     * @test
     */
    public function itTriggersWithUpdatedTokenForUserEvent(): void
    {
        Event::fake();
        Event::assertListening(
            UpdatedTokenForUserEvent::class,
            SetNewTokenForUser::class,
        );
    }

    /**
     * @test
     */
    public function itUpdatesTheBaesTokenOfAUser(): void
    {
        // Set up.
        $user = Client::factory()
            ->withBaesToken()
            ->create();

        $token = ExternalToken::make(Str::random(), Str::random());

        $dummyString = Str::random();
        $loginCredentials = LoginCredentials::make($dummyString, $dummyString);
        $request = Request::create('/', 'GET');
        $coordinates = Coordinates::make($dummyString, $dummyString);
        $deviceLogin = DeviceLogin::make(
            $dummyString,
            $dummyString,
            $dummyString,
            $coordinates,
            false,
        );
        $login = Login::make($loginCredentials, $request, $deviceLogin);

        $event = new SuccessfulLoginEvent($user, $login, $token);

        // Preassert.
        $this->assertNotEquals($token->token, $user->baes_token);
        $this->assertNotEquals($token->refreshToken, $user->baes_refresh_token);

        // Run.
        App::make(SetNewTokenForUser::class)->handle($event);

        // Assert.
        $user->refresh();
        $this->assertEquals($token->token, $user->baes_token);
        $this->assertEquals($token->refreshToken, $user->baes_refresh_token);
    }
}
