<?php

namespace Tests\Feature\Listeners;

use App\Events\PhoneUpdatedEvent;
use App\Jobs\NotifyUserOfPhoneUpdateJob;
use App\Listeners\NotifyUserOfPhoneUpdate;
use App\Models\Client;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class NotifyUserOfPhoneUpdateTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithPhoneUpdatedEvent(): void
    {
        Event::fake();
        Event::assertListening(
            PhoneUpdatedEvent::class,
            NotifyUserOfPhoneUpdate::class,
        );
    }

    /**
     * @test
     */
    public function itTriggersNotifyUserOfPhoneUpdateJob(): void
    {
        $listener = new NotifyUserOfPhoneUpdate();

        Bus::fake();

        $listener->handle(new PhoneUpdatedEvent(Client::factory()->make()));

        Bus::assertDispatched(NotifyUserOfPhoneUpdateJob::class);
    }
}
