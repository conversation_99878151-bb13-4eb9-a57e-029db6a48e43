<?php

namespace Tests\Feature\Listeners;

use App\Dtos\ContactChannels;
use App\Dtos\Customer;
use App\Events\NewUserRegistrationRequestEvent;
use Tests\TestCase;
use Illuminate\Support\Facades\Queue;
use App\Jobs\ProcessUserRegistrationAsynchronously;
use Tests\Support\Providers\CustomerProvider;

class HandleUserRegistrationTest extends TestCase
{
    public function testItCallsProcessUserRegistrationJob(): void
    {
        Queue::fake();

        $customer = Customer::fromArray(CustomerProvider::getValidCustomerDTOArray());

        $contactChannels = new ContactChannels();

        $creditcardApplicationId = random_int(1000, 9999);

        $handleUserRegistration = new \App\Listeners\HandleUserRegistration();

        $handleUserRegistration->handle(
            new NewUserRegistrationRequestEvent(
                $creditcardApplicationId,
                $customer,
                $contactChannels
            )
        );

        Queue::assertPushed(
            ProcessUserRegistrationAsynchronously::class,
            function ($job) use ($creditcardApplicationId, $customer) {
                return $job->customer->id === $customer->id && $job->creditcardApplicationId === $creditcardApplicationId;
            }
        );
    }
}
