<?php

namespace Tests\Feature\Listeners;

use App\Events\EmailUpdatedEvent;
use App\Jobs\NotifyUserOfEmailUpdateJob;
use App\Listeners\NotifyUserOfEmailUpdate;
use App\Models\Client;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class NotifyUserOfEmailUpdateTest extends TestCase
{
    /**
     * @test
     */
    public function itTriggersWithEmailUpdatedEvent(): void
    {
        Event::fake();
        Event::assertListening(
            EmailUpdatedEvent::class,
            NotifyUserOfEmailUpdate::class,
        );
    }

    /**
     * @test
     */
    public function itTriggersNotifyUserOfEmailUpdateJob(): void
    {
        $listener = new NotifyUserOfEmailUpdate();

        Bus::fake();

        $listener->handle(new EmailUpdatedEvent(Client::factory()->make()));

        Bus::assertDispatched(NotifyUserOfEmailUpdateJob::class);
    }
}
