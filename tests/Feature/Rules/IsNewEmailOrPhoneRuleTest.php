<?php

namespace Tests\Unit\Rules;

use App\Rules\IsNewEmailOrPhoneRule;
use App\Tasks\CheckEmailOrPhoneIsNewTask;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\Support\Traits\HasValidationRuleClosure;
use Tests\TestCase;

class IsNewEmailOrPhoneRuleTest extends TestCase
{
    use HasValidationRuleClosure;

    protected mixed $mockedTask;

    protected string $emailOrPhone = '<EMAIL>';

    protected IsNewEmailOrPhoneRule $rule;


    public function setUp(): void
    {
        parent::setUp();

        /** @var CheckEmailOrPhoneIsNewTask|MockInterface */
        $this->mockedTask = $this->mock(CheckEmailOrPhoneIsNewTask::class);

        $this->mockedTask
            ->shouldReceive('withEmailOrPhone')
            ->once()
            ->with($this->emailOrPhone)
            ->andReturn($this->mockedTask);

        $this->rule = new IsNewEmailOrPhoneRule($this->mockedTask);
    }

    #[Test]
    public function it_passes_if_phone_or_email_exists_on_external_service(): void
    {
        $this->mockedTask
            ->shouldReceive('do')
            ->once()
            ->andReturn(true);

        $this->rule->validate('emailOrPhone', $this->emailOrPhone, $this->getFailedValidationClosure());

        $this->assertNull($this->failedMessage);
    }

    #[Test]
    public function fails_if_phone_or_email_are_not_found_on_external_service(): void
    {
        $this->mockedTask
            ->shouldReceive('do')
            ->once()
            ->andReturn(false);

        $this->rule->validate('emailOrPhone', $this->emailOrPhone, $this->getFailedValidationClosure());

        $this->assertEquals(trans('rules.is_new_contact'), $this->failedMessage);
    }
}
