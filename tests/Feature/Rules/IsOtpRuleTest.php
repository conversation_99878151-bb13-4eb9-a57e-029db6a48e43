<?php

namespace Tests\Feature\Rules;

use App\Rules\IsOtpRule;
use App\Tasks\ValidateOtpTask;
use <PERSON><PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\Support\Traits\HasValidationRuleClosure;
use Tests\TestCase;

class IsOtpRuleTest extends TestCase
{
    use HasValidationRuleClosure;

    private ValidateOtpTask|MockInterface $validateOTPTask;

    private IsOtpRule $rule;

    public function setUp(): void
    {
        parent::setUp();

        /** @var ValidateOtpTask|MockInterface */
        $this->validateOTPTask = $this->mock(ValidateOtpTask::class);

        $this->validateOTPTask
            ->shouldReceive('withOtpData')
            ->withAnyArgs()
            ->andReturn($this->validateOTPTask);

        $this->rule = new IsOtpRule($this->validateOTPTask, 'valid_shared_key');
    }

    #[Test]
    public function fails_if_shared_key_is_empty(): void
    {
        $rule = new IsOtpRule($this->validateOTPTask, null);

        $rule->validate('OTPCode', 'fake_otp_code', $this->getFailedValidationClosure());

        $this->assertEquals(trans('rules.is_otp'), $this->failedMessage);
    }

    #[Test]
    public function passes_if_shared_key_is_not_empty_and_otp_is_valid(): void
    {
        $this->validateOTPTask
            ->shouldReceive('do')
            ->once()
            ->andReturn(true);

        $this->rule->validate('OTPCode', 'valid_otp_code', $this->getFailedValidationClosure());

        $this->assertNull($this->failedMessage);
    }

    #[Test]
    public function failes_if_shared_key_is_not_empty_but_otp_code_is_invalid(): void
    {
        $this->validateOTPTask
            ->shouldReceive('do')
            ->once()
            ->andReturn(false);

        $this->rule->validate('OTPCode', 'valid_otp_code', $this->getFailedValidationClosure());

        $this->assertEquals(trans('rules.is_otp'), $this->failedMessage);
    }
}
