<?php

namespace Tests\Unit\Rules;

use App\Rules\IsLongitudeRule;
use PHPUnit\Framework\Attributes\Test;
use Tests\Support\Traits\HasValidationRuleClosure;
use Tests\TestCase;

class IsLongitudeRuleTest extends TestCase
{
    use HasValidationRuleClosure;

    protected IsLongitudeRule $rule;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rule = new IsLongitudeRule();
    }

    #[Test]
    public function passes_on_valid_longitudes(): void
    {
        $longitudes = ['0', '-0', '-123', '123', 12.435798, '-12.435798'];

        foreach ($longitudes as $longitude) {
            $this->rule->validate('longitude', $longitude, $this->getFailedValidationClosure());

            $this->assertNull($this->failedMessage);
        }
    }

    #[Test]
    public function passes_on_null_values_if_specified_on_the_constructor(): void
    {
        $rule = new IsLongitudeRule(true);

        $rule->validate('longitude', null, $this->getFailedValidationClosure());

        $this->assertNull($this->failedMessage);
    }

    #[Test]
    public function returns_fail_message_on_invalid_longitudes(): void
    {
        $longitudes = ['00016297-5', [], '200', '-200', '@120', null];

        foreach ($longitudes as $longitude) {
            $this->rule->validate('longitude', $longitude, $this->getFailedValidationClosure());

            $this->assertEquals(trans('rules.is_longitude'), $this->failedMessage);
        }
    }
}
