<?php

namespace Tests\Unit\Rules;

use Tests\TestCase;
use App\Rules\IsLatitudeRule;
use PHPUnit\Framework\Attributes\Test;
use Tests\Support\Traits\HasValidationRuleClosure;

class IsLatitudeRuleTest extends TestCase
{
    use HasValidationRuleClosure;

    protected IsLatitudeRule $rule;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rule = new IsLatitudeRule();
    }

    #[Test]
    public function passes_on_valid_latitudes(): void
    {
        $latitudes = ['0', '-0', '-85', '85', 12.435798, '-12.435798'];

        foreach ($latitudes as $latitude) {
            $this->rule->validate('latitute', $latitude, $this->getFailedValidationClosure());

            $this->assertNull($this->failedMessage);
        }
    }

    #[Test]
    public function passes_on_null_values_if_specified_on_constructor(): void
    {
        $rule = new IsLatitudeRule(true);

        $rule->validate('latitute', null, $this->getFailedValidationClosure());

        $this->assertNull($this->failedMessage);
    }

    #[Test]
    public function returns_fail_message_on_invalid_latitudes(): void
    {
        $latitudes = ['00016297-5', [], '200', '-200', '@120', 99, -99, null];

        foreach ($latitudes as $latitude) {
            $this->rule->validate('latitute', $latitude, $this->getFailedValidationClosure());
            $this->assertEquals(trans('rules.is_latitude'), $this->failedMessage);
        }
    }
}
