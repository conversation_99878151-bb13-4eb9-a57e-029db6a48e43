<?php

namespace Tests\Feature\Rules;

use App\Rules\IsDuiRule;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\Support\Traits\HasValidationRuleClosure;
use Tests\TestCase;

class IsDuiRuleTest extends TestCase
{
    use HasValidationRuleClosure;

    protected IsDuiRule $rule;

    public function setUp(): void
    {
        parent::setUp();

        $this->rule = app()->make(IsDuiRule::class);
    }

    #[Test]
    public function passes_if_dui_is_valid(): void
    {
        $dui = '00016297-5';

        $this->rule->validate('dui', $dui, $this->getFailedValidationClosure());

        $this->assertNull($this->failedMessage);
    }

    #[Test]
    #[TestWith([123])] // Invalid type
    #[TestWith(['00000000-0'])] // Blacklisted dui
    #[TestWith(['1234-1234-0'])] // Invalid structure
    #[TestWith(['123456789-0'])] // Invalid section before dash
    #[TestWith(['12345678-12'])] //Invalid section after dash
    #[TestWith(['ABCDEFGH-I'])] // Invalid characters
    #[TestWith(['00016297-9'])] // Invalid last digit
    public function fails_on_invalid_duis(mixed $dui): void
    {
        $this->rule->validate('dui', $dui, $this->getFailedValidationClosure());

        $this->assertEquals(trans('rules.is_dui'), $this->failedMessage);
    }
}
