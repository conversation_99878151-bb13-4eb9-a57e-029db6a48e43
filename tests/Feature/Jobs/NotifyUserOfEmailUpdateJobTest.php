<?php

namespace Tests\Feature\Jobs;

use App\Factory\ClientFactory;
use App\Jobs\NotifyUserOfEmailUpdateJob;
use App\Models\Client;
use App\Models\NotificationHistories;
use Illuminate\Http\Request;
use Illuminate\Log\LogManager as Log;
use Mockery;
use Symfony\Component\HttpFoundation\HeaderBag;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class NotifyUserOfEmailUpdateJobTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itSendsAnEmail(): void
    {
        $user = Client::factory()->create();
        $http = Mockery::mock(ClientFactory::class);
        $http
            ->shouldReceive('customeResponse')
            ->once();
        $headers = Mockery::mock(HeaderBag::class);
        $headers
            ->shouldReceive('all')
            ->once()
            ->andReturn([]);
        $request = Mockery::mock(Request::class);
        $request
            ->shouldReceive('ip')
            ->once()
            ->andReturn('');
        $request->headers = $headers;

        $job = new NotifyUserOfEmailUpdateJob($user);
        $this->assertNull($job->handle($http, $request, app(Log::class)));
    }

    /**
     * @test
     */
    public function itLogsToDb(): void
    {
        $user = Client::factory()->create();
        $http = Mockery::mock(ClientFactory::class);
        $http->shouldReceive('customeResponse');
        $headers = Mockery::mock(HeaderBag::class);
        $headers
            ->shouldReceive('all')
            ->andReturn([]);
        $request = Mockery::mock(Request::class);
        $request
            ->shouldReceive('ip')
            ->andReturn('');
        $request->headers = $headers;

        (new NotifyUserOfEmailUpdateJob($user))->handle($http, $request, app(Log::class));

        $this->assertNotNull(NotificationHistories::firstWhere('client_id', $user->id));
    }
}
