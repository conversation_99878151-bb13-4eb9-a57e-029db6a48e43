<?php

namespace Tests\Feature\Jobs;

use Tests\TestCase;
use App\Dtos\Customer;
use App\Dtos\ContactChannels;
use App\Jobs\ProcessUserRegistrationAsynchronously;
use Tests\Support\Providers\CustomerProvider;

class ProcessUserRegistrationAsynchronouslyTest extends TestCase
{
    public function testItRegisterANewUser(): void
    {
        $customer = Customer::fromArray(CustomerProvider::getValidCustomerDTOArray());

        $contactChannels = new ContactChannels();

        $creditcardApplicationId = random_int(1000, 9999);

        $this->assertDatabaseMissing('clients', ['dui' => $customer->dui]);

        ProcessUserRegistrationAsynchronously::dispatch($creditcardApplicationId, $customer, $contactChannels);

        $this->assertDatabaseHas('clients', ['dui' => $customer->dui]);
    }
}
