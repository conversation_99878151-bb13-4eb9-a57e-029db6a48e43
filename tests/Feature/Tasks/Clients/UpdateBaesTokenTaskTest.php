<?php

namespace Tests\Feature\Tasks\Clients;

use App\Dtos\ExternalToken;
use App\Models\Client;
use App\Tasks\Clients\UpdateBaesTokenTask;
use Illuminate\Support\Str;
use Tests\TestCase;

class UpdateBaesTokenTaskTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected UpdateBaesTokenTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new UpdateBaesTokenTask();
    }

    /**
     * @test
     */
    public function itUpdatesBaesTokenOfUser(): void
    {
        $user = Client::factory()->create();
        $token = ExternalToken::make(Str::random(), Str::random());

        $this
            ->task
            ->withUser($user)
            ->withToken($token)
            ->do();

        $user->refresh();
        $this->assertEquals($token->token, $user->baes_token);
        $this->assertEquals($token->refreshToken, $user->baes_refresh_token);
    }
}
