<?php

namespace Tests\Feature\Tasks\Clients;

use App\Dtos\ExternalToken;
use App\Events\UpdatedTokenForUserEvent;
use App\Models\Client;
use App\Repos\RefreshTokenRepo;
use App\Tasks\Clients\RefreshTokenForUserTask;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class RefreshTokenForUserTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itRefreshesAToken(): void
    {
        $user = Client::factory()
            ->withBaesToken()
            ->create();
        $deviceId = 'some_device';
        $token = ExternalToken::factory()->create();

        $repo = Mockery::mock(RefreshTokenRepo::class);
        $repo
            ->shouldReceive('prepare')
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($token);

        $task = new RefreshTokenForUserTask($repo);

        Event::fake();

        $result = $task
            ->withUser($user)
            ->withDeviceId($deviceId)
            ->do();

        $this->assertSame($token, $result);
        Event::assertDispatched(UpdatedTokenForUserEvent::class);
    }
}
