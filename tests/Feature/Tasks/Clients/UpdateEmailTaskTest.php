<?php

namespace Tests\Feature\Tasks\Clients;

use App\Models\Client;
use App\Tasks\Clients\UpdateEmailTask;
use Tests\TestCase;

class UpdateEmailTaskTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected UpdateEmailTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new UpdateEmailTask();
    }

    /**
     * @test
     */
    public function itUpdatesEmailOfUser(): void
    {
        $user = Client::factory()->create();
        $newEmail = '<EMAIL>';

        $this
            ->task
            ->withUser($user)
            ->withEmail($newEmail)
            ->do();

        $user->refresh();
        $this->assertEquals($newEmail, $user->email);
    }
}
