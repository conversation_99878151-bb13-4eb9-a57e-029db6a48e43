<?php

namespace Tests\Feature\Tasks\Clients;

use App\Models\Client;
use App\Tasks\Clients\UpdatePhoneTask;
use Tests\TestCase;

class UpdatePhoneTaskTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected UpdatePhoneTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new UpdatePhoneTask();
    }

    /**
     * @test
     */
    public function itUpdatesPhoneNumberOfUser(): void
    {
        $user = Client::factory()->create();
        $newPhone = (string) random_int(70000000, 79999999);

        $this
            ->task
            ->withUser($user)
            ->withPhoneNumber($newPhone)
            ->do();

        $user->refresh();
        $this->assertEquals($newPhone, $user->phone_number);
    }
}
