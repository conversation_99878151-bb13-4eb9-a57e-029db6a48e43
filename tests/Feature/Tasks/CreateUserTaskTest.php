<?php

namespace Tests\Feature\Tasks;

use App\Dtos\ContactChannels;
use App\Dtos\Customer;
use App\Dtos\Email;
use App\Dtos\Phone;
use App\Events\UserRegisteredEvent;
use App\Models\Systems;
use App\Tasks\CreateUserTask;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Tests\TestCase;

class CreateUserTaskTest extends TestCase
{
    use WithFaker;

    /**
     * Subject to test.
     */
    protected CreateUserTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = App::make(CreateUserTask::class);
    }

    /**
     * @test
     */
    public function itCreatesANewUser(): void
    {
        $creditCardApplicationId = random_int(5000, 5999);
        $contactChannels = ContactChannels::make(
            Email::make($this->faker->email()),
            Phone::make((string) $this->faker->randomNumber(8, true)),
        );
        $customer = new Customer(
            id: random_int(100, 999),
            firstName: Str::random(),
            secondName: Str::random(),
            firstSurname: Str::random(),
            secondSurname: Str::random(),
            marriedName: Str::random(),
            birthDate: Str::random(),
            dui: '12345678',
            nit: Str::random(),
        );

        Event::fake();

        $result = $this
            ->task
            ->withCustomer($customer)
            ->withContactChannels($contactChannels)
            ->withCreditCardApplicationId($creditCardApplicationId)
            ->do();

        $this->assertEquals($customer->id, $result->client_id);
        $this->assertModelExists($result);
        $this->assertDatabaseHas('clients_systems', [
            'client_id' => $result->id,
            'system_id' => Systems::default()->id,
        ]);

        if (!$result->homologated) {
            Event::assertDispatched(UserRegisteredEvent::class);
        }
    }
}
