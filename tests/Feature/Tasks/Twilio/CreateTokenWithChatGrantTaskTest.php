<?php

namespace Tests\Feature\Tasks\Twilio;

use App\Tasks\Twilio\CreateTokenWithChatGrantTask;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;
use Twilio\Jwt\AccessToken;
use Twilio\Jwt\Grants\ChatGrant;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class CreateTokenWithChatGrantTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     * @runTestsInSeparateProcesses
     * @preserveGlobalState disabled
     * @see Mocking Hard Dependencies (new Keyword) http://docs.mockery.io/en/latest/cookbook/mocking_hard_dependencies.html
     */
    public function itCreatesAToken(): void
    {
        $identity = Str::random(10);
        $conversation = Mockery::mock(TwilioConversation::class);
        $conversation->chatServiceSid = Str::random();

        $chatGrant = Mockery::mock(ChatGrant::class);
        $chatGrant
            ->shouldReceive('setServiceSid')
            ->with($conversation->chatServiceSid)
            ->once();

        $accessToken = Mockery::mock('overload:' . AccessToken::class);
        $accessToken
            ->shouldReceive('addGrant')
            ->with($chatGrant)
            ->once()
            ->andReturn($accessToken);

        $task = App::makeWith(CreateTokenWithChatGrantTask::class, ['chatGrant' => $chatGrant]);
        $result = $task
            ->withConversation($conversation)
            ->withIdentity($identity)
            ->do();

        $this->assertInstanceOf(AccessToken::class, $result);
    }
}
