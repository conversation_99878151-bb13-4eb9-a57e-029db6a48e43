<?php

namespace Tests\Feature\Tasks;

use App\Models\Device;
use App\Tasks\OfflineDevicesTask;
use ArgumentCountError;
use Tests\TestCase;

class OfflineDevicesTaskTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected OfflineDevicesTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new OfflineDevicesTask();
    }

    /**
     * @test
     */
    public function itOfflinesEveryDeviceExceptOne(): void
    {
        $deviceA = Device::factory()
            ->online()
            ->create();
        $deviceB = Device::factory()
            ->offline()
            ->create(['client_id' => $deviceA->client->id]);

        $this->assertEquals(Device::ONLINE, $deviceA->online);
        $this->assertEquals(Device::OFFLINE, $deviceB->online);

        $this
            ->task
            ->withDevice($deviceB)
            ->do();

        $this->assertEquals(Device::OFFLINE, $deviceA->fresh()->online);
        $this->assertEquals(Device::ONLINE, $deviceB->fresh()->online);
    }

    /**
     * @test
     */
    public function itDisablesDevicesWithSameDeviceIdForOtherUsers(): void
    {
        $device = Device::factory()->create([
            'active' => true,
            'online' => Device::ONLINE,
        ]);

        $duplicatedDevice = Device::factory()->create([
            'device_id' => $device->device_id,
            'active' => true,
            'online' => Device::ONLINE,
        ]);

        $this->assertTrue((bool) $device->active);
        $this->assertEquals(Device::ONLINE, $device->online);
        $this->assertTrue((bool) $duplicatedDevice->active);
        $this->assertEquals(Device::ONLINE, $duplicatedDevice->online);

        $this->task->withDevice($device)->do();

        $device->refresh();
        $duplicatedDevice->refresh();

        $this->assertTrue((bool) $device->active);
        $this->assertEquals(Device::ONLINE, $device->online);
        $this->assertFalse((bool) $duplicatedDevice->active);
        $this->assertEquals(Device::OFFLINE, $duplicatedDevice->online);
    }

    /**
     * @test
     */
    public function itFailsWhenNoDeviceIsSet(): void
    {
        $this->expectException(ArgumentCountError::class);
        $this->task->do();
    }
}
