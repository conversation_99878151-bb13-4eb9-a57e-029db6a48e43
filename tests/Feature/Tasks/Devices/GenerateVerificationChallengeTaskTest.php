<?php

namespace Tests\Feature\Tasks\Devices;

use App\Exceptions\MissingPublicKeyException;
use App\Models\Device;
use App\Services\Crypt\Rsa;
use App\Tasks\Devices\GenerateVerificationChallengeTask;
use ArgumentCountError;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\CreatesRsaKeys;

class GenerateVerificationChallengeTaskTest extends TestCase
{
    use CreatesRsaKeys;

    /**
     * Subject to test.
     */
    protected GenerateVerificationChallengeTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = app(GenerateVerificationChallengeTask::class);
    }

    /**
     * @test
     */
    public function itGeneratesNewChallengeAndUpdatesDevice(): void
    {
        [$privateKey, $publicKey] = $this->createKeyPair();
        $firstChallenge = Str::random(64);
        $device = Device::factory()
            ->create([
                'verification_challenge' => $firstChallenge,
                'biometric_public_key' => $publicKey,
            ]);

        $encryptedChallenge = $this
            ->task
            ->withDevice($device)
            ->do()
            ->verificationChallenge;

        $secondChallenge = $device->fresh()->verification_challenge;
        $this->assertNotEquals($firstChallenge, $secondChallenge);

        $decryptedChallenge = app(Rsa::class)->decryptWith($encryptedChallenge, $privateKey);
        $this->assertEquals($secondChallenge, $decryptedChallenge);
    }

    /**
     * @test
     */
    public function itFailsWhenNoDeviceIsSet(): void
    {
        $this->expectException(ArgumentCountError::class);
        $this->task->do();
    }

    /**
     * @test
     */
    public function itFailsWhenDeviceHasMissingPublicKey(): void
    {
        $device = Device::factory()
            ->active()
            ->emptyPublicKey()
            ->create();
        $this->task->withId($device->device_id);

        $this->expectException(MissingPublicKeyException::class);
        $this->task->do();
    }
}
