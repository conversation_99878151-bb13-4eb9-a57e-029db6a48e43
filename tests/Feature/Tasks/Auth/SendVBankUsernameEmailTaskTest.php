<?php

namespace Tests\Feature\Tasks\Auth;

use Tests\TestCase;
use App\Enums\Code\Status;
use App\Exceptions\Auth\ClientNotFoundException;
use Mo<PERSON>y\MockInterface;
use App\Repos\SendVBankUsernameEmailRepo;
use PHPUnit\Framework\Attributes\Test;
use App\Tasks\Auth\SendVBankUsernameEmailTask;
use Symfony\Component\HttpFoundation\Response;

class SendVBankUsernameEmailTaskTest extends TestCase
{
    private const FAKE_CUSTOMER_ID = 'fake_customer_id';

    private const FAKE_DEVICE_ID = 'fake_device_id';

    private SendVBankUsernameEmailTask $task;

    private MockInterface|SendVBankUsernameEmailRepo $mockedRepo;

    public function setUp(): void
    {
        parent::setUp();

        /** @var MockInterface|SendVBankUsernameEmailRepo */
        $this->mockedRepo = $this->mock(SendVBankUsernameEmailRepo::class);

        $this->mockedRepo
            ->shouldReceive('prepare')
            ->withAnyArgs()
            ->once()
            ->andReturnSelf();

        $this->task = new SendVBankUsernameEmailTask($this->mockedRepo);
    }

    #[Test]
    public function sends_vbank_username_recovery_email(): void
    {
        $validResponse = [
            'Data' => true,
            'Status' => [
                'ResponseStatus' => [
                    'Code' => Status::codeExternalFind->value,
                ]
            ]
        ];

        $this->mockedRepo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($validResponse);

        $this->task
            ->withCustomerId(self::FAKE_CUSTOMER_ID)
            ->withDeviceId(self::FAKE_DEVICE_ID)
            ->do();
    }

    #[Test]
    public function throws_client_not_found_exception_if_response_code_is_404(): void
    {
        $responseData = [
            'Data' => true,
            'Status' => [
                'ResponseStatus' => [
                    'Code' => Response::HTTP_NOT_FOUND,
                ]
            ]
        ];

        $this->mockedRepo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($responseData);

        $this->expectException(ClientNotFoundException::class);

        $this->task
            ->withCustomerId(self::FAKE_CUSTOMER_ID)
            ->withDeviceId(self::FAKE_DEVICE_ID)
            ->do();
    }
}
