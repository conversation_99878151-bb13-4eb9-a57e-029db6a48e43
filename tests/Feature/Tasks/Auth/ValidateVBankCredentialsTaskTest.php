<?php

namespace Tests\Feature\Tasks\Auth;

use App\Dtos\LoginCredentials;
use Tests\TestCase;
use <PERSON><PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use App\Repos\ValidateVBankCredentialsRepo;
use Symfony\Component\HttpFoundation\Response;
use App\Tasks\Auth\ValidateVBankCredentialsTask;

class ValidateVBankCredentialsTaskTest extends TestCase
{
    private ValidateVBankCredentialsTask $task;

    private MockInterface|ValidateVBankCredentialsRepo $mockedRepo;

    public function setUp(): void
    {
        parent::setUp();

        /** @var MockInterface|ValidateVBankCredentialsRepo */
        $this->mockedRepo = $this->mock(ValidateVBankCredentialsRepo::class);

        $this->mockedRepo
            ->shouldReceive('prepare')
            ->withAnyArgs()
            ->andReturn($this->mockedRepo);

        $this->task = new ValidateVBankCredentialsTask($this->mockedRepo);
    }

    #[Test]
    public function validates_if_vbank_credentials_are_correct(): void
    {
        $validResponse = [
            'Data' => [
                'RequirePasswordReset' => false
            ],
            'Status' => [
                'RequestStatus' => [
                    'Code' => Response::HTTP_OK,
                    'Message' => 'Ok'
                ],
                'ResponseStatus' => [
                    'Code' => Response::HTTP_OK,
                    'Message' => 'Inicio de sesión exitosa'
                ]
            ]
        ];

        $this->mockedRepo
            ->shouldReceive('fetchRaw')
            ->once()
            ->andReturn($validResponse);

        $credentials = new LoginCredentials('vbank_nickname', 'vbank_password');

        $result = $this->task
            ->withCredentials($credentials)
            ->do();

        $this->assertEquals($validResponse, $result);
    }
}
