<?php

namespace Tests\Feature\Tasks\Auth;

use App\Exceptions\Auth\CouldNotCreateVBankUser;
use Tests\TestCase;
use Mockery\MockInterface;
use App\Repos\CreateVBankUserRepo;
use App\Tasks\Auth\CreateVBankUserTask;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;

class CreateVBankUserTaskTest extends TestCase
{
    private const FAKE_CUSTOMER_ID = 'fake_customer_id';

    private const FAKE_DEVICE_ID = 'fake_device_id';

    private const FAKE_USERNAME = 'fake_username';

    private MockInterface|CreateVBankUserRepo $createVBankUserRepo;

    private CreateVBankUserTask $createVBankUserTask;

    public function setUp(): void
    {
        parent::setUp();

        $createVBankUserRepoParams = [
            'customerId' => self::FAKE_CUSTOMER_ID,
            'deviceId' => self::FAKE_DEVICE_ID,
        ];

        /** @var MockInterface|CreateVBankUserRepo */
        $this->createVBankUserRepo = $this->mock(CreateVBankUserRepo::class);
        $this->createVBankUserRepo
            ->shouldReceive('prepare')
            ->with($createVBankUserRepoParams)
            ->once()
            ->andReturnSelf();

        $this->createVBankUserTask = new CreateVBankUserTask($this->createVBankUserRepo);
    }

    #[Test]
    public function creates_vbank_user_account_through_repo(): void
    {
        $validResponse = [
            'Data' => [
                'CustomerId' => self::FAKE_CUSTOMER_ID,
                'Username' => self::FAKE_USERNAME,
            ],
            'Status' => [
                'ReponseStatus' => [
                    'Code' =>  Response::HTTP_OK,
                ],
            ],
        ];

        $this->createVBankUserRepo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($validResponse);

        $vBankUsername = $this->createVBankUserTask
            ->withCustomerId(self::FAKE_CUSTOMER_ID)
            ->withDeviceId(self::FAKE_DEVICE_ID)
            ->do();

        $this->assertEquals(self::FAKE_USERNAME, $vBankUsername);
    }

    #[Test]
    public function throws_exception_if_cannot_process_response_from_repo(): void
    {
        $invalidResponse = [
            'Data' => null,
            'Status' => [
                'ReponseStatus' => [
                    'Code' =>  Response::HTTP_BAD_REQUEST,
                ],
            ],
        ];

        $this->createVBankUserRepo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($invalidResponse);

        $this->expectException(CouldNotCreateVBankUser::class);

        $this->createVBankUserTask
            ->withCustomerId(self::FAKE_CUSTOMER_ID)
            ->withDeviceId(self::FAKE_DEVICE_ID)
            ->do();
    }
}
