<?php

namespace Tests\Feature\Tasks\Auth;

use App\Dtos\CreditCardApplication;
use App\Models\Client;
use App\Repos\GetCustomerCardApplicationsRepo;
use App\Tasks\Auth\GetCurrentCustomerIdTask;
use Illuminate\Support\Facades\App;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class GetCurrentCustomerIdTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * Subject to test.
     */
    protected GetCurrentCustomerIdTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = App::make(GetCurrentCustomerIdTask::class);
    }

    /**
     * @test
     */
    public function itReturnsCustomerIdOfAuthenticatedUser(): void
    {
        $user = Client::factory()->create();
        $this->actingAs($user, 'api');

        $customerId = $this
            ->task
            ->do();

        $this->assertEquals($user->client_id, $customerId);
    }

    /**
     * @test
     */
    public function itReturnsCustomerIdOfBiometryUser(): void
    {
        $creditCardApplication = CreditCardApplication::factory()->create();

        $repo = Mockery::mock(GetCustomerCardApplicationsRepo::class)->makePartial();
        $repo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($creditCardApplication);

        $result = App::makeWith(GetCurrentCustomerIdTask::class, ['repo' => $repo])
            ->withDui('some dui')
            ->do();

        $this->assertEquals($creditCardApplication->customer->id, $result);
    }

    /**
     * @test
     */
    public function itReturnsNullWhenNoUser(): void
    {
        $this->assertNull($this->task->do());
        $this->assertNull($this->task->withDui(null)->do());
        $this->assertNull($this->task->withDui('')->do());
    }
}
