<?php

namespace Tests\Feature\Tasks\Auth;

use App\Models\Device;
use App\Services\Crypt\Rsa;
use App\Tasks\Auth\CheckSignedChallengeTask;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\CreatesRsaKeys;

class CheckSignedChallengeTaskTest extends TestCase
{
    use CreatesRsaKeys;

    /**
     * Subject to test.
     */
    protected CheckSignedChallengeTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = App::make(CheckSignedChallengeTask::class);
    }

    /**
     * @test
     */
    public function itSuccessfullyVerifiesADevice(): void
    {
        [$privateKey, $publicKey] = $this->createKeyPair();
        $challenge = Str::random();
        $device = Device::factory()->create([
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $challenge,
        ]);
        $signature = Rsa::signWith($challenge, $privateKey);

        $result = $this
            ->task
            ->withDevice($device)
            ->withSignedChallenge($signature)
            ->do();

        $this->assertTrue($result);
    }

    /**
     * @test
     */
    public function itFailsToVerifyADevice(): void
    {
        [$privateKey, $publicKey] = $this->createKeyPair();
        $device = Device::factory()->create([
            'biometric_public_key' => $publicKey,
        ]);
        $signature = Rsa::signWith(Str::random(), $privateKey);

        $result = $this
            ->task
            ->withDevice($device)
            ->withSignedChallenge($signature)
            ->do();

        $this->assertFalse($result);
    }
}
