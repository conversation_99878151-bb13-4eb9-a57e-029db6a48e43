<?php

namespace Tests\Feature\Tasks\Auth;

use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use App\Repos\CallFaceAuthenticationExtRepo;
use App\Tasks\Auth\CallFaceAuthenticationExtTask;

class CallFaceAuthenticationExtTaskTest extends TestCase
{
    #[Test]
    public function uses_faceauthenticationext_repo_instead_of_plain_faceauthentication_repo(): void
    {
        $task = app()->make(CallFaceAuthenticationExtTask::class);

        $taskReflection = new \ReflectionClass($task);

        $taskRepoProperty = $taskReflection->getProperty('repo');

        $taskRepoProperty->setAccessible(true);

        $taskRepo = $taskRepoProperty->getValue($task);

        $this->assertInstanceOf(CallFaceAuthenticationExtRepo::class, $taskRepo);
    }
}
