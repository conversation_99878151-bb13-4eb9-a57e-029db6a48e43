<?php

namespace Tests\Feature\Tasks\Auth;

use Tests\TestCase;
use App\Models\Client;
use <PERSON><PERSON>y\MockInterface;
use Illuminate\Support\Str;
use App\DTO\FaceAuthRequestDTO;
use PHPUnit\Framework\Attributes\Test;
use App\Repos\CallFaceAuthenticationRepo;
use App\Tasks\Auth\CallFaceAuthenticationTask;

class CallFaceAuthenticationTaskTest extends TestCase
{
    private MockInterface|CallFaceAuthenticationRepo $mockedRepo;

    private CallFaceAuthenticationTask $task;

    public function setUp(): void
    {
        parent::setUp();

        /** @var MockInterface|CallFaceAuthenticationRepo */
        $this->mockedRepo = $this->mock(CallFaceAuthenticationRepo::class);

        $this->mockedRepo
            ->shouldReceive('prepare')
            ->withAnyArgs()
            ->andReturn($this->mockedRepo);

        $this->task = new CallFaceAuthenticationTask($this->mockedRepo);
    }

    #[Test]
    public function calls_faceauthentication_repo(): void
    {
        $client = Client::factory()->create();

        $this->mockedRepo
            ->shouldReceive('fetchRaw')
            ->once()
            ->andReturn([]);

        $faceAuthData = new FaceAuthRequestDTO(
            ccApplicationId: $client->creditcard_application_id,
            method: 'ME_AUTH_FACIAL_2',
            query: $client->dui,
            imageBuffer: base64_encode(Str::random(60)),
            deviceId: 'device_id',
            customerId: '1234'
        );

        $result = $this->task
            ->withFaceAuthData($faceAuthData)
            ->do();

        $this->assertIsArray($result);
    }
}
