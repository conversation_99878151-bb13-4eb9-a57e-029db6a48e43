<?php

namespace Tests\Feature\Tasks;

use App\Models\Client;
use App\Services\AuthNotifyService;
use App\Tasks\NotifyUserOfTooManyAttemptsTask;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class NotifyUserOfTooManyAttemptsTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itCallsTheNotifierOnExistentUser(): void
    {
        $user = Client::factory()->create();
        $notifier = Mockery::mock(AuthNotifyService::class);
        $notifier
            ->shouldReceive('blockTime')
            ->once();

        $this->assertNull(
            app(NotifyUserOfTooManyAttemptsTask::class, ['notifier' => $notifier])
                ->withNickname($user->nickname)
                ->do()
        );
    }

    /**
     * @test
     */
    public function itDoesNotCallTheNotifierOnUnexistentUser(): void
    {
        $notifier = Mockery::mock(AuthNotifyService::class);
        $notifier->shouldNotReceive('blockTime');

        $this->assertNull(
            app(NotifyUserOfTooManyAttemptsTask::class, ['notifier' => $notifier])
                ->withNickname(Str::random())
                ->do()
        );
    }
}
