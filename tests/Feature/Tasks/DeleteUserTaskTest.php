<?php

namespace Tests\Feature\Tasks;

use App\Events\UserDeletedEvent;
use App\Models\Client;
use App\Tasks\DeleteUserTask;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class DeleteUserTaskTest extends TestCase
{
    use WithFaker;

    /**
     * Subject to test.
     */
    protected DeleteUserTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = App::make(DeleteUserTask::class);
    }

    /**
     * @test
     */
    public function shouldSoftDeleteAnUser(): void
    {
        $client = Client::factory()->create();
        
        Event::fake();
        
        $this
            ->task
            ->withUser($client)
            ->do();

        $this->assertSoftDeleted('clients', [
            'id' => $client->id,
        ]);
        $this->assertNull($client->nit);
        $this->assertNull($client->first_name);
        $this->assertNull($client->first_surname);
        Event::assertDispatched(UserDeletedEvent::class);
    }

    /**
     * @test
     */
    public function shouldForceDeleteAnUser(): void
    {
        $client = Client::factory()->create();
        
        Event::fake();
        
        $this
            ->task
            ->withUser($client)
            ->forceDelete()
            ->do();

        $this->assertDatabaseMissing('clients', [
            'id' => $client->id,
        ]);
        Event::assertDispatched(UserDeletedEvent::class);
    }
}
