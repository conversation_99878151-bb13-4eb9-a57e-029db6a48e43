<?php

namespace Tests\Feature\Tasks;

use App\Models\Client;
use App\Models\Device;
use App\Services\Configurations as Config;
use App\Tasks\CheckUserCanUseDeviceTask;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class CheckUserCanUseDeviceTaskTest extends TestCase
{
    use ClosesMockery;

    /**
     * Subject to test.
     */
    protected CheckUserCanUseDeviceTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = App::make(CheckUserCanUseDeviceTask::class);
    }

    /**
     * @test
     */
    public function itReturnsTrueIfTheUserAlreadyHasThatDevice(): void
    {
        $device = Device::factory()->create();

        $result = $this
            ->task
            ->withUser($device->client)
            ->withDeviceId($device->device_id)
            ->do();

        $this->assertTrue($result);
    }

    /**
     * @test
     */
    public function itReturnsTrueIfTheUserCanRegisterTheDevice(): void
    {
        $user = Client::factory()->create();

        $result = $this
            ->task
            ->withUser($user)
            ->withDeviceId(Str::uuid()->toString())
            ->do();

        $this->assertTrue($result);
    }

    /**
     * @test
     */
    public function itReturnsFalseIfTheUserHasTooManyDevices(): void
    {
        $config = Mockery::mock(Config::class);
        $config
            ->shouldReceive('maximunDevices')
            ->once()
            ->andReturn(2);
        $task = App::makeWith(CheckUserCanUseDeviceTask::class, ['config' => $config]);

        $user = Client::factory()->create();
        Device::factory()
            ->count(3)
            ->create(['client_id' => $user->id]);

        $result = $task
            ->withUser($user)
            ->withDeviceId(Str::uuid()->toString())
            ->do();

        $this->assertFalse($result);
    }

    /**
     * @test
     */
    public function itDoesNotCountSoftDeletedDevices(): void
    {
        $config = Mockery::mock(Config::class);
        $config
            ->shouldReceive('maximunDevices')
            ->andReturn(1);
        $task = App::makeWith(CheckUserCanUseDeviceTask::class, ['config' => $config]);

        $user = Client::factory()->create();
        $devices = Device::factory()
            ->count(3)
            ->create(['client_id' => $user->id]);

        // Devices are coutend normally.
        $result = $task
            ->withUser($user)
            ->withDeviceId(Str::uuid()->toString())
            ->do();
        $this->assertFalse($result);

        // Deleted devices should not count.
        $devices
            ->map
            ->delete();
        $result = $task
            ->withUser($user)
            ->withDeviceId(Str::uuid()->toString())
            ->do();
        $this->assertTrue($result);
    }
}
