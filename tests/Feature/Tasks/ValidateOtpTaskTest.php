<?php

namespace Tests\Feature\Tasks;

use App\Dtos\OtpValidation;
use App\Repos\ValidateOtpRepo;
use App\Tasks\ValidateOtpTask;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ValidateOtpTaskTest extends TestCase
{
    private ValidateOtpRepo|MockInterface $repo;

    public function setUp(): void
    {
        parent::setUp();

        $this->repo = $this->mock(ValidateOtpRepo::class);
    }

    #[Test]
    public function it_validates_otp_code(): void
    {
        $otpValidationData = new OtpValidation('shared-key', '123456');

        $this->repo
            ->shouldReceive('isValidOtp')
            ->andReturnTrue();

        $result = (new ValidateOtpTask($this->repo))
            ->withOtpData($otpValidationData)
            ->do();

        $this->assertTrue($result);
    }
}
