<?php

namespace Tests\Feature\Tasks;

use App\DTO\VBankAccountDTO;
use App\Repos\ValidateVBankAccountRepo;
use App\Tasks\ValidateVBankAccountTask;
use Mo<PERSON>y\MockInterface;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestWith;

class ValidateVBankAccountTaskTest extends TestCase
{
    private ValidateVBankAccountTask $task;

    private MockInterface|ValidateVBankAccountRepo $mockedRepo;

    public function setUp(): void
    {
        parent::setUp();

        /** @var MockInterface|ValidateVBankAccountRepo */
        $this->mockedRepo = $this->mock(ValidateVBankAccountRepo::class);

        $this->task = new ValidateVBankAccountTask($this->mockedRepo);
    }

    #[Test]
    #[TestWith(['customerId', null])]
    #[TestWith([null, 'customerDui'])]
    public function validated_if_vbank_account_exists($customerId, $customerDui): void
    {
        $vBankAccountDTO = new VBankAccountDTO('username', false);

        $this->mockedRepo
            ->shouldReceive('prepare')
            ->with([
                'CustomerId' => $customerId,
                'Dui' => $customerDui
            ])
            ->andReturn($this->mockedRepo);

        $this->mockedRepo
            ->shouldReceive('fetch')
            ->once()
            ->andReturn($vBankAccountDTO);

        $result = $customerId
            ? $this->task->withCustomerId($customerId)->do()
            : $this->task->withCustomerDui($customerDui)->do();

        $this->assertInstanceOf(VBankAccountDTO::class, $result);

        $this->assertEquals($vBankAccountDTO->toArray(), $result->toArray());
    }
}
