<?php

namespace Tests\Feature\Tasks;

use App\Models\Device;
use App\Tasks\ClearChallengeOfDeviceTask;
use Tests\TestCase;

class ClearChallengeOfDeviceTaskTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected ClearChallengeOfDeviceTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new ClearChallengeOfDeviceTask();
    }

    /**
     * @test
     */
    public function itClearsTheChallengeOfADevice(): void
    {
        $device = Device::factory()->create();

        $this->assertNotNull($device->verification_challenge);

        $this
            ->task
            ->withDevice($device)
            ->do();

        $this->assertNull($device->fresh()->verification_challenge);
    }
}
