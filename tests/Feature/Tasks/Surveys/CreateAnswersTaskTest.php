<?php

namespace Tests\Feature\Tasks\Surveys;

use App\Dtos\Answer;
use App\Tasks\Surveys\CreateAnswersTask;
use Tests\TestCase;
use Tests\Traits\CreatesSurveys;

class CreateAnswersTaskTest extends TestCase
{
    use CreatesSurveys;

    /**
     * Subject to test.
     */
    protected CreateAnswersTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new CreateAnswersTask();
    }

    /**
     * @test
     */
    public function itCreatesAnswers(): void
    {
        $this->freezeTime();

        $customerId = random_int(100, 999);

        [,, $options] = $this->createSurveyWithQuestionsAndOptions();
        $answers = $options
            ->pluck('id')
            ->map(fn($id) => Answer::factory()->createWith(['optionId' => $id]));

        $this
            ->task
            ->withCustomerId($customerId)
            ->withAnswers($answers)
            ->do();

        foreach ($options as $option) {
            $this->assertDatabaseHas('answers', [
                'option_id' => $option->id,
                'customer_id' => $customerId,
                'created_at' => now()
            ]);
        }
    }
}
