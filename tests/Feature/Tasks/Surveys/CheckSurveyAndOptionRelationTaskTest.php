<?php

namespace Tests\Feature\Tasks\Surveys;

use App\Models\Option;
use App\Tasks\Surveys\CheckSurveyAndOptionRelationTask;
use Tests\TestCase;
use Tests\Traits\CreatesSurveys;

class CheckSurveyAndOptionRelationTaskTest extends TestCase
{
    use CreatesSurveys;

    /**
     * Subject to test.
     */
    protected CheckSurveyAndOptionRelationTask $task;

    protected function setUp(): void
    {
        parent::setUp();

        $this->task = new CheckSurveyAndOptionRelationTask();
    }

    /**
     * @test
     */
    public function itChecksThatSurveryAndOptionAreRelated(): void
    {
        [$survey, ,$options] = $this->createSurveyWithQuestionsAndOptions();

        $isRelated = $this
            ->task
            ->withSurveyId($survey->id)
            ->withOptionId($options->random()->id)
            ->do();
        $this->assertTrue($isRelated);

        $isRelated = $this
            ->task
            ->withSurveyId($survey->id)
            ->withOptionId(Option::factory()->create()->id)
            ->do();
        $this->assertFalse($isRelated);
    }
}
