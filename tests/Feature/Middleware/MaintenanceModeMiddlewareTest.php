<?php

namespace Tests\Feature\Middleware;

use App\Http\Middleware\MaintenanceModeMiddleware;
use App\Models\MaintenanceModes as MaintenancePeriod;
use Illuminate\Http\Request;
use Tests\TestCase;

class MaintenanceModeMiddlewareTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected MaintenanceModeMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = app(MaintenanceModeMiddleware::class);
    }

    /**
     * @test
     */
    public function itAllowsRequestsToPassOnNoMaintenance(): void
    {
        $request = Request::create('/api/some-path', 'GET');
        $callback = fn ($_) => 'next middleware';

        $this->assertSame($callback(null), $this->middleware->handle($request, $callback));
    }

    /**
     * @test
     */
    public function itReturnsServiceUnavailableOnMaintenance(): void
    {
        $request = Request::create('/api/some-path', 'GET');
        $callback = fn ($_) => 'next middleware';
        $period = MaintenancePeriod::factory()->create([
            'date_time_maintenance_start' => now()->subDay(),
            'date_time_maintenance_end' => now()->addDay(),
        ]);

        $this->assertEquals(503, $this->middleware->handle($request, $callback)->status());

        $period->delete();
    }
}
