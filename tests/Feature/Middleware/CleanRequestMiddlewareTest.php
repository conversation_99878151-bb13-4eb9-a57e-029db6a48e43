<?php

namespace Tests\Feature\Middleware;

use Illuminate\Http\Request;
use Tests\TestCase;
use Illuminate\Support\Facades\Route;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\TestWith;
use Symfony\Component\HttpFoundation\Response;

class CleanRequestMiddlewareTest extends TestCase
{
    #[Test]
    #[TestWith(['string', '<script>alert("XSS")</script>', 'alert("XSS")'])]
    #[TestWith(['array', ['<script>alert("XSS")</script>', '<bold>example</bold>'], ['alert("XSS")', 'example']])]
    #[TestDox('it sanitizes $payloadType request body content')]
    public function it_sanitizes_request_body_content(string $payloadType, mixed $payload, mixed $expectedResponse): void
    {
        $payload = [
            'data' => $payload
        ];

        Route::middleware('clean.api')->post('/api/test', fn (Request $request) => $request->all());

        $response = $this->postJson('/api/test', $payload);

        $response
            ->assertJson([
                'data' => $expectedResponse
            ])
            ->assertStatus(Response::HTTP_OK);
    }

    #[Test]
    #[TestWith(['BIOMETRY'])]
    #[TestWith(['ADDITIONALCCARDS'])]
    #[TestDox('it can exclude $endpoint from sanitizing body content')]
    public function it_can_exclude_some_endpoint_from_sanitizing_body_content(string $endpoint): void
    {
        $payload = [
            'data' => '<script>alert("XSS")</script>'
        ];

        Route::middleware('clean.api')->post("/api/$endpoint", fn () => $payload);

        $response = $this->postJson("/api/$endpoint", $payload);

        $response
            ->assertJsonFragment($payload)
            ->assertStatus(Response::HTTP_OK);
    }

    #[Test]
    public function it_returns_a_bad_request_response_when_request_body_is_empty(): void
    {

        Route::middleware('clean.api')->post('/api/test', fn () => 'OK');

        $response = $this->postJson('/api/test', []);

        $response
            ->assertStatus(Response::HTTP_BAD_REQUEST)
            ->assertJson([
                'message' => 'Format Incorrect',
                'status' => Response::HTTP_BAD_REQUEST,
            ]);
    }
}
