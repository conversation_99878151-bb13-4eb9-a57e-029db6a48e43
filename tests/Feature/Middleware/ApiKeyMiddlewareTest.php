<?php

namespace Tests\Feature\Middleware;

use App\Http\Middleware\ApiKeyMiddleware;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\HandlesEncryption;

class ApiKeyMiddlewareTest extends TestCase
{
    use HandlesEncryption;

    /**
     * Subject to test.
     */
    protected ApiKeyMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = app(ApiKeyMiddleware::class);
    }

    /**
     * @test
     */
    public function itAllowsValidRequestsToPass(): void
    {
        // Set up.
        $request = Request::create('/api/some-path', 'GET');
        $request
            ->headers
            ->add(['APIKey' => $this->encrypt(config('app.api.key'))]);
        $callback = fn ($_) => 'next middleware';

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
    }

    /**
     * @test
     */
    public function itReturnsUnauthorizedOnMissingHeader(): void
    {
        // Missing header.
        $request = Request::create('/api/some-path', 'GET');

        $result = $this->middleware->handle($request, fn ($_) => '');

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());

        // Empty header.
        $request
            ->headers
            ->add(['APIKey' => '']);

        $result = $this->middleware->handle($request, fn ($_) => '');

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
    }

    /**
     * @test
     */
    public function itReturnsUnauthorizedOnWrongValue(): void
    {
        // Wrong value but encrypted.
        $request = Request::create('/api/some-path', 'GET');
        $request
            ->headers
            ->add(['APIKey' => $this->encrypt(Str::random())]);

        $result = $this->middleware->handle($request, fn ($_) => '');

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());

        // Wrong value and without encryption.
        $request
            ->headers
            ->replace(['APIKey' => Str::random(2)]);

        $result = $this->middleware->handle($request, fn ($_) => '');

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
    }
}
