<?php

namespace Tests\Feature\Middleware;

use App\Http\Middleware\AuthUserTokenIfAnyMiddleware;
use App\Models\Client;
use Illuminate\Auth\AuthManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Tests\TestCase;

class AuthUserTokenIfAnyMiddlewareTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected AuthUserTokenIfAnyMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = App::make(AuthUserTokenIfAnyMiddleware::class);
        $this->auth = App::make(AuthManager::class)->guard('api');
        $this->user = Client::factory()
            ->withBaesToken()
            ->create();
    }

    /**
     * @test
     */
    public function itAuthsAUser(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add(['Authorization' => 'Bearer ' . $this->user->baes_token]);

        $callback = fn ($_) => 'next middleware';

        // Preassert.
        $this->assertNull($this->auth->user());

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
        $this->assertEquals($this->user->id, $this->auth->user()->id);
    }

    /**
     * @test
     */
    public function itDoesNotAuthOnMissingToken(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $callback = fn ($_) => 'next middleware';

        // Preassert.
        $this->assertNull($this->auth->user());

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
        $this->assertNull($this->auth->user());
    }

    /**
     * @test
     */
    public function itDoesNotAuthOnWrongToken(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add(['Authorization' => 'Bearer wrong_token']);
        $callback = fn ($_) => 'next middleware';

        // Preassert.
        $this->assertNull($this->auth->user());

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
        $this->assertNull($this->auth->user());
    }
}
