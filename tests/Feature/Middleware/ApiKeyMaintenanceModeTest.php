<?php

namespace Tests\Feature\Middleware;

use Tests\TestCase;
use App\Models\ApiKeys;
use App\Services\Crypt\Rsa;
use Illuminate\Http\Request;
use App\Enums\Message\Generate;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Middleware\ApiKeyMaintainanceMode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use Spatie\Crypto\Rsa\Exceptions\CouldNotDecryptData;

class ApiKeyMaintenanceModeTest extends TestCase
{
    private ApiKeyMaintainanceMode $middleware;

    private Request $validRequest;

    private Request $invalidRequest;

    private Rsa $rsa;

    public function setUp(): void
    {
        parent::setUp();

        $this->rsa = new Rsa();

        $this->middleware = $this->app->make(ApiKeyMaintainanceMode::class);

        $this->validRequest = Request::create('/api/endpoint');

        $this->validRequest
            ->headers
            ->add(['APIKey' => $this->rsa->encrypt(config('app.api.key'))]);

        $this->invalidRequest = Request::create('/api/endpoint');

        $this->invalidRequest
            ->headers
            ->add(['APIKey' => config('app.api.key')]);
    }

    #[Test]
    public function exception_is_thrown_if_api_key_is_not_decryptable(): void
    {
        $this->expectException(CouldNotDecryptData::class);

        $this->middleware->handle($this->invalidRequest, fn() => "");
    }

    #[Test]
    public function returns_unauthorized_if_api_keys_do_not_match(): void
    {
        /** @var JsonResponse */
        $result = $this->middleware->handle($this->validRequest, fn() => "");

        $this->assertSame(
            [
                'result' => Generate::NOT_AUTORIZATE->value,
                'status' => Response::HTTP_UNAUTHORIZED
            ],
            $result->getData(true)
        );

        $this->assertSame(Response::HTTP_UNAUTHORIZED, $result->getStatusCode());
    }

    #[Test]
    public function passes_on_valid_request(): void
    {
        ApiKeys::factory()->create([
            'maintenance_mode' => true,
            'apikey' => config('app.api.key')
        ]);

        $expectedResult = 'next';

        $result = $this->middleware->handle($this->validRequest, fn() => $expectedResult);

        $this->assertSame($expectedResult, $result);
    }
}
