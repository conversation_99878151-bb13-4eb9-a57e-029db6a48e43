<?php

namespace Tests\Feature\Middleware;

use App\Http\Middleware\InterceptUpdateCustomerContactMiddleware;
use App\Models\Client;
use App\Repos\GetCustomerContactChannelsRepo;
use App\Services\Log;
use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class InterceptUpdateCustomerContactMiddlewareTest extends TestCase
{
    private const ENDPOINT = 'api/CustomerContactsp';

    private Client $client;

    private \Closure $next;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create(['mode' => 'TEST']);

        $responseContent = json_encode([
            'Status' => [
                'RequestStatus' => ['Code' => Response::HTTP_OK],
                'ResponseStatus' => ['Code' => 0]
            ]
        ]);

        $response = new Response($responseContent);

        $this->next = fn($req) => $response;
    }

    #[Test]
    public function middleware_updates_customer_phone_number_on_success_response(): void
    {
        $updatedPhoneNumber = '000000000';

        $this->runMiddlewareTest(
            GetCustomerContactChannelsRepo::PHONE_TYPE_CODE,
            $updatedPhoneNumber,
        );
    }

    #[Test]
    public function middleware_updates_customer_email_on_success_response(): void
    {
        $updatedEmail = '<EMAIL>';

        $this->runMiddlewareTest(
            GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE,
            $updatedEmail,
        );
    }

    private function runMiddlewareTest(string $contactTypeCode, string $updatedContactValue,): void
    {
        $request = Request::create(self::ENDPOINT, 'PUT', [
            'CustomerContacts' => [
                'ContactTypeCode' => $contactTypeCode,
                'ConctactValue' => $updatedContactValue,
            ],
        ]);

        $request->setUserResolver(fn() => $this->client);

        $middleware = new InterceptUpdateCustomerContactMiddleware(
            $this->app->make(Log::class),
        );

        $middleware->handle($request, $this->next);

        $this->client->refresh();

        $currentContactValue = $contactTypeCode === GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE
            ? $this->client->email
            : $this->client->phone_number;

        $this->assertEquals($updatedContactValue, $currentContactValue);
    }
}
