<?php

namespace Tests\Feature\Middleware;

use App\Auth\Baes\BaesGuard;
use App\Http\Middleware\AuthUserTokenMiddleware;
use App\Models\Client;
use Illuminate\Auth\AuthManager;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Tests\TestCase;

class AuthUserTokenMiddlewareTest extends TestCase
{
    protected AuthUserTokenMiddleware $middleware;

    private BaesGuard $auth;

    private Client $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = App::make(AuthUserTokenMiddleware::class);
        $this->auth = App::make(AuthManager::class)->guard('api');
        $this->user = Client::factory()
            ->withBaesToken()
            ->create();
    }

    /**
     * @test
     */
    public function itAllowsValidRequestsToPass(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add(['Authorization' => 'Bearer ' . $this->user->baes_token]);

        $callback = fn($_) => 'next middleware';

        // Preassert.
        $this->assertNull($this->auth->user());

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
        $this->assertEquals($this->user->id, $this->auth->user()->id);
    }

    /**
     * @test
     */
    public function itFailsOnMissingToken(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');

        $callback = fn($_) => 'next middleware';

        // Preassert.
        $this->assertNull($this->auth->user());

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
        $this->assertNull($this->auth->user());
    }

    /**
     * @test
     */
    public function itFailsOnWrongToken(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add(['Authorization' => 'Bearer ' . Str::random(100)]);

        $callback = fn($_) => 'next middleware';

        // Preassert.
        $this->assertNull($this->auth->user());

        // Run.
        $result = $this->middleware->handle($request, $callback);

        // Assert.
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
        $this->assertNull($this->auth->user());
    }
}
