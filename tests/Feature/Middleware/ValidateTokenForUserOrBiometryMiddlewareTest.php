<?php

namespace Tests\Feature\Middleware;

use App\Dtos\ExternalAuthBiometry;
use App\Dtos\ExternalAuthUser;
use App\Http\Middleware\ValidateTokenForUserMiddleware;
use App\Http\Middleware\ValidateTokenForUserOrBiometryMiddleware;
use App\Repos\ValidateTokenRepo;
use Exception;
use Illuminate\Http\Client\Factory as Http;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Mockery;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class ValidateTokenForUserOrBiometryMiddlewareTest extends TestCase
{
    use ClosesMockery;

    /**
     * External endpoint to mock.
     */
    private string $externalEndpoint;

    protected function setUp(): void
    {
        parent::setUp();

        $this->externalEndpoint = config('services.baes.base_url') . 'ValidateTokenAuth';
    }

    /**
     * @test
     */
    public function itAllowsValidUserRequestsToPass(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add([
                ExternalAuthUser::DEVICE_HEADER => 'device',
                ExternalAuthUser::USER_AUTH_HEADER => 'some id',
                ExternalAuthUser::USERNAME_HEADER => 'some_username',
                'Authorization' => 'Bearer token',
            ]);

        $callback = fn ($_) => 'next middleware';

        App::when(ValidateTokenRepo::class)
            ->needs(Http::class)
            ->give(
                fn () => (new Http())->fake([
                    $this->externalEndpoint => Http::response(
                        ['Data' => 'OK'],
                        200,
                    )
                ])
            );

        // Run.
        $result = App::make(ValidateTokenForUserOrBiometryMiddleware::class)->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
    }

    /**
     * @test
     */
    public function itAllowsValidBiometryRequestsToPass(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add([
                ExternalAuthBiometry::DEVICE_HEADER => 'device',
                ExternalAuthBiometry::DUI_HEADER => 'dui',
                ExternalAuthBiometry::BIOMETRY_HEADER => 'biometry',
                'Authorization' => 'Bearer token',
            ]);

        $callback = fn ($_) => 'next middleware';

        App::when(ValidateTokenRepo::class)
            ->needs(Http::class)
            ->give(
                fn () => (new Http())->fake([
                    $this->externalEndpoint => Http::response(
                        ['Data' => 'OK'],
                        200,
                    )
                ])
            );

        // Run.
        $result = App::make(ValidateTokenForUserOrBiometryMiddleware::class)->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
    }

    /**
     * @test
     */
    public function itReturnsForbiddenOnMissingToken(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add([
                ExternalAuthBiometry::DEVICE_HEADER => 'device',
                ExternalAuthBiometry::DUI_HEADER => 'dui',
                ExternalAuthBiometry::BIOMETRY_HEADER => 'biometry',
            ]);

        // Run.
        $result = App::make(ValidateTokenForUserOrBiometryMiddleware::class)->handle($request, fn ($_) => '');

        // Assert.
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
    }

    /**
     * @test
     */
    public function itReturnsForbiddenOnWrongData(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add(['Authorization' => 'Bearer token']);

        App::when(ValidateTokenRepo::class)
            ->needs(Http::class)
            ->give(
                fn () => (new Http())->fake([
                    $this->externalEndpoint => Http::response(
                        ['Data' => 'FAIL'],
                        200,
                    )
                ])
            );

        // Run.
        $result = App::make(ValidateTokenForUserOrBiometryMiddleware::class)->handle($request, fn ($_) => '');

        // Assert.
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
    }

    /**
     * @test
     */
    public function itAllowsValidBiometryRequestsToPassEvenIfUserRequestIsInvalid(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add([
                ExternalAuthUser::DEVICE_HEADER => 'device',
                ExternalAuthUser::USER_AUTH_HEADER => 'some id',
                ExternalAuthUser::USERNAME_HEADER => 'some_username',
                'Authorization' => 'Bearer token',
                ExternalAuthBiometry::DUI_HEADER => 'dui',
                ExternalAuthBiometry::BIOMETRY_HEADER => 'biometry',
            ]);

        $callback = fn ($_) => 'next middleware';

        App::when(ValidateTokenRepo::class)
            ->needs(Http::class)
            ->give(
                fn () => (new Http())->fake([
                    $this->externalEndpoint => Http::response(
                        ['Data' => 'OK'],
                        200,
                    )
                ])
            );

        $userMiddleware = Mockery::mock(ValidateTokenForUserMiddleware::class);
        $userMiddleware
            ->shouldReceive('isValid')
            ->with($request)
            ->once()
            ->andReturn(false);

        // Run.
        $result = App::makeWith(
            ValidateTokenForUserOrBiometryMiddleware::class,
            ['userMiddleware' => $userMiddleware],
        )->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
    }

    /**
     * @test
     */
    public function itReturnsForbiddenIfExternalServicesAreNotAvailable(): void
    {
        $request = Request::create('/', 'GET');
        $request->headers->add([
            ExternalAuthUser::DEVICE_HEADER => 'device',
            ExternalAuthUser::USER_AUTH_HEADER => 'some id',
            'Authorization' => 'Bearer token',
            ExternalAuthBiometry::DUI_HEADER => 'dui',
            ExternalAuthBiometry::BIOMETRY_HEADER => 'biometry',
        ]);

        $validateTokenRepoMock = $this->mock(ValidateTokenRepo::class);
        $validateTokenRepoMock
            ->shouldReceive('fetch')
            ->andThrow(Exception::class);

        /** @var ValidateTokenForUserOrBiometryMiddleware */
        $middleware = $this->app->make(ValidateTokenForUserOrBiometryMiddleware::class);
        $response = $middleware->handle($request, fn () => '');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }
}
