<?php

namespace Tests\Feature\Middleware;

use App\Dtos\ExternalAuthUser;
use App\Http\Middleware\ValidateTokenForUserMiddleware;
use App\Repos\ValidateTokenRepo;
use Illuminate\Http\Client\Factory as Http;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Tests\TestCase;

class ValidateTokenForUserMiddlewareTest extends TestCase
{
    /**
     * External endpoint to mock.
     */
    private string $externalEndpoint;

    protected function setUp(): void
    {
        parent::setUp();

        $this->externalEndpoint = config('services.baes.base_url') . 'ValidateTokenAuth';
    }

    /**
     * @test
     */
    public function itAllowsValidRequestsToPass(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add([
                ExternalAuthUser::DEVICE_HEADER => 'device',
                ExternalAuthUser::USER_AUTH_HEADER => 'some id',
                ExternalAuthUser::USERNAME_HEADER => 'some_username',
                'Authorization' => 'Bearer token',
            ]);

        $callback = fn ($_) => 'next middleware';

        App::when(ValidateTokenRepo::class)
            ->needs(Http::class)
            ->give(
                fn () => (new Http())->fake([
                    $this->externalEndpoint => Http::response(
                        ['Data' => 'OK'],
                        200,
                    )
                ])
            );

        // Run.
        $result = App::make(ValidateTokenForUserMiddleware::class)->handle($request, $callback);

        // Assert.
        $this->assertSame($callback(null), $result);
    }

    /**
     * @test
     */
    public function itReturnsForbiddenOnMissingToken(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add([
                ExternalAuthUser::DEVICE_HEADER => 'device',
                ExternalAuthUser::USER_AUTH_HEADER => 'some id',
                ExternalAuthUser::USERNAME_HEADER => 'some_username',
            ]);

        // Run.
        $result = App::make(ValidateTokenForUserMiddleware::class)->handle($request, fn ($_) => '');

        // Assert.
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
    }

    /**
     * @test
     */
    public function itReturnsForbiddenOnWrongData(): void
    {
        // Set up.
        $request = Request::create('/', 'GET');
        $request
            ->headers
            ->add(['Authorization' => 'Bearer token']);

        App::when(ValidateTokenRepo::class)
            ->needs(Http::class)
            ->give(
                fn () => (new Http())->fake([
                    $this->externalEndpoint => Http::response(
                        ['Data' => 'FAIL'],
                        200,
                    )
                ])
            );

        // Run.
        $result = App::make(ValidateTokenForUserMiddleware::class)->handle($request, fn ($_) => '');

        // Assert.
        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(401, $result->getStatusCode());
    }
}
