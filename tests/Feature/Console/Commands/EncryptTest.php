<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\Encrypt;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\HandlesEncryption;

class EncryptTest extends TestCase
{
    use HandlesEncryption;

    /**
     * Subject to test.
     */
    protected Encrypt $command;

    /**
     * @test
     */
    public function itEncrypts(): void
    {
        $string = Str::random();

        $this
            ->withoutMockingConsoleOutput()
            ->artisan("encrypt {$string}");

        $decryptedResult = $this->decrypt(trim(Artisan::output()));
        $this->assertSame($string, $decryptedResult);
    }
}
