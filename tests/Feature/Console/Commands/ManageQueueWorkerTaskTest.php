<?php

namespace Tests\Feature\Console\Commands;

use Tests\TestCase;
use Illuminate\Support\Facades\Artisan;
use App\Console\Commands\QueueTaskOptions\StopQueueWorkerTask;
use App\Console\Commands\QueueTaskOptions\StartQueueWorkerTask;
use App\Console\Commands\QueueTaskOptions\CreateQueueWorkerTask;
use App\Console\Commands\QueueTaskOptions\DeleteQueueWorkerTask;

class ManageQueueWorkerTaskTest extends TestCase
{
    public function test_create_option_executes_correct_class()
    {
        $this->mock(CreateQueueWorkerTask::class, function ($mock) {
            $mock->shouldReceive('execute')
                ->once()
                ->with('Laravel Queue Worker Task')
                ->andReturn('Task "Laravel Queue Worker Task" created successfully.');
        });

        Artisan::call('manage:queue-task', ['--create' => true]);

        $output = Artisan::output();

        $this->assertStringContainsString('Task "Laravel Queue Worker Task" created successfully.', $output);
    }

    public function test_start_option_executes_correct_class()
    {
        $this->mock(StartQueueWorkerTask::class, function ($mock) {
            $mock->shouldReceive('execute')
                ->once()
                ->with('Laravel Queue Worker Task')
                ->andReturn('Task started successfully.');
        });

        Artisan::call('manage:queue-task', ['--start' => true]);

        $output = Artisan::output();

        $this->assertStringContainsString('Task started successfully.', $output);
    }

    public function test_end_option_executes_correct_class()
    {
        $this->mock(StopQueueWorkerTask::class, function ($mock) {
            $mock->shouldReceive('execute')
                ->once()
                ->with('Laravel Queue Worker Task')
                ->andReturn('Task stopped successfully.');
        });

        Artisan::call('manage:queue-task', ['--end' => true]);

        $output = Artisan::output();

        $this->assertStringContainsString('Task stopped successfully.', $output);
    }

    public function test_delete_option_executes_correct_class()
    {
        $this->mock(DeleteQueueWorkerTask::class, function ($mock) {
            $mock->shouldReceive('execute')
                ->once()
                ->with('Laravel Queue Worker Task')
                ->andReturn('Task deleted successfully.');
        });

        Artisan::call('manage:queue-task', ['--delete' => true]);

        $output = Artisan::output();

        $this->assertStringContainsString('Task deleted successfully.', $output);
    }
}
