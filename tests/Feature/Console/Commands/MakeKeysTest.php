<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\MakeKeys;
use App\Services\Crypt\Rsa;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Tests\TestCase;

class MakeKeysTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected MakeKeys $command;

    /**
     * RSA service.
     */
    private Rsa $rsa;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rsa = App::make(Rsa::class);
    }

    /**
     * @test
     */
    public function itMakesValidKeys(): void
    {
        $this
            ->withoutMockingConsoleOutput()
            ->artisan('make:keys');

        // Parse keys.
        $keys = Artisan::output();
        preg_match(
            '/-----BEGIN PUBLIC KEY-----\n([\w\d+\/\n=]+)-----END PUBLIC KEY-----/',
            $keys,
            $matches1,
        );
        $publicKey = $matches1[0];
        preg_match(
            '/-----BEGIN PRIVATE KEY-----\n([\w\d+\/\n=]+)-----END PRIVATE KEY-----/',
            $keys,
            $matches2,
        );
        $privateKey = $matches2[0];

        // Assert.
        $subject = Str::random();
        $this->assertEquals(
            $subject,
            $this->rsa->decryptWith(
                $this->rsa->encryptWith($subject, $publicKey),
                $privateKey,
            ),
        );
    }
}
