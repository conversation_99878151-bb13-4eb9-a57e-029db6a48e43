<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\DeleteClient;
use Tests\TestCase;
use Tests\Traits\CreatesClients;

class DeleteClientTest extends TestCase
{
    use CreatesClients;

    /**
     * Subject to test.
     */
    protected DeleteClient $command;

    /**
     * @test
     */
    public function itDeletesClientsById(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        $this
            ->artisan("delete:client", ['--id' => $user->id])
            ->assertSuccessful();

        $this->assertDatabaseMissing('clients', [
            'id' => $user->id,
        ]);
    }

    /**
     * @test
     */
    public function itDeletesClientsByCustomerId(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        $this
            ->artisan("delete:client", ['--customer-id' => $user->client_id])
            ->assertSuccessful();

        $this->assertDatabaseMissing('clients', [
            'id' => $user->id,
        ]);
    }

    /**
     * @test
     */
    public function itDeletesClientsByDui(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        $this
            ->artisan("delete:client", ['--dui' => $user->dui])
            ->assertSuccessful();

        $this->assertDatabaseMissing('clients', [
            'id' => $user->id,
        ]);
    }

    /**
     * @test
     */
    public function itDoesNotFailWhenTryingToDeleteInexistentClient(): void
    {
        $this
            ->artisan("delete:client", ['--id' => 0])
            ->assertSuccessful();
    }
}
