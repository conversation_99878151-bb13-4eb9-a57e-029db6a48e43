<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\MakeClient;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class MakeClientTest extends TestCase
{
    /**
     * Subject to test.
     */
    protected MakeClient $command;

    /**
     * @test
     */
    public function itMakesClients(): void
    {
        $this
            ->withoutMockingConsoleOutput()
            ->artisan('make:client');

        $firstLine = explode("\n", Artisan::output())[0];
        $id = Arr::last(explode(' ', $firstLine));
        $this->assertTrue(is_numeric($id));
        $this->assertDatabaseHas('clients', [
            'id' => $id,
        ]);
    }
}
