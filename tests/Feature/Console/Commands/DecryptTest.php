<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\Decrypt;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\HandlesEncryption;

class DecryptTest extends TestCase
{
    use HandlesEncryption;

    /**
     * Subject to test.
     */
    protected Decrypt $command;

    /**
     * @test
     */
    public function itDecrypts(): void
    {
        $string = Str::random();
        $encryptedString = $this->encrypt($string);

        $this
            ->withoutMockingConsoleOutput()
            ->artisan("decrypt {$encryptedString}");

        $this->assertSame($string, trim(Artisan::output()));
    }
}
