<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\Sign;
use App\Services\Crypt\Rsa;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\CreatesRsaKeys;

class SignTest extends TestCase
{
    use CreatesRsaKeys;

    /**
     * Subject to test.
     */
    protected Sign $command;

    /**
     * RSA service.
     */
    private Rsa $rsa;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rsa = App::make(Rsa::class);
    }

    /**
     * @test
     */
    public function itSigns(): void
    {
        // Set up.
        [$privateKey, $publicKey] = $this->createKeyPair();

        $tempDisk = Str::random();
        Storage::fake($tempDisk);
        Storage::disk($tempDisk)->put('private_key_file', $privateKey);
        $privateKeyFilePath = Storage::disk($tempDisk)->path('private_key_file');

        $subject = Str::random();

        // Run.
        $this
            ->withoutMockingConsoleOutput()
            ->artisan("sign {$subject} {$privateKeyFilePath}");

        // Assert.
        $this->assertTrue($this->rsa->verifyWith(
            $subject,
            trim(Artisan::output()),
            $publicKey,
        ));
    }
}
