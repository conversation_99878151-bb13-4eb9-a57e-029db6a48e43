<?php

namespace Tests\Feature\Controllers\Surveys;

use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\CreatesSurveys;

class SurveysIndexControllerTest extends TestCase
{
    use WithoutMiddleware;
    use CreatesSurveys;

    private string $endpoint = '/api/surveys';

    /**
     * @test
     */
    public function itShowsASurveyByName(): void
    {
        [$survey, $questions, $options] = $this->createSurveyWithQuestionsAndOptions();

        $question = $questions->random();
        $option = $options->random();
        $this
            ->getJson($this->endpoint . "?name={$survey->name}")
            ->assertSuccessful()
            ->assertJsonFragment([
                'id' => $survey->id,
                'name' => $survey->name,
                'description' => $survey->description,
            ])
            ->assertJsonFragment([
                'id' => $question->id,
                'survey_id' => $question->survey_id,
                'type' => $question->type,
                'question' => $question->question,
            ])
            ->assertJsonFragment([
                'id' => $option->id,
                'question_id' => $option->question_id,
                'value' => $option->value,
                'type' => $option->type,
            ]);
    }

    /**
     * @test
     */
    public function itReturnsNotFoundOnInexistentSurvey(): void
    {
        $this
            ->getJson($this->endpoint . '?name=' . Str::random())
            ->assertNotFound();
    }
}
