<?php

namespace Tests\Feature\Controllers\Surveys;

use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;
use Tests\Traits\CreatesSurveys;

class SurveysShowControllerTest extends TestCase
{
    use WithoutMiddleware;
    use CreatesSurveys;

    private string $endpoint = '/api/surveys/';

    /**
     * @test
     */
    public function itShowsASurvey(): void
    {
        [$survey, $questions, $options] = $this->createSurveyWithQuestionsAndOptions();

        $question = $questions->random();
        $option = $options->random();
        $this
            ->getJson($this->endpoint . $survey->id)
            ->assertSuccessful()
            ->assertJsonFragment([
                'id' => $survey->id,
                'name' => $survey->name,
                'description' => $survey->description,
            ])
            ->assertJsonFragment([
                'id' => $question->id,
                'survey_id' => $question->survey_id,
                'type' => $question->type,
                'question' => $question->question,
            ])
            ->assertJsonFragment([
                'id' => $option->id,
                'question_id' => $option->question_id,
                'value' => $option->value,
                'type' => $option->type,
            ]);
    }

    /**
     * @test
     */
    public function itReturnsNotFoundOnInexistentSurvey(): void
    {
        $this
            ->getJson($this->endpoint . rand())
            ->assertNotFound();
    }
}
