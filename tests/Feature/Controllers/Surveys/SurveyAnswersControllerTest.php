<?php

namespace Tests\Feature\Controllers\Surveys;

use App\Models\Client;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Tests\TestCase;
use Tests\Traits\CreatesSurveys;

class SurveyAnswersControllerTest extends TestCase
{
    use CreatesSurveys;
    use WithoutMiddleware;

    /**
     * Subject to test.
     */
    private string $endpoint = 'api/surveys/{id}/answers';

    /**
     * @test
     */
    public function itCreatesAnswers()
    {
        [$survey, $questions, $options] = $this->createSurveyWithQuestionsAndOptions(3);

        $answers = $questions
            ->map(fn ($question) => $question->options->random())
            ->pluck('id')
            ->map(fn ($id) => [
                'option_id' => $id,
                'other' => Arr::random([null, Str::random()])
            ]);
        $body = ['answers' => $answers];

        $this->actingAs(Client::factory()->create(), 'api');
        $this
            ->postJson(strtr($this->endpoint, ['{id}' => $survey->id]), $body)
            ->assertCreated();
    }
}
