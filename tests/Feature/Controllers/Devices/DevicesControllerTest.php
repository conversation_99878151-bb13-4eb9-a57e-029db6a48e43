<?php

namespace Tests\Feature\Controllers\Devices;

use App\Models\Device;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class DevicesControllerTest extends TestCase
{
    use WithoutMiddleware;

    /**
     * @test
     */
    public function itDeletesADevice(): void
    {
        $device = Device::factory()
            ->offline()
            ->create();

        $this->actingAs($device->client, 'api');
        $this
            ->deleteJson("/api/devices/{$device->device_id}")
            ->assertNoContent();
        $this->assertSoftDeleted($device);
    }

    /**
     * @test
     */
    public function itFailsToDeleteSomeoneElsesDevice(): void
    {
        $device1 = Device::factory()
            ->offline()
            ->create();
        $device2 = Device::factory()
            ->offline()
            ->create();

        $this->actingAs($device1->client, 'api');
        $this
            ->deleteJson("/api/devices/{$device2->device_id}")
            ->assertJsonValidationErrors(['id']);
    }

    /**
     * @test
     */
    public function itFailsToDeleteAnOnlineDevice(): void
    {
        $device = Device::factory()
            ->online()
            ->create();

        $this->actingAs($device->client, 'api');
        $this
            ->deleteJson("/api/devices/{$device->device_id}")
            ->assertJsonValidationErrors(['id']);
    }
}
