<?php

namespace Tests\Feature\Controllers\Devices;

use App\Models\Device;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Str;
use Tests\TestCase;

class VerificationChallengeControllerTest extends TestCase
{
    use WithoutMiddleware;

    /**
     * @test
     */
    public function itGeneratesANewChallenge(): void
    {
        $device = Device::factory()
            ->active()
            ->create();

        $response = $this
            ->putJson("/api/devices/{$device->device_id}/challenge")
            ->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'verification_challenge',
                ],
            ]);
    }

    /**
     * @test
     */
    public function itReturnsNotFoundOnInexistentDevice(): void
    {
        $id = Str::random();

        $this
            ->putJson("/api/devices/{$id}/challenge")
            ->assertNotFound();
    }
}
