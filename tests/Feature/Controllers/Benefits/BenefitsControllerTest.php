<?php

namespace Tests\Feature\Controllers\Benefits;

use App\Models\Benefit;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class BenefitsControllerTest extends TestCase
{
    use WithoutMiddleware;

    /**
     * @test
     */
    public function itReturnsAListOfBenefits(): void
    {
        $benefit = Benefit::factory()->create();

        $this
            ->getJson('/api/benefits')
            ->assertSuccessful()
            ->assertJsonFragment([
                'title' => $benefit->title,
                'description' => $benefit->description,
                'example' => $benefit->example,
                'longcode' => $benefit->longcode,
            ]);
    }

    /**
     * @test
     */
    public function itReturnsDescriptionAsMarkdown(): void
    {
        $description = '<h3>Some title</h3>';
        $benefit = Benefit::factory()->create(['description' => $description]);

        $this
            ->getJson('/api/benefits?format=markdown')
            ->assertSuccessful()
            ->assertJsonFragment(['description' => '### Some title']);
    }
}
