<?php

namespace Tests\Feature\Controllers;

use App\Models\ApiKeys;
use App\Models\MaintenanceModes;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class MaintenanceModeControllerTest extends TestCase
{
    use WithoutMiddleware;

    private const ENDPOINT = '/api/maintenancemode';

    private string $apiKeyCode;

    public function setUp(): void
    {
        parent::setUp();

        ApiKeys::factory()->create(['syncup' => ApiKeys::SYNC_UP]);

        $apiKey = ApiKeys::where(['syncup' => ApiKeys::SYNC_UP])->first();

        $this->apiKeyCode = bin2hex(bin2hex(base64_encode(base64_encode($apiKey->apikey))));
    }

    public function tearDown(): void
    {
        MaintenanceModes::truncate();

        parent::tearDown();
    }

    #[Test]
    public function response_when_app_is_available(): void
    {
        $response = $this->post(self::ENDPOINT);

        $response
            ->assertOk()
            ->assertJson([
                'result' => 'Ok',
                'apikey' => $this->apiKeyCode,
                'status' => Response::HTTP_OK,
                'code' => 0
            ]);
    }

    #[Test]
    public function response_when_app_is_under_maintenance(): void
    {
        $maintenance = MaintenanceModes::factory()->create([
            'date_time_maintenance_start' => now(),
            'date_time_maintenance_end' => now()->addHour()
        ]);

        $response = $this->post(self::ENDPOINT);

        $response
            ->assertServiceUnavailable()
            ->assertJson([
                'result' => $maintenance->client_message,
                'apikey' => $this->apiKeyCode,
                'status' => Response::HTTP_SERVICE_UNAVAILABLE,
                'code' => 1
            ]);
    }
}
