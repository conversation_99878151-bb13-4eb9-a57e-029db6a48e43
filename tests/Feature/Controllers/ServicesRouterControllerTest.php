<?php

namespace Tests\Feature\Controllers;

use Exception;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;
use App\Services\Log;
use Illuminate\Http\Request;
use App\Factory\ClientFactory;
use App\Helpers\CustomHandler;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\MockObject\MockObject;
use App\Http\Controllers\API\ServicesRouter\ServicesRouterController;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;

class ServicesRouterControllerTest extends TestCase
{
    use WithoutMiddleware;
    use WithFaker;

    private ClientFactory|MockObject $clientFactory;

    private Log|MockObject $log;

    private CustomHandler|MockObject $exceptionHandler;

    private ServicesRouterController $controller;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var ClientFactory */
        $this->clientFactory = $this->createMock(ClientFactory::class);

        /** @var Log */
        $this->log = $this->createMock(Log::class);

        /** @var CustomHandler */
        $this->exceptionHandler = $this->createMock(CustomHandler::class);

        $this->controller = new ServicesRouterController(
            $this->clientFactory,
            $this->log,
            $this->exceptionHandler
        );
    }

    #[Test]
    public function successful_response_service()
    {
        $request = Request::create('/api/test', 'GET', ['parameters' => []]);

        $responseData = [
            'status' => Response::HTTP_OK,
            'content' => ['foo' => 'bar']
        ];

        $this->clientFactory->expects($this->once())
            ->method('customeResponse')
            ->willReturn($responseData);

        $response = $this->controller->getServices($request);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $this->assertEquals(['foo' => 'bar'], $response->getOriginalContent());
    }

    #[Test]
    public function baes_401_overrides_status()
    {
        $request = Request::create('/api/test', 'GET', ['parameters' => []]);

        $responseData = [
            'status' => Response::HTTP_OK,
            'content' => [
                'Status' => [
                    'RequestStatus' => [
                        'Code' => Response::HTTP_UNAUTHORIZED
                    ],
                ]
            ]
        ];

        $this->clientFactory->expects($this->once())
            ->method('customeResponse')
            ->willReturn($responseData);

        $this->log->expects($this->once())
            ->method('debug');

        $response = $this->controller->getServices($request);

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    #[Test]
    public function exception_handling()
    {
        $request = Request::create('/api/test', 'GET', ['parameters' => []]);

        $exception = new Exception('Test exception');

        $this->clientFactory->expects($this->once())
            ->method('customeResponse')
            ->willThrowException($exception);

        $this->log->expects($this->once())
            ->method('error');

        $this->exceptionHandler->expects($this->once())
            ->method('exceptionHandler')
            ->with($exception)
            ->willReturn(response()->json(['error' => 'handled'], Response::HTTP_INTERNAL_SERVER_ERROR));

        $response = $this->controller->getServices($request);

        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());

        $this->assertEquals(['error' => 'handled'], $response->getOriginalContent());
    }
}
