<?php

namespace Tests\Feature\Controllers\Clients;

use App\Enums\Message\Generate;
use Tests\TestCase;
use App\Models\Client;
use App\Models\PerfilImages;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use App\Http\Resources\Client\ClientFullProfileResource;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\TestWith;
use Symfony\Component\HttpFoundation\Response;

class ClientProfileControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();

        $this->client->profileImages()->createMany([
            PerfilImages::factory()->make(['type' => 'BackgroundImage'])->toArray(),
            PerfilImages::factory()->make(['type' => 'ProfileImage'])->toArray()
        ]);

        $this->actingAs($this->client, 'api');
    }

    public function testItReturnsTheClientProfile(): void
    {
        $this->client->load('profileImages');

        $expectedResult = (new ClientFullProfileResource($this->client))
            ->response()
            ->getData(true)['data'];

        $this->get('/api/profile')
            ->assertOk()
            ->assertExactJson($expectedResult);
    }

    public function testItCatchesExceptionsWhenAnErrorOccurs(): void
    {
        $this->client->delete();

        $this->get('/api/profile')
            ->assertServerError()
            ->assertJsonFragment([
                'message' => Generate::FAIL_SERVER->value,
                'code' => 5000
            ]);
    }
}
