<?php

namespace Tests\Feature\Controllers\Clients;

use Exception;
use Tests\TestCase;
use App\Models\Client;
use App\Enums\Message\Generate;
use PHPUnit\Framework\Attributes\Test;
use App\Http\Resources\ClientDeviceResource;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class ClientDeviceControllerTest extends TestCase
{
    use WithoutMiddleware;

    private const ENDPOINT = '/api/deviceslist';

    private Client $client;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()
            ->hasDevices(2)
            ->create();
    }

    #[Test]
    public function returns_client_devices(): void
    {
        $this->actingAs($this->client, 'api');

        $clientDevices = $this->client
            ->devices()
            ->select(
                'device_id',
                'device_name',
                'ultimate_latitud',
                'ultimate_longitud',
                'created_at as ultimate_conexion',
                'online'
            )
            ->get();

        $expectedResponse = (new ClientDeviceResource($clientDevices))
            ->response()
            ->getData(assoc: true);

        $response = $this->get(self::ENDPOINT);

        $response
            ->assertOk()
            ->assertJson($expectedResponse);
    }

    #[Test]
    public function catches_unexpected_exceptions(): void
    {
        $this->actingAs($this->client, 'api');

        $client = $this->mock(Client::class, function ($mock) {
            $mock
                ->shouldReceive('devices')
                ->once()
                ->andThrow(new Exception());
        });

        $this->mock('auth', function ($mock) use ($client) {
            $mock->shouldReceive('guard->user')->andReturn($client);
        });

        $response = $this->get(self::ENDPOINT);

        $response
            ->assertInternalServerError()
            ->assertJson([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => Generate::FAIL_SERVER->value,
                'code' => 5000,
            ]);
    }
}
