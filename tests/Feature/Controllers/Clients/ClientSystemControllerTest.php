<?php

namespace Tests\Feature\Controllers\Clients;

use App\Enums\Message\Generate;
use App\Helpers\CustomHandler;
use App\Http\Responses\Auth\ClientResponse;
use App\Models\Client;
use App\Models\Systems;
use App\Services\ClientService;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\TestWith;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class ClientSystemControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    private Systems $system;

    private ClientResponse $clientResponse;

    public function setUp(): void
    {
        parent::setUp();

        $this->clientResponse = app()->make(ClientResponse::class);

        $this->client = Client::factory()->create();

        $this->system = Systems::factory()->create();

        $this->actingAs($this->client, 'api');
    }

    public function testItCanAddClientToSystem(): void
    {
        $response = $this->postJson('/api/addclientsystem', [
            'ClientId' => $this->client->client_id,
            'SystemCode' => $this->system->system_code,
        ]);

        $expectedResponse = $this->clientResponse->successfullyAddedToSystem();

        $response
            ->assertStatus($status = Response::HTTP_CREATED)
            ->assertJson($expectedResponse->getData(true));

        $this->assertTrue($this->client->systems->contains($this->system));
    }

    public function testItCanRemoveClientFromSystem(): void
    {
        $response = $this->postJson('/api/removeclientsystem', [
            'ClientId' => $this->client->client_id,
            'SystemCode' => $this->system->system_code,
        ]);

        $expectedResponse = $this->clientResponse->successfullyRemovedFromSystem();

        $response
            ->assertStatus($status = Response::HTTP_ACCEPTED)
            ->assertJson($expectedResponse->getData(true));

        $this->assertFalse($this->client->systems->contains($this->system));
    }

    #[TestWith(['addclientsystem'])]
    #[TestWith(['removeclientsystem'])]
    #[TestDox('It returns bad request on $endpoint endpoint when client not found')]
    public function testItReturnsBadRequestWhenClientNotFound(string $endpoint): void
    {
        $response = $this->postJson("/api/{$endpoint}", [
            'ClientId' => 'invalid-client-id',
            'SystemCode' => $this->system->system_code,
        ]);

        $expectedResponse = $this->clientResponse->clientSystemNotFound();

        $response
            ->assertStatus($status = Response::HTTP_BAD_REQUEST)
            ->assertJson($expectedResponse->getData(true));
    }

    #[TestWith(['addclientsystem'])]
    #[TestWith(['removeclientsystem'])]
    #[TestDox('It returns bad request on $endpoint endpoint when system not found')]
    public function testItCatchesExceptionWhenAnErrorOccurs(string $endpoint): void
    {
        $code = 5000;
        $errorMesage = Generate::FAIL_SERVER->value;

        $this->mock(ClientService::class, function ($mock) use ($code, $errorMesage) {
            $mock->shouldReceive('clientBySystemId')
                ->once()
                ->withAnyArgs()
                ->andThrow(new \Exception($errorMesage, $code));
        });

        $this->mock(CustomHandler::class, function ($mock) use ($code, $errorMesage) {
            $mock->shouldReceive('exceptionHandler')
                ->once()
                ->withAnyArgs()
                ->andReturn(response()->json([
                    'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                    'code' => $code,
                    'message' => $errorMesage,
                ], Response::HTTP_INTERNAL_SERVER_ERROR));
        });

        $response = $this->postJson("/api/{$endpoint}", [
            'ClientId' => $this->client->client_id,
            'SystemCode' => 'invalid-system-code',
        ]);

        $response
            ->assertStatus($status = Response::HTTP_INTERNAL_SERVER_ERROR)
            ->assertJson([
                'code' => $code,
                'message' => Generate::FAIL_SERVER->value,
                'status' => $status,
            ]);
    }
}
