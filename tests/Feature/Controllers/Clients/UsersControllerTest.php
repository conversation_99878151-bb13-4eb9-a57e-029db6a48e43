<?php

namespace Tests\Feature\Controllers\Clients;

use App\Events\NewUserRegistrationRequestEvent;
use App\Events\UserDeletedEvent;
use App\Events\UserRegisteredEvent;
use App\Repos\GetCustomerContactChannelsRepo;
use App\Services\Dui;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Http\Client\Factory as Http;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http as HttpFacade;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Providers\CustomerProvider;
use Tests\TestCase;
use Tests\Traits\CreatesClients;

class UsersControllerTest extends TestCase
{
    use WithoutMiddleware;
    use CreatesClients;

    /**
     * Subject to test.
     */
    private string $endpoint = '/api/users';

    /**
     * External customer data endpoint to mock.
     */
    private string $externalCustomerDataEndpoint;

    /**
     * External customer contacts endpoint to mock.
     */
    private string $externalCustomerContactsEndpoint;

    /**
     * Http client instance.
     */
    protected Http $http;

    /**
     * DUI generator.
     */
    protected Dui $duis;

    protected function setUp(): void
    {
        parent::setUp();

        $this->http = HttpFacade::getFacadeRoot();
        // Temporal binding for testing.
        App::instance(Http::class, $this->http);

        $this->externalCustomerDataEndpoint = config('services.baes.base_url') . 'CustomerData*';
        $this->externalCustomerContactsEndpoint = config('services.baes.base_url') . 'CustomerContacts*';
        $this->duis = App::make(Dui::class);
    }

    /**
     * @test
     */
    public function itFailsToRegisterOnMissingData(): void
    {
        $this
            ->postJson($this->endpoint, [])
            ->assertUnprocessable()
            ->assertJsonValidationErrors(['customer_id', 'creditcard_application_id']);
    }

    /**
     * @test
     */
    public function itRegistersANewUser(): void
    {
        $creditcardApplicationId = random_int(5000, 5999);

        $customerData = CustomerProvider::getValidExternalCustomerData();
        $customerId = data_get($customerData, 'CustomerId');

        $contactChannels = [
            CustomerProvider::getValidExternalCustomerContactChannel(GetCustomerContactChannelsRepo::PHONE_TYPE_CODE),
            CustomerProvider::getValidExternalCustomerContactChannel(GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE)
        ];

        $this->http->fake([
            $this->externalCustomerDataEndpoint => $this->http->response([
                'Data' => $customerData
            ], Response::HTTP_OK),
            $this->externalCustomerContactsEndpoint => $this->http->response([
                'Data' => [
                    'ContactTypes' => $contactChannels,
                ],
            ], Response::HTTP_OK),
        ]);

        Event::fake()->except(
            NewUserRegistrationRequestEvent::class,
        );

        $response = $this->postJson($this->endpoint, [
            'customer_id' => $customerId,
            'creditcard_application_id' => $creditcardApplicationId,
        ]);

        $response->assertCreated();

        $this->assertDatabaseHas('clients', [
            'client_id' => $customerId,
            'creditcard_application_id' => $creditcardApplicationId
        ]);

        Event::assertDispatched(UserRegisteredEvent::class);
    }

    /**
     * @test
     */
    public function itDeletesAUser(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        Event::fake();

        $this
            ->delete($this->endpoint . '/' . $user->id)
            ->assertNoContent();

        $this->assertDatabaseMissing('clients', [
            'client_id' => $user->client_id,
        ]);

        Event::assertDispatched(UserDeletedEvent::class);
    }

    /**
     * @test
     */
    public function itDeletesAUserByQuery(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        $this
            ->delete($this->endpoint . '?' . "id={$user->id}")
            ->assertSuccessful()
            ->assertJsonFragment([
                'id' => $user->id,
                'customer_id' => $user->client_id,
                'dui' => $user->dui,
            ]);

        $this->assertDatabaseMissing('clients', [
            'client_id' => $user->client_id,
        ]);
    }

    /**
     * @test
     */
    public function itDeletesMultipleUsersByQuery(): void
    {
        $users = collect();
        foreach (range(1, 10) as $_) {
            $users->push($this->createClientWithSystemAndPassword());
        }
        $deletedDuis = $users->pluck('dui');

        $byId = 'id[]=' . $users->pop()->id . '&id[]=' . $users->pop()->id;
        $byCustomerId = 'customer_id=' . $users->pop()->client_id;
        $users->push($users->random());
        $byDui = 'dui[]=' . $users->pluck('dui')->join('&dui[]=');
        $endpoint = $this->endpoint . '?' . collect([$byId, $byCustomerId, $byDui])->join('&');

        $response = $this
            ->delete($endpoint)
            ->assertSuccessful();

        foreach ($deletedDuis as $dui) {
            $response->assertJsonFragment(['dui' => $dui]);
            $this->assertDatabaseMissing('clients', ['dui' => $dui]);
        }
    }

    protected function tearDown(): void
    {
        App::forgetInstance($this->http::class);
    }
}
