<?php

namespace Tests\Feature\Controllers\Clients;

use App\Models\Client;
use App\Services\Dui;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class DuisControllerTest extends TestCase
{
    use WithoutMiddleware;

    /**
     * @test
     */
    public function itConfirmsAClientByDui(): void
    {
        $user = Client::factory()->create();

        $this
            ->getJson("/api/duis/{$user->dui}")
            ->assertSuccessful()
            ->assertHeader('Content-Type', 'application/json');
    }

    /**
     * @test
     */
    public function itReturnsNotFoundOnInexistentDui(): void
    {
        $dui = app(Dui::class)->generate();

        $this
            ->getJson("/api/duis/{$dui}")
            ->assertNotFound();
    }
}
