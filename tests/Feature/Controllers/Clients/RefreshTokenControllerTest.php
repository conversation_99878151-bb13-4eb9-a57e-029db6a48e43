<?php

namespace Tests\Feature\Controllers\Clients;

use App\Exceptions\Auth\InvalidRefreshToken;
use App\Models\Client;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Http\Client\Factory as Http;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http as HttpFacade;
use Illuminate\Support\Str;
use Tests\TestCase;

class RefreshTokenControllerTest extends TestCase
{
    use WithoutMiddleware;

    /**
     * Subject to test.
     */
    private string $endpoint = '/api/token/user';

    /**
     * External endpoint to mock.
     */
    private string $externalEndpoint;

    /**
     * Http client instance.
     */
    protected Http $http;

    protected function setUp(): void
    {
        parent::setUp();

        $this->http = HttpFacade::getFacadeRoot();
        // Temporal binding for testing.
        App::instance(Http::class, $this->http);

        $this->externalEndpoint = config('services.baes.base_url') . 'TokenAuthorization';
    }

    /**
     * @test
     */
    public function itRefreshesAToken(): void
    {
        // Set up.
        $user = Client::factory()
            ->withBaesToken()
            ->create();
        $deviceId = 'some_device';

        $newToken = Str::random();
        $newRefreshToken = Str::random();

        $this->http->fake([
            $this->externalEndpoint => $this->http->response(
                [
                    'Data' => [
                        'TokenAuthorization' => $newToken,
                        'RefreshTokenAuthorization' => $newRefreshToken,
                    ],
                    'Status' => [
                        'ResponseStatus' => [
                            'Code' => 0,
                        ],
                    ],
                ],
                200,
            )
        ]);

        // Run.
        $response = $this->putJson($this->endpoint, [
            'refresh_token' => $user->baes_refresh_token,
            'device_id' => $deviceId,
        ]);

        // Assert.
        $response
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'token' => $newToken,
                    'refresh_token' => $newRefreshToken,
                ],
            ]);

        $user->refresh();
        $this->assertSame($newToken, $user->baes_token);
        $this->assertSame($newRefreshToken, $user->baes_refresh_token);
    }

    /**
     * @test
     */
    public function itReturnsUnauthorizedWhenRefreshTokenIsInvalidOrTimedOut(): void
    {
        // Set up.
        $user = Client::factory()
            ->withBaesToken()
            ->create();
        $deviceId = 'some_device';

        $this->http->fake([
            $this->externalEndpoint => $this->http->response(
                [
                    'Data' => null,
                    'Status' => [
                        'ResponseStatus' => [
                            'Code' => 2102,
                            'Message' => 'Invalid Token',
                        ],
                    ],
                ],
                200,
            )
        ]);

        // Run.
        $response = $this->putJson($this->endpoint, [
            'refresh_token' => $user->baes_refresh_token,
            'device_id' => $deviceId,
        ]);

        // Assert.
        $response
            ->assertBadRequest()
            ->assertJson(['error_code' => InvalidRefreshToken::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itReturnsServerErrorOnUnknownError(): void
    {
        // Set up.
        $user = Client::factory()
            ->withBaesToken()
            ->create();
        $deviceId = 'some_device';

        $this->http->fake([
            $this->externalEndpoint => $this->http->response([], 200)
        ]);

        // Run.
        $response = $this->putJson($this->endpoint, [
            'refresh_token' => $user->baes_refresh_token,
            'device_id' => $deviceId,
        ]);

        // Assert.
        $response->assertInternalServerError();
    }

    protected function tearDown(): void
    {
        App::forgetInstance($this->http::class);
    }
}
