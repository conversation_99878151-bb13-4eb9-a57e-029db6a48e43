<?php

namespace Tests\Feature\Controllers\Clients;

use Tests\TestCase;
use App\Services\Dui;
use App\Models\Client;
use App\Factory\ClientFactory;
use App\Enums\Message\Generate;
use Illuminate\Support\Facades\App;
use Tests\Support\GuzzleClientMock;
use App\DTO\ExternalRequestResponse;
use App\Enums\Code\ClientPhoneValidationCode;
use App\Repos\GetCustomerContactChannelsRepo;
use GuzzleHttp\Psr7\Response as Psr7Response;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class ClientsControllerTest extends TestCase
{
    use WithoutMiddleware;

    /**
     * Subject to test.
     */
    private string $endpoint = '/api/validateuser';

    /**
     * DUI generator service.
     */
    private Dui $duis;

    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->duis = App::make(Dui::class);

        $this->client = Client::factory()->create();

        $this->actingAs($this->client, 'api');
    }

    public function testItValidatesClientAgainstExternalDatabase(): void
    {
        $contactInformationData = [
            'ContactTypes' => [
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::PHONE_TYPE_CODE,
                    'CustomerContacts' => [
                        ['ContactValue' => $this->client->phone_number],
                    ],
                ],
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE,
                    'CustomerContacts' => [
                        ['ContactValue' => $this->client->email],
                    ],
                ],
            ]
        ];

        $expectedResponse = new ExternalRequestResponse(
            data: $contactInformationData
        );

        $guzzleClient = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($expectedResponse->toResponseArray()))
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleClient);

        $response = $this->post($this->endpoint, [
            'dui' => $this->client->dui,
            'phone_number' => $this->client->phone_number,
        ]);

        $response
            ->assertOk()
            ->assertJsonFragment([
                'customer_id' => (string) $this->client->client_id,
                'creditcard_application_id' => $this->client->creditcard_application_id
            ]);
    }

    public function testItValidatesIfNotContactInformationIsFound(): void
    {
        $contactInformationData = [
            'ContactTypes' => [
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::PHONE_TYPE_CODE,
                    'CustomerContacts' => [],
                ],
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE,
                    'CustomerContacts' => [],
                ]
            ]
        ];

        $expectedResponse = new ExternalRequestResponse(
            data: $contactInformationData
        );

        $guzzleClient = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($expectedResponse->toResponseArray()))
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleClient);

        $response = $this->post($this->endpoint, [
            'dui' => $this->client->dui,
            'phone_number' => $this->client->phone_number,
        ]);

        $response
            ->assertOk()
            ->assertJsonFragment([
                'result' => Generate::NOT_MORE_DATA->value,
                'status' => Response::HTTP_OK,
                'code' => ClientPhoneValidationCode::SUCCESS->value
            ]);
    }

    public function testItReturnsNotFoundIfClientWithDuiCouldNotBeFound(): void
    {
        $response = $this->post($this->endpoint, [
            'dui' => $this->duis->generate(),
            'phone_number' => $this->client->phone_number,
        ]);

        $response
            ->assertNotFound()
            ->assertJsonFragment([
                'result' => Generate::DUI_FAIL->value,
                'status' => Response::HTTP_NOT_FOUND,
                'code' => ClientPhoneValidationCode::DUI_NOT_FOUND->value
            ]);
    }

    public function testItFailsIfPhoneNumbersDoNotMatch(): void
    {
        $contactInformationData = [
            'ContactTypes' => [
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::PHONE_TYPE_CODE,
                    'CustomerContacts' => [
                        ['ContactValue' => '1234567890'],
                    ],
                ],
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE,
                    'CustomerContacts' => [
                        ['ContactValue' => $this->client->email],
                    ],
                ],
            ]
        ];

        $expectedResponse = new ExternalRequestResponse(
            data: $contactInformationData
        );

        $guzzleClient = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($expectedResponse->toResponseArray()))
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleClient);

        $response = $this->post($this->endpoint, [
            'dui' => $this->client->dui,
            'phone_number' => $this->client->phone_number,
        ]);

        $response
            ->assertStatus(Response::HTTP_BAD_REQUEST)
            ->assertJsonFragment([
                'result' => Generate::NUMBER_INCORRECT_TRY_AGAIN->value,
                'status' => Response::HTTP_BAD_REQUEST,
                'code' => ClientPhoneValidationCode::INVALID_PHONE_NUMBER->value
            ]);
    }

    public function testValidateUserCatchesExceptionsIfThrown(): void
    {
        $this->mock(ClientFactory::class, function ($mock) {
            $mock->shouldReceive('customeResponse')
                ->once()
                ->withAnyArgs()
                ->andThrow(new \Exception('An error occurred'));
        });

        $response = $this->post($this->endpoint, [
            'dui' => $this->client->dui,
            'phone_number' => $this->client->phone_number,
        ]);

        $response
            ->assertInternalServerError()
            ->assertJsonFragment([
                'result' => Generate::FAIL_SERVER->value,
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'code' => ClientPhoneValidationCode::INTERNAL_SERVER_FAILURE->value,
                'message' => 'An error occurred'
            ]);
    }
}
