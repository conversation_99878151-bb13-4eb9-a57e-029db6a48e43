<?php

namespace Tests\Feature\Controllers\Clients;

use Tests\TestCase;
use App\Models\Client;
use App\Enums\Code\Status;
use App\Models\NicknameLog;
use Illuminate\Support\Str;
use App\Factory\ClientFactory;
use App\Enums\Message\Generate;
use App\Services\ClientService;
use App\Http\Responses\ApiResponse;
use Tests\Support\GuzzleClientMock;
use App\DTO\ExternalRequestResponse;
use GuzzleHttp\Psr7\Response as Psr7Response;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use App\Http\Controllers\API\Clients\ClientNicknameFaceAuthController;
use PHPUnit\Framework\MockObject\MockObject;

class ClientNicknameFaceAuthenticationControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();

        $this->actingAs($this->client, 'api');
    }

    public function testFaceAuthenticationForNickname(): void
    {
        $nicknameLog = NicknameLog::create([
            'key' => Str::random(),
            'client_id' => $this->client->id,
            'faceauthentication' => Generate::NEGATIVE_FACE_BASE,
        ]);

        $expectedResponse = new ExternalRequestResponse(
            requestStatusMessage: Generate::succes->value,
            responseStatusCode: Status::codeExternalFind->value
        );

        $guzzleClient = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($expectedResponse->toResponseArray()))
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleClient);

        $mockedController = $this->createMockedController($guzzleClient);

        $this->app->instance(ClientNicknameFaceAuthController::class, $mockedController);

        $response = $this->post('/api/FaceAuthenticationForNickname', [
            'Key' => $nicknameLog->key
        ]);

        $response
            ->assertOk()
            ->assertJsonFragment($expectedResponse->toResponseArray());

        $this->assertDatabaseHas('nickname_log', [
            'client_id' => $this->client->id,
            'faceauthentication' => Generate::POSITIVE_FACE_BASE->value
        ]);
    }

    public function testItReturnsUnauthorizedWhenNicknameLogIsNotFound(): void
    {
        $expectedResponse = new ExternalRequestResponse(
            requestStatusCode: Response::HTTP_OK,
            requestStatusMessage: Generate::succes->value,
            responseStatusCode: Response::HTTP_UNAUTHORIZED,
            responseStatusMessage: Generate::INVALED_KEY->value
        );

        $guzzleClient = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($expectedResponse->toResponseArray()))
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleClient);

        $response = $this->post('/api/FaceAuthenticationForNickname', [
            'Key' => 'WRONG_KEY'
        ]);

        $response
            ->assertOk()
            ->assertJsonFragment($expectedResponse->toResponseArray());
    }

    private function createMockedController($guzzleClient): MockObject
    {
        $mockedController = $this->getMockBuilder(ClientNicknameFaceAuthController::class)
            ->setConstructorArgs([
                $guzzleClient,
                $this->app->make(ClientService::class),
                $this->app->make(ApiResponse::class)
            ])
            ->onlyMethods(['logBank'])
            ->getMock();

        $mockedController
            ->expects($this->once())
            ->method('logBank');

        return $mockedController;
    }
}
