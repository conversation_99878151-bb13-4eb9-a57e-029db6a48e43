<?php

namespace Tests\Feature\Controllers\Clients;

use App\Enums\Code\ClientCredentialCode;
use Exception;
use Tests\TestCase;
use App\Models\Client;
use App\Enums\Message\Generate;
use App\Services\ClientService;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\TestWith;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class ClientCredentialsControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    private const VALID_RESULTS_STRUCTURE = [
        'result',
        'status',
        'code',
        'message',
    ];

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();

        $this->actingAs($this->client, 'api');
    }

    #[TestWith(['unlock', 'CLIENT_ID'])]
    #[TestWith(['unlock', 'DUI'])]
    #[TestWith(['lock', 'CLIENT_ID'])]
    #[TestWith(['lock', 'DUI'])]
    #[TestDox('Test can $action credentials using $identifier')]
    public function testCanLockOrUnlockCredentials(string $action, string $identifier): void
    {
        $user = $this->client;

        $response = $this->post("/api/{$action}client", [
            'IDENTIFIER' => $identifier,
            'VALUE' => $identifier === 'DUI' ? strval($user->dui) : strval($user->client_id),
        ]);

        $response
            ->assertOk()
            ->assertJsonStructure(self::VALID_RESULTS_STRUCTURE);

        $this->assertDatabaseHas('clients', [
            'id' => $this->client->id,
            'status' => $action === 'unlock'
                ? Client::ENABLE
                : Client::DISABLE,
        ]);
    }

    #[TestWith(['unlock'])]
    #[TestWith(['lock'])]
    #[TestDox('Test $action clients credentials using wrong identifier fails')]
    public function testLockAndUnlockUserCredentialsUsingWrongIdentifierFails(string $action): void
    {
        $response = $this->post("/api/{$action}client", [
            'IDENTIFIER' => 'WRONG_IDENTIFIER',
            'VALUE' => $this->client->dui,
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'code' => ClientCredentialCode::INVALID_IDENTFIER->value,
                'result' => 'Uno o más campos son requeridos'
            ]);
    }

    #[TestWith(['lock', 'CLIENT_ID', 'No se encontró cliente con :value.'])]
    #[TestWith(['lock', 'DUI', 'No se encontró cliente con :value.'])]
    #[TestWith(['unlock', 'CLIENT_ID', 'No se encontró cliente con :value.'])]
    #[TestWith(['unlock', 'DUI', 'No se encontró cliente con :value.'])]
    #[TestDox('Test $action clients credentials with $identifier when user cannot be found fails')]
    public function testlockAndUnlockUserCredentialsWhenUserCannotBeFoundFails(string $action, string $identifier, $expectedMessage): void
    {
        $wrongUser = Client::factory()->make();

        $identifierString = $identifier === 'DUI' ? "DUI:$wrongUser->dui" : "CLIENT_ID:$wrongUser->client_id";

        $response = $this->post("/api/{$action}client", [
            'IDENTIFIER' => $identifier,
            'VALUE' => $identifier === 'DUI' ? strval($wrongUser->dui) : strval($wrongUser->client_id),
        ]);

        $response
            ->assertNotFound()
            ->assertJson([
                'code' => $identifier === 'DUI'
                    ? ClientCredentialCode::CLIENT_DUI_NOT_FOUND->value
                    : ClientCredentialCode::CLIENT_ID_NOT_FOUND->value,
                'result' => str_replace(':value', $identifierString, $expectedMessage),
            ]);
    }

    #[TestWith(['lock'])]
    #[TestWith(['unlock'])]
    #[TestDox('Test $action clients credentials catches exception when an error occurs')]
    public function testLockAndUnlockCatchesExceptionWhenAnErrorOccurs(string $action): void
    {
        $user = $this->client;

        $exceptionMessage = 'Error message';

        $this->mock(ClientService::class, function ($mock) use ($exceptionMessage) {
            $mock->shouldReceive('getClientFromRequest')
                ->once()
                ->andThrow(new Exception($exceptionMessage));
        });

        $response = $this->post("/api/{$action}client", [
            'IDENTIFIER' => 'DUI',
            'VALUE' => $user->dui,
        ]);

        $response
            ->assertServerError()
            ->assertJsonFragment([
                'code' => ClientCredentialCode::INTERNAL_SERVER_FAILURE->value,
                'result' => Generate::FAIL_SERVER->value,
                'message' => $exceptionMessage,
            ]);
    }

    public function testCanLockClientByFaceAuth(): void
    {
        $response = $this->post('/api/lockbyfaceauth');

        $response
            ->assertOk()
            ->assertJsonFragment([
                'result' => Generate::CREDENTIAL_FAIL->value,
                'code' => ClientCredentialCode::SUCCESS->value
            ]);

        $this->assertDatabaseHas('clients', [
            'id' => $this->client->id,
            'status' => Client::BLOCK_BY_FACE_AUTH,
        ]);
    }

    public function testLockingClientByFaceAuthCatchesExceptionWhenAnErrorOccurs(): void
    {
        $this->mock(ClientService::class, function ($mock) {
            $mock->shouldReceive('lockClientByFaceAuth')
                ->once()
                ->andThrow(new Exception());
        });

        $response = $this->post('/api/lockbyfaceauth');

        $response
            ->assertServerError()
            ->assertJsonFragment([
                'code' => ClientCredentialCode::FAIL->value,
                'result' => Generate::FAIL_SERVER->value,
            ]);
    }
}
