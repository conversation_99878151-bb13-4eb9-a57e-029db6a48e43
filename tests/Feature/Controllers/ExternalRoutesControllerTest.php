<?php

namespace Tests\Feature\Controllers;

use App\Http\Controllers\API\ExternalRoutesController;
use App\Repos\DummyGenerateTokenRepo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Mockery;
use ReflectionObject;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class ExternalRoutesControllerTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itReturnsNotFoundOnHalfDefinedRoute(): void
    {
        $resource = Str::random();
        $path = 'api/' . $resource;
        Route::addRoute('GET', $path, [ExternalRoutesController::class, $resource]);

        $this
            ->getJson($path)
            ->assertNotFound();
    }

    /**
     * @test
     */
    public function itCallsTheCorrectRepo(): void
    {
        // Set up.

        // Test route.
        $resource = Str::random();
        $path = 'api/' . $resource;
        Route::addRoute('POST', $path, [ExternalRoutesController::class, $resource]);

        // Mocked repo.
        $body = ['some_key' => Str::random()];
        $responseBody = ['some_key' => Str::random()];
        $someHeader = Str::random();
        $extraHeaders = ['some-header' => $someHeader];
        // No special reason for this particular repo, just a base is needed.
        $repo = Mockery::mock(DummyGenerateTokenRepo::class);
        $repo
            ->shouldReceive('prepare')
            ->with($body)
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('addHeaders')
            ->with(Mockery::on(fn ($headers) => Arr::get($headers, 'some-header') === $someHeader))
            ->once()
            ->andReturn($repo);
        $repo
            ->shouldReceive('fetchRaw')
            ->once()
            ->andReturn($responseBody);
        $repo
            ->shouldReceive('getStatus')
            ->once()
            ->andReturn(200);
        App::instance(get_class($repo), $repo);

        // Modified controller.
        $controller = App::make(ExternalRoutesController::class);
        $mirroredController = new ReflectionObject($controller);
        $repos = $mirroredController->getProperty('repos');
        $repos->setAccessible(true);
        $reposWithMock = $repos->getValue($controller);
        $reposWithMock[$resource] = get_class($repo);
        $repos->setValue($controller, $reposWithMock);
        App::instance(ExternalRoutesController::class, $controller);

        // Run.
        $response = $this
            ->withHeaders($extraHeaders)
            ->postJson($path, $body);

        // Assert.
        $response
            ->assertSuccessful()
            ->assertJsonFragment($responseBody);
    }
}
