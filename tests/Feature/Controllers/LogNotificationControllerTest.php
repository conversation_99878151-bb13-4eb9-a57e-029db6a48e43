<?php

namespace Tests\Feature\Controllers;

use App\Models\NotificationsTemplates;
use Tests\TestCase;
use App\Models\Client;
use Illuminate\Support\Str;
use App\Enums\Message\Generate;
use App\Services\NotifyService;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class LogNotificationControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();
    }

    #[Test]
    public function can_save_firebase_token(): void
    {
        $response = $this->post(
            uri: '/api/savefirebasetoken',
            data: $data = [
                "ccapplication_id" => $this->client->creditcard_application_id,
                "token" => Str::random(),
                "huawei" => false,
            ]
        );

        $response
            ->assertOk()
            ->assertJson([
                'result' => Generate::RESULT_OK->value,
                'status' => Response::HTTP_OK
            ]);

        $this->assertDatabaseHas('log_notifications', $data);
    }

    #[Test]
    public function notificationTemplateIsNotFount(): void
    {
        $response = $this->post('/api/sendnotification');

        $response
            ->assertBadRequest()
            ->assertJson([
                'result' => Generate::TEMPLATE_NOT_FOUND->value,
                'status' => Response::HTTP_BAD_REQUEST,
                'Firebase_response' => "",
                'Huawei_response' => "",
            ]);
    }

    #[Test]
    public function canSendNotification(): void
    {
        $expectedResponse = [
            'result' => Generate::NOTIFICATE_SEND->value,
            'Firebase_response' => 'success',
            'Huawei_response' => 'success',
            'status' => Response::HTTP_OK,
        ];

        $this->mock(NotifyService::class, function ($mock) use ($expectedResponse) {
            $mock
                ->shouldReceive('sendNotificationCustom')
                ->once()
                ->withAnyArgs()
                ->andReturn([$expectedResponse['Firebase_response'], $expectedResponse['Huawei_response']]);
        });

        $template = new NotificationsTemplates();
        $template->title = "Test Template";
        $template->content = "[Name] this is a test.";
        $template->status = true;
        $template->save();

        $variables = [
            [
                'Key' => 'Name',
                'Value' => $this->client->full_name
            ]
        ];

        $response = $this->post('/api/sendnotification', [
            "template_id" => $template->id,
            "type" => 'SOLICITUD',
            "id" => $this->client->client_id,
            "variables" => $variables,
            "keys_deeplink" => $variables
        ]);

        $response
            ->assertOk()
            ->assertJson($expectedResponse);
    }
}
