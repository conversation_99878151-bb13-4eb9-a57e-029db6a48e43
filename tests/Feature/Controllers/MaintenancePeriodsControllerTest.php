<?php

namespace Tests\Feature\Controllers;

use App\Models\MaintenanceModes as MaintenancePeriod;
use Illuminate\Support\Str;
use Tests\TestCase;

class MaintenancePeriodsControllerTest extends TestCase
{
    private string $endpoint = '/api/maintenance-periods';

    /**
     * @test
     */
    public function itReturnsEmptyOnNoMaintenance(): void
    {
        $this
            ->getJson($this->endpoint)
            ->assertStatus(204);
    }

    /**
     * @test
     */
    public function itReturnsAMessageOnActiveMaintenance(): void
    {
        $message = Str::random();
        $yesterday = now()->subDay();
        $tomorrow = now()->addDay();
        $period = MaintenancePeriod::factory()->create([
            'client_message' => $message,
            'date_time_maintenance_start' => $yesterday,
            'date_time_maintenance_end' => $tomorrow,
        ]);

        $this
            ->getJson($this->endpoint)
            ->assertStatus(200)
            ->assertJsonFragment([
                'message' => $message,
            ]);

        $period->delete();
    }

    /**
     * @test
     */
    public function itReturnsEmptyOnActiveMaintenanceOutOfDate(): void
    {
        $yesterday = now()->subDay();
        MaintenancePeriod::factory()->create([
            'date_time_maintenance_start' => $yesterday,
            'date_time_maintenance_end' => $yesterday,
        ]);

        $this
            ->getJson($this->endpoint)
            ->assertStatus(204);
    }

    /**
     * @test
     */
    public function itReturnsEmptyOnInactiveMaintenance(): void
    {
        $yesterday = now()->subDay();
        $tomorrow = now()->addDay();
        MaintenancePeriod::factory()
            ->inactive()
            ->create([
                'date_time_maintenance_start' => $yesterday,
                'date_time_maintenance_end' => $tomorrow,
            ]);

        $this
            ->getJson($this->endpoint)
            ->assertStatus(204);
    }
}
