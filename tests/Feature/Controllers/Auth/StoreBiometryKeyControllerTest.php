<?php

namespace Tests\Feature\Controllers\Auth;

use App\Http\Responses\Auth\BiometricResponse;
use Tests\TestCase;
use App\Models\Client;
use App\Models\Device;
use App\Services\DeviceService;
use PHPUnit\Framework\Attributes\Test;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class StoreBiometryKeyControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    private Device $device;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();

        $this->device = Device::factory()->create([
            'client_id' => $this->client->id,
            'active' => true,
        ]);
    }

    #[Test]
    public function can_save_public_biometry_key(): void
    {
        $this->actingAs($this->client, 'api');

        $expectedResponse = (new BiometricResponse())
            ->biometricPublicKeySavedSuccessfully()
            ->getData(true);

        $response = $this->post('/api/savebiometricpublickey', [
            'device_id' => $this->device->device_id,
            'biometry_public_key' => base64_encode('public_key'),
        ]);

        $response
            ->assertSuccessful()
            ->assertExactJson($expectedResponse);
    }

    #[Test]
    public function catches_unexpected_exceptions(): void
    {
        $this->actingAs($this->client, 'api');

        $errorMessage = 'Unexpected Error';

        $this->mock(DeviceService::class, function ($mock) use ($errorMessage) {
            $mock
                ->shouldReceive('updateBiometricToDevice')
                ->once()
                ->andThrow(new \Exception($errorMessage));
        });

        $expectedResponse = (new BiometricResponse())
            ->biometricPublicKeyInternalServerError($errorMessage)
            ->getData(true);

        $response = $this->post('/api/savebiometricpublickey', [
            'device_id' => $this->device->device_id,
            'biometry_public_key' => base64_encode('public_key'),
        ]);

        $response
            ->assertInternalServerError()
            ->assertExactJson($expectedResponse);
    }
}
