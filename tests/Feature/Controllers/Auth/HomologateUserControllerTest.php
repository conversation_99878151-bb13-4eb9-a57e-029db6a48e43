<?php

namespace Tests\Feature\Controllers\Auth;

use App\DTO\VBankAccountDTO;
use App\Exceptions\Auth\ClientNotFoundException;
use App\Exceptions\Auth\CouldNotCreateVBankUser;
use App\Http\Responses\Auth\HomologateUserResponse;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;
use App\Models\Client;
use App\Tasks\Auth\CreateVBankUserTask;
use App\Tasks\Auth\SendVBankUsernameEmailTask;
use App\Tasks\ValidateVBankAccountTask;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;

class HomologateUserControllerTest extends TestCase
{
    use WithoutMiddleware;

    private const ENDPOINT = 'api/homologate-user';

    private const DEVICE_ID = 'device_id';

    private Client $client;

    private MockInterface|ValidateVBankAccountTask $validateVBankAccountTask;

    private MockInterface|SendVBankUsernameEmailTask $SendVBankUsernameEmailTask;

    private MockInterface|CreateVBankUserTask $createVBankUserTask;

    public function setUp(): void
    {
        parent::setUp();

        $this->validateVBankAccountTask = $this->mock(ValidateVBankAccountTask::class);
        $this->validateVBankAccountTask
            ->shouldReceive('withCustomerId')
            ->withAnyArgs()
            ->andReturnSelf();

        $this->createVBankUserTask = $this->mock(CreateVBankUserTask::class);
        $this->createVBankUserTask
            ->shouldReceive('withCustomerId')
            ->withAnyArgs()
            ->andReturnSelf();
        $this->createVBankUserTask
            ->shouldReceive('withDeviceId')
            ->withAnyArgs()
            ->andReturnSelf();

        $this->SendVBankUsernameEmailTask = $this->mock(SendVBankUsernameEmailTask::class);
        $this->SendVBankUsernameEmailTask
            ->shouldReceive('withCustomerId')
            ->withAnyArgs()
            ->andReturnSelf();
        $this->SendVBankUsernameEmailTask
            ->shouldReceive('withDeviceId')
            ->withAnyArgs()
            ->andReturnSelf();

        $this->client = Client::factory()->create(['homologated' => false]);
        $this->actingAs($this->client, 'api');
    }

    #[Test]
    public function can_homologate_user_with_vbank_account(): void
    {
        $VBankAccount = new VBankAccountDTO('fake_username', false);

        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($VBankAccount);

        $this->SendVBankUsernameEmailTask
            ->shouldReceive('do')
            ->once()
            ->andReturn();

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);
        $this->app->instance(SendVBankUsernameEmailTask::class, $this->SendVBankUsernameEmailTask);

        $expectedResponse = (new HomologateUserResponse())->clientHomologatedSuccessfully();

        $response = $this->post(self::ENDPOINT, headers: ['DeviceAuth' => self::DEVICE_ID]);
        $response
            ->assertOk()
            ->assertJson($expectedResponse->getData(true));

        $this->client->refresh();
        $this->assertTrue((bool) $this->client->homologated);
    }

    #[Test]
    public function can_homologate_users_without_vbank_account(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->once()
            ->andReturnNull();

        $this->createVBankUserTask
            ->shouldReceive('do')
            ->once()
            ->andReturn();

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);
        $this->app->instance(CreateVBankUserTask::class, $this->createVBankUserTask);

        $expectedResponse = (new HomologateUserResponse())->clientHomologatedSuccessfully();

        $response = $this->post(self::ENDPOINT, headers: ['DeviceAuth' => self::DEVICE_ID]);
        $response
            ->assertOk()
            ->assertJson($expectedResponse->getData(true));

        $this->client->refresh();
        $this->assertTrue((bool) $this->client->homologated);
    }

    #[Test]
    public function catches_if_send_username_email_task_does_not_found_customer(): void
    {
        $VBankAccount = new VBankAccountDTO('fake_username', false);

        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($VBankAccount);

        $this->SendVBankUsernameEmailTask
            ->shouldReceive('do')
            ->once()
            ->andThrow(ClientNotFoundException::class);

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);
        $this->app->instance(SendVBankUsernameEmailTask::class, $this->SendVBankUsernameEmailTask);

        $exceptedResponse = (new HomologateUserResponse())->clientNotFound();

        $response = $this->post(self::ENDPOINT, headers: ['DeviceAuth' => self::DEVICE_ID]);
        $response
            ->assertNotFound()
            ->assertJson($exceptedResponse->getData(true));
    }

    #[Test]
    public function catches_if_theres_an_error_creating_vbank_user(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->once()
            ->andReturnNull();

        $this->createVBankUserTask
            ->shouldReceive('do')
            ->once()
            ->andThrow(CouldNotCreateVBankUser::class);

        $exceptedResponse = (new HomologateUserResponse())->couldNotCreateVBankUser();

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);
        $this->app->instance(CreateVBankUserTask::class, $this->createVBankUserTask);


        $response = $this->post(self::ENDPOINT, headers: ['DeviceAuth' => self::DEVICE_ID]);
        $response
            ->assertInternalServerError()
            ->assertJson($exceptedResponse->getData(true));
    }

    #[Test]
    public function catches_if_theres_an_unexpected_error_homologating_the_user(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->once()
            ->andThrow(\Exception::class);

        $exceptedResponse = (new HomologateUserResponse())->unexpectedError();

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);

        $response = $this->post(self::ENDPOINT, headers: ['DeviceAuth' => self::DEVICE_ID]);
        $response
            ->assertInternalServerError()
            ->assertJson($exceptedResponse->getData(true));
    }
}
