<?php

namespace Tests\Feature\Controllers\Auth;

use Exception;
use Tests\TestCase;
use App\DTO\OTP\Type;
use App\Models\Client;
use App\Models\OtpLogs;
use App\DTO\OTP\Channel;
use App\Enums\OTP\Generate;
use App\Enums\OTP\Validate;
use App\Models\NicknameLog;
use App\Services\Crypt\Rsa;
use Illuminate\Support\Str;
use App\Services\OTPService;
use App\Helpers\OTPSharedKey;
use App\Factory\ClientFactory;
use App\Services\Configurations;
use App\Enums\Message\OTPMessage;
use Tests\Support\GuzzleClientMock;
use App\DTO\ExternalRequestResponse;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\TestWith;
use GuzzleHttp\Psr7\Response as Psr7Response;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\Message\Generate as GeneralGenerate;
use App\Enums\Message\Generate as GenerateMessage;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class OTPControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    private const SEND_OTP_ENDPOINT = '/api/sendotpcode';

    private const VALIDATE_OTP_ENDPOINT = '/api/otpvalidatecode';

    private const VALIDATE_OTP_FOR_NICKNAME_ENDPOINT = '/api/OTPValidateCodeForNickname';

    private array $validSendOTPBody = [
        'type' => Type::EMAIL->value,
        'value' => '<EMAIL>',
        'chanel' => Channel::EMAIL->value,
    ];

    public function setUp(): void
    {
        parent::setUp();

        $responseData = new ExternalRequestResponse();

        $guzzleMock = GuzzleClientMock::create(ClientFactory::class, [
            new Psr7Response(body: json_encode($responseData->toResponseArray())),
        ]);

        $this->app->instance(ClientFactory::class, $guzzleMock);

        $this->client = Client::factory()->create();

        $this->validSendOTPBody['value'] = $this->client->email;
    }

    #[Test]
    public function can_send_opt_code(): void
    {
        $this->actingAs($this->client, 'api');

        $data = new ExternalRequestResponse(data: [
            'SharedKey' => Str::random(),
        ]);

        $mock = GuzzleClientMock::create(ClientFactory::class, [
            new Psr7Response(body: json_encode($data->toResponseArray())),
        ]);

        $this->app->instance(ClientFactory::class, $mock);

        $response = $this->postJson(self::SEND_OTP_ENDPOINT, $this->validSendOTPBody);

        $response
            ->assertOk()
            ->assertJsonStructure([
                'SharedKey'
            ])
            ->assertJsonFragment([
                'code' => Generate::SUCCESS->value
            ]);
    }

    #[Test]
    public function catches_if_client_is_block_by_otp(): void
    {
        $client = Client::factory()->create(['status' => Client::BLOCKOTP]);

        $this->actingAs($client, 'api');

        $response = $this->post(self::SEND_OTP_ENDPOINT, $this->validSendOTPBody);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'result' => OTPMessage::NOT_GENERATE_CODES->value,
                'code' => Generate::BLOCK_BY_ATTEMPTS->value,
            ]);
    }

    #[Test]
    public function catches_if_send_otp_generation_failed_by_timing_blocker(): void
    {
        OtpLogs::factory(10)->create([
            'value' => $this->client->email,
            'created_at' => now()->subMinutes(random_int(1, 5))
        ]);

        $this->actingAs($this->client, 'api');

        $response = $this->post(self::SEND_OTP_ENDPOINT, $this->validSendOTPBody);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'code' => Generate::BLOCK_TIME->value,
                'result' => OTPMessage::MAX_GENERATES_CODE->value
            ]);
    }

    #[Test]
    public function catches_if_shared_key_is_missing_from_external_response(): void
    {
        $this->actingAs($this->client, 'api');

        $response = $this->post(self::SEND_OTP_ENDPOINT, $this->validSendOTPBody);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'code' => Generate::WAIT_TIME->value,
                'result' =>  OTPMessage::WAIT_OTP_TIME->value
            ]);
    }

    #[Test]
    public function send_otp_code_catches_unexpected_exceptions(): void
    {
        $this->mock(OTPService::class)
            ->shouldReceive('getChanel')
            ->andThrow(new \Exception());

        $this->post(self::SEND_OTP_ENDPOINT, $this->validSendOTPBody)
            ->assertServerError()
            ->assertJsonFragment([
                'message' => GeneralGenerate::FAIL_SERVER->value,
                'code' => 5000
            ]);
    }

    #[Test]
    public function can_validate_otp_code(): void
    {
        $OTPLog = OtpLogs::factory()->create([
            'value' => $this->client->email,
        ]);

        $OTPAuthorization = OtpLogs::SESSION;

        $this->partialMock(Rsa::class)
            ->shouldReceive('decrypt')
            ->andReturn("{$OTPLog->identifier};{$OTPLog->value};{$OTPAuthorization};{$this->client->id}");

        $response = $this->post(self::VALIDATE_OTP_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => $OTPLog->sharedkey,
        ]);

        $response
            ->assertOk()
            ->assertJsonFragment([
                'status' => Response::HTTP_OK,
                'result' => OTPMessage::VALID->value,
                'code' => Validate::SUCCESS->value
            ]);

        $OTPLog->refresh();

        $this->assertSame(OtpLogs::VERIFIED, (int) $OTPLog->verified);
    }

    #[Test]
    #[TestWith([false, null, null, 'OTP log doesn\'t exists'])]
    #[TestWith([true, '', 'emitter', 'OTP identifier is missing from shared key'])]
    #[TestWith([true, 'fakeidentifier', '', 'OTP emitter value is missing from shared key'])]
    #[TestDox('OTP Code validation fails if $testDoxMessage')]
    public function fails_to_validate_if_otp_log_is_missing_or_shared_key_is_invalid(
        bool $otpExists,
        ?string $identifier,
        ?string $emitter,
        ?string $testDoxMessage
    ): void {
        $OTPLog = OtpLogs::factory()->create([
            'identifier' => $identifier,
            'value' => $emitter,
        ]);

        $decryptedSharedKey = $otpExists
            ? "identifier;emitter;authorization;user"
            : "{$OTPLog->identifier};{$OTPLog->value};SESSION;{$this->client->id}";

        $this->partialMock(Rsa::class)
            ->shouldReceive('decrypt')
            ->andReturn($decryptedSharedKey);

        $response = $this->post(self::VALIDATE_OTP_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => Str::random(),
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'result' => OTPMessage::INVALID_SHARED_KEY,
                'status' => Response::HTTP_BAD_REQUEST,
                'code' => 3
            ]);
    }

    #[Test]
    public function validate_otp_catches_expired_codes(): void
    {
        $otpLifetime = Configurations::getInstance()->getConfigurations('OTP_TIEMPO_VIDA') ?: 60;

        $OTPLog = OtpLogs::factory()->create([
            'value' => $this->client->id,
            'created_at' => now()->subSeconds($otpLifetime + 1)
        ]);

        $this->partialMock(Rsa::class)
            ->shouldReceive('decrypt')
            ->andReturn("{$OTPLog->identifier};{$OTPLog->value};SESSION;{$this->client->id}");

        $response = $this->post(self::VALIDATE_OTP_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => $OTPLog->sharedkey,
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'status' => Response::HTTP_BAD_REQUEST,
                'code' => 4,
                'result' => OTPMessage::EXPIRED->value
            ]);
    }

    #[Test]
    #[TestWith(['APIK'])]
    #[TestWith(['SESSION'])]
    #[TestDox('If client exceeds otp code maximum attemps using authorization $authorization it will be blocked.')]
    public function can_block_emitter_if_exceeds_maximum_allowed_attempts(string $authorization): void
    {
        $configurations = Configurations::getInstance();

        $otpMaximumAttempts = $configurations->getConfigurations('OTP_INTENTOS_VALIDACION') ?: 3;
        $otpBlockTime = $configurations->getConfigurations('OTP_TIEMPO_BLOQUEO_VALIDACION') ?: 5;

        $otpMessage = $authorization === OtpLogs::SESSION
            ? $configurations->getConfigurations('OTP_MENSAJE_BLOQUEO_SESION')
            : str_replace('MINS', $otpBlockTime, $configurations->getConfigurations('OTP_MENSAJE_BLOQUEO_APIKEY'));

        $OTPLog = OtpLogs::factory()->create([
            'type' => Type::EMAIL->value,
            'value' => $this->client->email,
            'attempts' => $otpMaximumAttempts,
        ]);

        $this->partialMock(Rsa::class)
            ->shouldReceive('decrypt')
            ->andReturn("{$OTPLog->identifier};{$OTPLog->value};{$authorization};{$this->client->id}");

        $response = $this->post(self::VALIDATE_OTP_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => $OTPLog->sharedkey,
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'status' => Response::HTTP_BAD_REQUEST,
                'code' => $authorization === OtpLogs::SESSION
                    ? Validate::MAX_ATTEMPTS_OTP_SESS->value
                    : Validate::MAX_ATTEMPTS_OTP_NO_SESS->value,
                'result' => $otpMessage ?: str_replace(':valor', $otpBlockTime, OTPMessage::SUCCESSED_ATTEMPTS->value)
            ]);

        $this->assertDatabaseHas('otp_black_list', ['emitter' => $this->client->email]);
    }

    #[Test]
    public function if_external_otp_validation_fails_the_otp_will_increase_the_attempts_count(): void
    {
        $OTPLog = OtpLogs::factory()->create([
            'value' => $this->client->email,
        ]);

        $OTPAuthorization = OtpLogs::SESSION;

        $this->partialMock(Rsa::class)
            ->shouldReceive('decrypt')
            ->andReturn("{$OTPLog->identifier};{$OTPLog->value};{$OTPAuthorization};{$this->client->id}");

        $externalRequestResponse = new ExternalRequestResponse(responseStatusCode: Response::HTTP_BAD_REQUEST);

        $guzzleMock = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($externalRequestResponse->toResponseArray()))
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleMock);

        $response = $this->post(self::VALIDATE_OTP_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => $OTPLog->sharedkey,
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'status' => Response::HTTP_BAD_REQUEST,
                'result' => OTPMessage::INVALID->value,
                'code' => Validate::INVALID->value
            ]);

        $OTPLog->refresh();

        $this->assertTrue((int) $OTPLog->attempts === 1);
    }

    #[Test]
    public function when_validating_otp_code_for_nickname_validations_fails_if_nickname_is_missing(): void
    {
        $response = $this->post(self::VALIDATE_OTP_FOR_NICKNAME_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => Str::random(),
            'Key' => 'WRONG_KEY'
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'code' => Validate::SHAREDKEY_ERR->value,
                'result' => OTPMessage::INVALID_SHARED_KEY->value,
                'status' => Response::HTTP_BAD_REQUEST
            ]);
    }

    #[Test]
    public function otp_code_validation_for_nickname_notifies_the_client(): void
    {
        $otpLog = OtpLogs::factory()->create([
            'type' => Type::EMAIL->value,
            'value' => $this->client->email
        ]);

        $nicknameLog = NicknameLog::create([
            'key' => Str::random(),
            'client_id' => $this->client->id,
            'faceauthentication' => GenerateMessage::PENDING_FACE_BASE
        ]);

        $this->partialMock(Rsa::class)
            ->shouldReceive('decrypt')
            ->andReturn("{$otpLog->identifier};{$otpLog->value};APIK;{$this->client->id}");

        $response = $this->post(self::VALIDATE_OTP_FOR_NICKNAME_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => Str::random(),
            'Key' => $nicknameLog->key
        ]);

        $response
            ->assertOk()
            ->assertJsonFragment([
                'result' =>  OTPMessage::VALID->value,
                'status' =>  Response::HTTP_OK,
                'code' =>  Validate::SUCCESS->value
            ]);

        $otpLog->refresh();

        $this->assertSame(OtpLogs::VERIFIED, (int) $otpLog->verified);

        $this->assertDatabaseHas('notifications_histories', [
            'client_id' => $this->client->id,
            'type' => 'MAIL'
        ]);
    }

    #[Test]
    #[TestWith([self::VALIDATE_OTP_ENDPOINT])]
    #[TestWith([self::VALIDATE_OTP_FOR_NICKNAME_ENDPOINT])]
    #[TestDox('endpoint $endpoint catches unexpected exceptions')]
    public function validate_otp_code_endpoints_catch_unexpected_exceptions(string $endpoint): void
    {
        $nicknameLog = NicknameLog::create([
            'key' => Str::random(),
            'client_id' => $this->client->id,
            'faceauthentication' => GenerateMessage::PENDING_FACE_BASE
        ]);

        $this->mock(OTPSharedKey::class)
            ->shouldReceive('recoverSharedKey')
            ->andThrow(new Exception());

        $response = $this->post(self::VALIDATE_OTP_FOR_NICKNAME_ENDPOINT, [
            'OTPCode' => '123456',
            'SharedKey' => Str::random(),
            'Key' => $nicknameLog->key
        ]);

        $response
            ->assertServerError()
            ->assertJsonFragment([
                'result' => GenerateMessage::FAIL_SERVER->value,
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR
            ]);
    }
}
