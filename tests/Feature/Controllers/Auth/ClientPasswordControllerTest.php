<?php

namespace Tests\Feature\Controllers\Auth;

use Exception;
use Tests\TestCase;
use App\Models\Client;
use App\Services\Crypt\Rsa;
use Illuminate\Support\Str;
use App\Factory\ClientFactory;
use App\Enums\Message\Generate;
use App\Enums\Code\PasswordCode;
use App\Events\CheckFailedEvent;
use App\Services\Configurations;
use Tests\Support\GuzzleClientMock;
use App\DTO\ExternalRequestResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\TestDox;
use PHPUnit\Framework\Attributes\TestWith;
use App\Http\Responses\Auth\PasswordResponse;
use App\Repos\GetCustomerContactChannelsRepo;
use GuzzleHttp\Psr7\Response as Psr7Response;
use PHPUnit\Framework\Attributes\DataProvider;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class ClientPasswordControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    private Rsa $rsa;

    private const RESET_PASSWORD_ENDPOINT = 'resetpassword';
    private const VALIDATE_PASSWORD_ENDPOINT = 'validatepassword';
    private const RESET_FIRST_PASSWORD_ENDPOINT = 'resetfirstpassword';

    private const VALID_PASSWORD = 'Manzana!1';

    private PasswordResponse $passwordResponse;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create([
            'first_name' => 'Name',
            'first_surname' => 'Surname'
        ]);

        $this->passwordResponse = app()->make(PasswordResponse::class);

        $customerData = [
            'FirstName' => $this->client->first_name,
            'SecondName' => $this->client->second_name,
            'FirstSurname' => $this->client->first_surname,
            'SecondSurname' => $this->client->second_surname,
            'MarriedName' => $this->client->married_surname,
            'DUI' => $this->client->dui,
            'NIT' => $this->client->nit,
            'BirthDate' => '01-01-1999',
        ];

        $customerDataResponse = new ExternalRequestResponse($customerData);

        $contactInformationData = [
            'ContactTypes' => [
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::PHONE_TYPE_CODE,
                    'CustomerContacts' => [
                        ['ContactValue' => $this->client->phone_number],
                    ],
                ],
                [
                    'ContactTypeCode' => GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE,
                    'CustomerContacts' => [
                        ['ContactValue' => $this->client->email],
                    ],
                ],
            ]
        ];

        $contactInformationDataResponse = new ExternalRequestResponse($contactInformationData);

        $guzzleClient = GuzzleClientMock::create(
            abstraction: ClientFactory::class,
            responses: [
                new Psr7Response(body: json_encode($customerDataResponse->toResponseArray())),
                new Psr7Response(body: json_encode($contactInformationDataResponse->toResponseArray())),
            ]
        );

        $this->app->instance(ClientFactory::class, $guzzleClient);

        $this->actingAs($this->client, 'api');

        $this->rsa = new Rsa();
    }

    public static function passwordProvider(): array
    {
        return [
            [self::RESET_FIRST_PASSWORD_ENDPOINT, 'Abc', 'too short', PasswordCode::FAIL->value],
            [self::RESET_FIRST_PASSWORD_ENDPOINT, 'ThisIsATooLongPassword', 'too long', PasswordCode::FAIL->value],
            [self::RESET_FIRST_PASSWORD_ENDPOINT, '01-01-1999', 'date', PasswordCode::FAIL->value],
            [self::RESET_FIRST_PASSWORD_ENDPOINT, '01/01/1999', 'date', PasswordCode::FAIL->value],
            [self::RESET_FIRST_PASSWORD_ENDPOINT, '010199', 'date', PasswordCode::FAIL->value],
            [self::RESET_FIRST_PASSWORD_ENDPOINT, '01-Jan-99', 'date', PasswordCode::FAIL->value],
            [self::RESET_FIRST_PASSWORD_ENDPOINT, 'NameSur123', 'client contact information', PasswordCode::FAIL->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, 'Abc', 'too short', PasswordCode::PASS_ERROR->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, 'ThisIsATooLongPassword', 'too long', PasswordCode::PASS_ERROR->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, '01.eneTest', 'date', PasswordCode::PASS_ERROR->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, '01-Ene-99', 'date', PasswordCode::PASS_ERROR->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, '01-january-1999', 'date', PasswordCode::PASS_ERROR->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, '01-01-1999', 'date', PasswordCode::PASS_ERROR->value],
            [self::VALIDATE_PASSWORD_ENDPOINT, 'NameSur123', 'client contact information', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, 'N4meSur123', 'leetspeak', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, 'Abc', 'too short', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, 'ThisIsATooLongPassword', 'too long', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, '0101Password!', 'date', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, '01/1999Pa!', 'date', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, '01-Enero-1999', 'date', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, 'Test!010199', 'date', PasswordCode::PASS_ERROR->value],
            [self::RESET_PASSWORD_ENDPOINT, 'NameSur123', 'client contact information', PasswordCode::PASS_ERROR->value],
        ];
    }

    #[TestWith([self::RESET_FIRST_PASSWORD_ENDPOINT])]
    #[TestWith([self::RESET_PASSWORD_ENDPOINT])]
    #[TestDox('Can reset client password using endpoint $endpoint')]
    public function testEndpointsCanResetClientPassword($endpoint): void
    {
        $arguments = [
            'password' => $encryptedNewPassword = $this->rsa->encrypt(self::VALID_PASSWORD),
            'password_confirm' => $encryptedNewPassword
        ];

        if ($endpoint = self::RESET_PASSWORD_ENDPOINT) {
            $arguments = array_merge(['dui' => $this->client->dui], $arguments);
        }

        $response = $this->post('/api/' . $endpoint, $arguments);

        $expectedResponse = $endpoint === self::RESET_PASSWORD_ENDPOINT
            ? $this->passwordResponse->passwordUpdatedSuccessfully()
            : $this->passwordResponse->firstPasswordUpdatedSuccessfully();

        $response
            ->assertOk()
            ->assertJsonFragment($expectedResponse->getData(true));

        $this->client->refresh();

        $this->assertTrue(Hash::check(self::VALID_PASSWORD, $this->client->password));

        $this->assertDatabaseHas('notifications_histories', [
            'client_id' => $this->client->id,
            'type' => 'EMAIL',
            'message' => Generate::CHANGED_PASSWORD->value,
        ]);
    }

    #[DataProvider('passwordProvider')]
    #[TestDox('Endpoint $endpoint throws exception if password \' $password \' is $rule')]
    public function testEdpointsThrowsExceptionIfPasswordDoestNotFollowTheSpecifiedRules(
        string $endpoint,
        string $password,
        string $rule,
        int $code,
    ): void {
        $arguments = [
            'password' => $encryptedNewPassword = $this->rsa->encrypt($password),
            'password_confirm' => $encryptedNewPassword
        ];

        $arguments = array_merge(['dui' => $this->client->dui], $arguments);

        $expectedResponse = $this->passwordResponse->invalidPasswordFormat($code, '');

        $expectedResponse = $this->passwordResponse->invalidPasswordFormat($code, '');

        $this->post("/api/$endpoint", $arguments)
            ->assertBadRequest()
            ->assertJsonFragment([
                'code' => $expectedResponse->getData(true)['code'],
            ]);
    }

    #[TestWith([self::VALIDATE_PASSWORD_ENDPOINT])]
    #[TestWith([self::RESET_FIRST_PASSWORD_ENDPOINT])]
    #[TestDox('Endpoint $endpoint catches if the new password is the same as the one in use.')]
    public function testEndpointsCatchIfTheNewPasswordIsTheSameAsTheCurrentOne(string $endpoint): void
    {
        $currentPassword = 'CurrentPassword';

        $this->client->update(['password' => bcrypt($currentPassword)]);

        $errorMessage =  (string) Configurations::getInstance()->getConfigurations('MENSAJE_PASSWORD_USADA');

        $expectedResponse = $endpoint === self::VALIDATE_PASSWORD_ENDPOINT
            ? $this->passwordResponse->currentPasswordUsed($errorMessage)
            : $this->passwordResponse->firstCurrentPasswordUsed($errorMessage);

        $this->post('/api/' . $endpoint, [
            'dui' => $this->client->dui,
            'password' => $encryptedNewPassword = $this->rsa->encrypt($currentPassword),
            'password_confirm' => $encryptedNewPassword
        ])
            ->assertBadRequest()
            ->assertJsonFragment($expectedResponse->getData(true));
    }

    #[TestWith([self::RESET_PASSWORD_ENDPOINT])]
    #[TestWith([self::RESET_FIRST_PASSWORD_ENDPOINT])]
    #[TestDox('Endpoint $endpoint catches unexpected exceptions.')]
    public function testEndpointsCatchUnexpectedException($endpoint): void
    {
        $mock = $this->mock(Rsa::class);

        $mock
            ->shouldReceive('decrypt')
            ->once()
            ->andThrow(new Exception(code: 999));

        $response = $this->post('/api/' . $endpoint, [
            'dui' => $this->client->dui,
            'password' => $encryptedPassword = $this->rsa->encrypt(self::VALID_PASSWORD),
            'password_confirm' => $encryptedPassword
        ]);

        $expectedResponse = $endpoint === self::RESET_PASSWORD_ENDPOINT
            ? $this->passwordResponse->resetPasswordInternalServerError()
            : $this->passwordResponse->firstPasswordInternalServerError();

        $response
            ->assertServerError()
            ->assertJsonFragment($expectedResponse->getData(true));
    }

    public function testCanValidatePassword(): void
    {
        $response = $this->post('/api/' . self::VALIDATE_PASSWORD_ENDPOINT, [
            'dui' => $this->client->dui,
            'password' => $this->rsa->encrypt(self::VALID_PASSWORD),
        ]);

        $expectedResponse = $this->passwordResponse->validPassword();

        $response
            ->assertOk()
            ->assertJsonFragment($expectedResponse->getData(true));
    }

    #[TestWith([self::VALIDATE_PASSWORD_ENDPOINT])]
    #[TestWith([self::RESET_PASSWORD_ENDPOINT])]
    #[TestDox('Endpoint $endpoint throws an error if the client is not found.')]
    public function testEndpointsThrowAnErrorIfClientIsNotFound(string $endpoint): void
    {
        Event::fake();

        $isValidateEndpoint = $endpoint === self::VALIDATE_PASSWORD_ENDPOINT;

        $errorMessage = $isValidateEndpoint
            ? Generate::NOT_CUSTOMER_FOUND_DUI->value
            : Str::of(Generate::NOT_CLIENT_WHIT_DUI->value)
            ->swap([":valor" => 'WRONG_DUI']) . '.';

        $expectedResponse = $isValidateEndpoint
            ? $this->passwordResponse->validatePasswordCustomerNotFound($errorMessage)
            : $this->passwordResponse->customerNotFound($errorMessage);


        $this->post('/api/' . $endpoint, [
            'dui' => 'WRONG_DUI',
            'password' => $encryptedPassword = $this->rsa->encrypt(self::VALID_PASSWORD),
            'password_confirm' => $encryptedPassword
        ])
            ->assertBadRequest()
            ->assertJsonFragment($expectedResponse->getData(true));

        Event::assertDispatched(CheckFailedEvent::class);
    }

    public function testResetPasswordThrowsAnErrorIfDuiIsMissingFromTheRequest(): void
    {
        $expectedResponse = $this->passwordResponse->missingFields(
            PasswordCode::PASS_ERROR->value,
            'El campo dui es obligatorio.'
        );

        $this->post('/api/' . self::RESET_PASSWORD_ENDPOINT, [
            'password' => $encryptedPassword = $this->rsa->encrypt(self::VALID_PASSWORD),
            'password_confirm' => $encryptedPassword
        ])
            ->assertBadRequest()
            ->assertJsonFragment($expectedResponse->getData(true));
    }

    public function testResetPasswordThrowsAnErrorIfCannotDecryptPassword(): void
    {
        $response = $this->post('/api/' . self::RESET_PASSWORD_ENDPOINT, [
            'dui' => $this->client->dui,
            'password' => 'UNENCRYPTED_PASSWORD',
        ]);

        $response
            ->assertBadRequest()
            ->assertJsonFragment([
                'result' => Generate::NOT_DESCRIPT->value,
                'code' => PasswordCode::DECRYPT_ERROR->value,
            ]);
    }
}
