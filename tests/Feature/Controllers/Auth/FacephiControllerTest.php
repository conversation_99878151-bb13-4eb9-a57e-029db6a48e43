<?php

namespace Tests\Feature\Controllers\Auth;

use Tests\TestCase;
use App\Models\Client;
use App\Services\Configurations;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class FacephiControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    private string $facephiScore;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();

        $this->facephiScore = Configurations::getInstance()->getConfigurations('SCORE_FACEPHI');
    }

    #[Test]
    public function can_retrieve_facephi_score_from_database(): void
    {
        $response = $this->get('/api/matchsidescorefacephi');

        $response
            ->assertOk()
            ->assertJson([
                'score' => $this->facephiScore,
                'status' => Response::HTTP_OK
            ]);
    }
}
