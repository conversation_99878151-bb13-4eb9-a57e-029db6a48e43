<?php

namespace Tests\Feature\Controllers\Auth;

use App\DTO\VBankAccountDTO;
use Carbon\Carbon;
use Tests\TestCase;
use App\Models\Client;
use App\Models\Device;
use App\Dtos\LoginSession;
use App\Dtos\ExternalToken;
use App\Enums\Code\HomologatedLoginCode;
use Illuminate\Support\Str;
use App\Repos\GenerateTokenRepo;
use Tests\Traits\CreatesClients;
use App\Events\UserIsDisabledEvent;
use Illuminate\Support\Facades\App;
use Tests\Traits\HandlesEncryption;
use App\Events\SuccessfulLoginEvent;
use Illuminate\Support\Facades\Http;
use App\Events\WrongCredentialsEvent;
use App\Repos\DummyGenerateTokenRepo;
use Illuminate\Support\Facades\Event;
use App\Events\MaxDevicesReachedEvent;
use PHPUnit\Framework\Attributes\Test;
use App\Exceptions\Auth\UserIsDisabled;
use App\Services\Auth\VBankLoginService;
use App\Events\UserCantAccessSystemEvent;
use App\Exceptions\Auth\WrongCredentials;
use App\Exceptions\Auth\MaxDevicesReached;
use App\Http\Responses\Auth\LoginResponse;
use App\Services\Configurations as Config;
use App\Exceptions\Auth\UserCantAccessSystem;
use App\Exceptions\Auth\InvalidPasswordPeriod;
use Symfony\Component\HttpFoundation\Response;
use App\Exceptions\Auth\InvalidFirstLoginPeriod;
use App\Events\CantDecryptCredentialsOnLoginEvent;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use App\Exceptions\Auth\ExternalTokenGenerationFailed;
use App\Tasks\ValidateVBankAccountTask;
use App\Tasks\Auth\ValidateVBankCredentialsTask;

class LoginControllerTest extends TestCase
{
    use HandlesEncryption;
    use CreatesClients;
    use WithoutMiddleware;

    private Config $config;

    private string $endpoint = '/api/login';

    private string $tokenServiceUrl;

    private Client $client;

    private Device $device;

    private array $validPayload;

    protected function setUp(): void
    {
        parent::setUp();

        $this->config = App::make(Config::class);

        $this->tokenServiceUrl = config('services.baes.base_url') . 'TokenAuthorization*';

        $this->client = $this->createClientWithSystemAndPassword();

        $validateVBankAccountTask = $this->mock(ValidateVBankAccountTask::class);

        $validateVBankAccountTask
            ->shouldReceive('withCustomerId')
            ->withAnyArgs()
            ->andReturn($validateVBankAccountTask);

        $validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn(new VBankAccountDTO('username', false));

        $this->app->instance(ValidateVBankAccountTask::class, $validateVBankAccountTask);

        $this->device = Device::factory()
            ->offline()
            ->create(['client_id' => $this->client->id]);

        $this->validPayload = [
            'nickname' => $this->encrypt($this->client->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => $this->device->device_id,
            'device_name' => $this->device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ];
    }

    /**
     * @test
     */
    public function itFailsToLoginOnUndecryptableCredentials(): void
    {
        $password = Str::random();
        $user = $this->createClientWithDefaultSystem(attributes: ['password' => bcrypt($password)]);
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $user->nickname,
                'password' => $password,
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400);
    }

    /**
     * @test
     */
    public function itTriggersEventOnUndecryptableCredentials(): void
    {
        $password = Str::random();
        $user = $this->createClientWithDefaultSystem(attributes: ['password' => bcrypt($password)]);
        $device = Device::factory()->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'nickname' => $user->nickname,
            'password' => $password,
            'device_id' => $device->device_id,
            'device_name' => $device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ]);

        Event::assertDispatched(CantDecryptCredentialsOnLoginEvent::class);
    }

    /**
     * @test
     */
    public function itCountsLoginAttemptsOnWrongPassword(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()->create(['client_id' => $user->id]);

        $body = [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt(Str::random()),
            'device_id' => $device->device_id,
            'device_name' => $device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ];

        $this->assertEquals(0, $user->password_login_attempts);

        $this->postJson($this->endpoint, $body);
        $this->assertEquals(1, $user->fresh()->password_login_attempts);

        $this->postJson($this->endpoint, $body);
        $this->assertEquals(2, $user->fresh()->password_login_attempts);
    }

    /**
     * @test
     */
    public function itBlocksUserOnTooManyLoginAttempts(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()->create(['client_id' => $user->id]);

        foreach (range(1, $this->config->maxAttempts()) as $_) {
            $this->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt(Str::random()),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ]);
        }

        $this->assertTrue($user->fresh()->IsDisabled());

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(403)
            ->assertJsonFragment(['error_code' => UserIsDisabled::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnWrongCredentials(): void
    {
        // Wrong user and password.
        $dummyCredential = Str::random(8);
        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($dummyCredential),
                'password' => $this->encrypt($dummyCredential),
                'device_id' => $dummyCredential,
                'device_name' => $dummyCredential,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400);

        // Wrong password.
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()->create(['client_id' => $user->id]);
        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt($dummyCredential),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400);

        // Wrong user.
        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($dummyCredential),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400);
    }

    /**
     * @test
     */
    public function itReturnsAvailableAttemptsOnWrongCredentials(): void
    {
        $dummyCredential = Str::random(8);

        // Wrong user and password.
        // It won't return available attempts because there's not user to match.
        foreach (range(1, 10) as  $_) {
            $this
                ->postJson($this->endpoint, [
                    'nickname' => $this->encrypt($dummyCredential),
                    'password' => $this->encrypt($dummyCredential),
                    'device_id' => $dummyCredential,
                    'device_name' => $dummyCredential,
                    'firebase_token' => 'some token',
                    'longitude' => '0',
                    'latitude' => '0',
                ])
                ->assertStatus(400)
                ->assertJsonFragment(['error_code' => WrongCredentials::getErrorCode()]);
        }

        // Wrong password.
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()->create(['client_id' => $user->id]);
        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt($dummyCredential),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400)
            ->assertJsonFragment(['error_code' => WrongCredentials::getErrorCode()])
            ->assertJsonFragment(['available_attempts' => (int) $this->config->maxAttempts() - 1]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnWrongCredentials(): void
    {
        $dummyCredential = Str::random(8);

        Event::fake();

        $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($dummyCredential),
            'password' => $this->encrypt($dummyCredential),
            'device_id' => $dummyCredential,
            'device_name' => $dummyCredential,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ]);

        Event::assertDispatched(WrongCredentialsEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnOldFirstPassword(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $user->created_at = Carbon::now()->subDays(100);
        $user->save();
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400)
            ->assertJson(['error_code' => InvalidFirstLoginPeriod::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnOldPassword(): void
    {
        $user = $this->createClientWithSystemAndPassword();
        $user->created_at = Carbon::now()->subDays(1000);
        $user->save();

        $lastPasswordChange = $user->passwords()->first();
        $lastPasswordChange->created_at = Carbon::now()->subDays(1000);
        $lastPasswordChange->save();

        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(400)
            ->assertJson(['error_code' => InvalidPasswordPeriod::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnBlockedByOtp(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->blockedByOtp()
                ->create()
        );
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(403);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnBlockedByFaceAuth(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->blockedByFaceAuth()
                ->create()
        );
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(403);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnDisabled(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->disabled()
                ->create()
        );
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(403)
            ->assertJsonFragment(['error_code' => UserIsDisabled::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnDisabled(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->disabled()
                ->create()
        );
        $device = Device::factory()->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => $device->device_id,
            'device_name' => $device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ]);

        Event::assertDispatched(UserIsDisabledEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnUserWithoutDefaultSystem(): void
    {
        $user = $this->createClientWithPassword();
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertForbidden()
            ->assertJson(['error_code' => UserCantAccessSystem::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnUserWithoutDefaultSystem(): void
    {
        $user = $this->createClientWithPassword();
        $device = Device::factory()->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => $device->device_id,
            'device_name' => $device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ]);

        Event::assertDispatched(UserCantAccessSystemEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnMaxDevicesReached(): void
    {
        $user = $this->createClientWithSystemAndPassword();
        Device::factory()
            ->count(60)
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => Str::random(),
                'device_name' => Str::random(),
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertStatus(422)
            ->assertJson(['error_code' => MaxDevicesReached::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnMaxDevicesReached(): void
    {
        $user = $this->createClientWithSystemAndPassword();
        Device::factory()
            ->count(60)
            ->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => Str::random(),
            'device_name' => Str::random(),
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ]);

        Event::assertDispatched(MaxDevicesReachedEvent::class);
    }

    /**
     * @test
     */
    public function itLogins(): void
    {
        // Temporal binding for testing.
        App::instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));

        $user = $this->createClientWithSystemAndPassword();
        Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => Str::random(),
                'device_name' => Str::random(),
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'token',
                    'refresh_token',
                    'message',
                    'user' => [
                        'customer_id',
                        'creditcard_application_id',
                        "nickname",
                        "first_name",
                        "second_name",
                        "first_surname",
                        "second_surname",
                        "married_surname",
                        "dui",
                        "nit",
                        "email",
                        "phone_number",
                        "status",
                        "BackgroundImage",
                        "ProfileImage",
                        "last_login",
                    ]
                ],
            ]);
    }

    /**
     * @test
     */
    public function itLoginsOnNullLocationData(): void
    {
        App::instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));

        $user = $this->createClientWithSystemAndPassword();
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => Str::random(),
                'device_name' => Str::random(),
                'firebase_token' => 'some token',
                'longitude' => null,
                'latitude' => null,
            ])
            ->assertSuccessful();
    }

    /**
     * @test
     */
    public function itTriggersEventOnSuccessfulLogin(): void
    {
        // Temporal binding for testing.
        App::instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));

        $user = $this->createClientWithSystemAndPassword();
        $device = Device::factory()->create(['client_id' => $user->id]);

        Event::fake();

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertSuccessful();

        Event::assertDispatched(function (SuccessfulLoginEvent $event) use ($user, $device) {
            return $event->user->id === $user->id
                and $event->login->device->id === $device->device_id;
        });
    }

    /**
     * @test
     */
    public function itDoesNotReturnAnErrorOnMultipleOnlineDevices(): void
    {
        // Temporal binding for testing.
        App::instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));

        $user = $this->createClientWithSystemAndPassword();
        Device::factory()
            ->online()
            ->create(['client_id' => $user->id]);
        $deviceOffline = Device::factory()
            ->offline()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $deviceOffline->device_id,
                'device_name' => $deviceOffline->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ])
            ->assertSuccessful();
    }

    /**
     * @test
     */
    public function afterLoginOnlyOneDeviceIsMarkedAsOnline(): void
    {
        // Temporal binding for testing.
        App::instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));

        $user = $this->createClientWithSystemAndPassword();
        [$deviceA, $deviceB] = Device::factory()
            ->count(2)
            ->online()
            ->create(['client_id' => $user->id]);
        $deviceC = Device::factory()
            ->offline()
            ->create(['client_id' => $user->id]);

        $response = $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => $deviceC->device_id,
            'device_name' => $deviceC->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ]);

        $response->assertSuccessful();
        $this->assertEquals(Device::OFFLINE, $deviceA->fresh()->online);
        $this->assertEquals(Device::OFFLINE, $deviceB->fresh()->online);
        $this->assertEquals(Device::ONLINE, $deviceC->fresh()->online);
    }

    /**
     * @test
     */
    public function itCatchesIfExternalTokenServiceReturnsAnError(): void
    {
        Http::fake([
            $this->tokenServiceUrl => Http::response(
                body: [],
                status: Response::HTTP_INTERNAL_SERVER_ERROR
            )
        ]);

        $errorCode = ExternalTokenGenerationFailed::getErrorCode();

        $expectedResponse = (new LoginResponse())->externalTokenServiceFailure($errorCode);

        $response = $this->post($this->endpoint, $this->validPayload);

        $response
            ->assertServiceUnavailable()
            ->assertExactJson($expectedResponse->getData(true));
    }

    /**
     * @test
     */
    public function itReturnsAppropiateResponseIfResponseContentIsEmpty(): void
    {
        Http::fake([
            $this->tokenServiceUrl => Http::response(
                body: ["Data" => null],
                status: Response::HTTP_OK
            )
        ]);

        $errorCode = ExternalTokenGenerationFailed::getErrorCode();

        $expectedResponse = (new LoginResponse())->externalTokenServiceFailure($errorCode);

        $response = $this->post($this->endpoint, $this->validPayload);

        $response
            ->assertServiceUnavailable()
            ->assertExactJson($expectedResponse->getData(true));
    }

    /**
     * @test
     */
    public function itCanLoginHomologatedUsers(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        $user->update(['homologated' => true]);

        $device = Device::factory()->create(['client_id' => $user->id]);

        $mockedVBankLoginService = $this->mock(VBankLoginService::class);

        $mockedVBankLoginService
            ->shouldReceive('login')
            ->withAnyArgs()
            ->once()
            ->andReturn(new LoginSession(
                token: new ExternalToken('token', 'refresh_token'),
                clientId: $user->client_id,
                creditcardApplicationId: $user->creditcard_application_id,
                message: '',
                homologationCode: HomologatedLoginCode::SUCCESSFUL_LOGIN->value
            ));

        $this->app->instance(VBankLoginService::class, $mockedVBankLoginService);

        $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => $device->device_id,
            'device_name' => $device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ])->assertSuccessful();
    }

    /**
     * @test
     */
    public function usesRateLimitingToBlockHomologatedUserFromMakingTooManyRequest(): void
    {
        $user = $this->createClientWithSystemAndPassword();

        $user->update(['homologated' => true]);

        $device = Device::factory()->create(['client_id' => $user->id]);

        $validateVBankCredentialsMock = $this->mock(ValidateVBankCredentialsTask::class);

        $validateVBankCredentialsMock
            ->shouldReceive('withCredentials')
            ->withAnyArgs()
            ->andReturn($validateVBankCredentialsMock);

        $validateVBankCredentialsMock
            ->shouldReceive('do')
            ->andReturn([]);

        $this->app->instance(ValidateVBankCredentialsTask::class, $validateVBankCredentialsMock);

        foreach (range(1, 10) as $_) {
            $this->postJson($this->endpoint, [
                'nickname' => $this->encrypt($user->nickname),
                'password' => $this->encrypt('123456'),
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'firebase_token' => 'some token',
                'longitude' => '0',
                'latitude' => '0',
            ]);
        }

        $this->postJson($this->endpoint, [
            'nickname' => $this->encrypt($user->nickname),
            'password' => $this->encrypt('123456'),
            'device_id' => $device->device_id,
            'device_name' => $device->device_name,
            'firebase_token' => 'some token',
            'longitude' => '0',
            'latitude' => '0',
        ])->assertStatus(Response::HTTP_TOO_MANY_REQUESTS);
    }
}
