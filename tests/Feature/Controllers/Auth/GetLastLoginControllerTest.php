<?php

namespace Tests\Feature\Controllers\Auth;

use Exception;
use Tests\TestCase;
use App\Models\Client;
use App\Models\RegisterClientsApp;
use PHPUnit\Framework\Attributes\Test;
use App\Http\Responses\Auth\LastLoginResponse;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class GetLastLoginControllerTest extends TestCase
{
    use WithoutMiddleware;

    private Client $client;

    public function setUp(): void
    {
        parent::setUp();

        $this->client = Client::factory()->create();
    }

    #[Test]
    public function can_get_latest_login_activity(): void
    {
        $latestLogin = new RegisterClientsApp();
        $latestLogin->endpoint_name = 'LOGIN';
        $latestLogin->status_response = '200';
        $latestLogin->client_id = $this->client->id;
        $latestLogin->device_id = 'device-id';
        $latestLogin->ip = '127.0.0.1';
        $latestLogin->result = '';
        $latestLogin->response_time_ms = 200;
        $latestLogin->save();

        $latestLogin = date('d/m/Y h:i a', strtotime($latestLogin->created_at));
        $latestLogin = 'Última sesión ' . str_replace(["am", "pm"], ["a.m.", "p.m."], $latestLogin);

        $this->actingAs($this->client, 'api');

        $response = $this->get('/api/getlastlogin');

        $expectedResponseData = (new LastLoginResponse())
            ->successful($latestLogin)
            ->getData(true);

        $response
            ->assertSuccessful()
            ->assertExactJson($expectedResponseData);
    }

    #[Test]
    public function can_get_last_login_activity_even_if_there_is_any(): void
    {
        $this->actingAs($this->client, 'api');

        $response = $this->get('/api/getlastlogin');

        $expectedResponseData = (new LastLoginResponse())
            ->emptyLoginActivity()
            ->getData(true);

        $response
            ->assertSuccessful()
            ->assertExactJson($expectedResponseData);
    }

    #[Test]
    public function can_catch_unexpected_exceptions(): void
    {
        $this->actingAs($this->client, 'api');

        $client = $this->mock(Client::class, function ($mock) {
            $mock
                ->shouldReceive('registerClientsApp')
                ->once()
                ->andThrow(new Exception());
        });

        $this->mock('auth', function ($mock) use ($client) {
            $mock->shouldReceive('guard->user')->andReturn($client);
        });

        $response = $this->get('/api/getlastlogin');

        $expectedResponseData = (new LastLoginResponse())
            ->internalServerError()
            ->getData(true);

        $response
            ->assertInternalServerError()
            ->assertExactJson($expectedResponseData);
    }
}
