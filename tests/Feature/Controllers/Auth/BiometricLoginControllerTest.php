<?php

namespace Tests\Feature\Controllers\Auth;

use App\DTO\VBankAccountDTO;
use App\Enums\Code\HomologatedLoginCode;
use App\Events\DeviceNotFoundOnBiometricLoginEvent;
use App\Events\FailedToBiometricLoginDueToLackOfChallengeEvent;
use App\Events\FailedToBiometricLoginDueToLackOfPublicKeyEvent;
use App\Events\SuccessfulBiometricLoginEvent;
use App\Events\TooManyBiometricLoginAttemptsEvent;
use App\Events\WrongBiometricCredentialsEvent;
use App\Exceptions\Auth\DeviceDoesNotHaveAPublicKey;
use App\Exceptions\Auth\DeviceDoesNotHaveChallenge;
use App\Exceptions\Auth\InvalidPasswordPeriod;
use App\Exceptions\Auth\UserCantAccessSystem;
use App\Models\Client;
use App\Models\Device;
use App\Repos\DummyGenerateTokenRepo;
use App\Repos\GenerateTokenRepo;
use App\Services\Crypt\Rsa;
use App\Tasks\ValidateVBankAccountTask;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Mockery\MockInterface;
use Tests\TestCase;
use Tests\Traits\CreatesClients;
use Tests\Traits\CreatesRsaKeys;
use Tests\Traits\HandlesEncryption;

class BiometricLoginControllerTest extends TestCase
{
    use CreatesRsaKeys;
    use HandlesEncryption;
    use CreatesClients;
    use WithoutMiddleware;

    private string $endpoint = '/api/biometric-login';

    private VBankAccountDTO $vBankAccountDTO;

    private MockInterface|ValidateVBankAccountTask $validateVBankAccountTask;

    public function setUp(): void
    {
        parent::setUp();

        $this->validateVBankAccountTask = $this->mock(ValidateVBankAccountTask::class);

        $this->validateVBankAccountTask
            ->shouldReceive('withCustomerId')
            ->andReturnSelf();

        $this->vBankAccountDTO = new VBankAccountDTO(username: 'test', requiresPasswordReset: false);

        $this->app->instance(ValidateVBankAccountTask::class, $this->validateVBankAccountTask);

        $this->app->instance(GenerateTokenRepo::class, App::make(DummyGenerateTokenRepo::class));
    }

    /**
     * @test
     */
    public function itFailsToLoginOnWrongData(): void
    {
        $this
            ->postJson($this->endpoint, [])
            ->assertStatus(422)
            ->assertJsonValidationErrors([
                'signed_data',
                'device_name',
                'device_id',
            ]);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnUnknownDevice(): void
    {
        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => Str::random(),
                'device_id' => Str::uuid(),
            ])
            ->assertStatus(400);
    }

    /**
     * @test
     */
    public function itTriggersEventOnUnknownDevice(): void
    {
        Event::fake();

        $this->postJson($this->endpoint, [
            'signed_data' => Str::random(),
            'device_name' => Str::random(),
            'device_id' => Str::uuid(),
        ]);

        Event::assertDispatched(DeviceNotFoundOnBiometricLoginEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnTooManyLoginAttempts(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        foreach (range(1, 10) as $_) {
            $this->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ]);
        }

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(429);
    }

    /**
     * @test
     */
    public function itTriggersEventOnTooManyLoginAttempts(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        foreach (range(1, 10) as $_) {
            $this->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ]);
        }

        Event::fake();

        $this->postJson($this->endpoint, [
            'signed_data' => Str::random(),
            'device_name' => $device->device_name,
            'device_id' => $device->device_id,
        ]);

        Event::assertDispatched(TooManyBiometricLoginAttemptsEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnMissingDevicePublicKey(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->emptyPublicKey()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(422)
            ->assertJsonFragment(['error_code' => DeviceDoesNotHaveAPublicKey::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnMissingDevicePublicKey(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->emptyPublicKey()
            ->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'signed_data' => Str::random(),
            'device_name' => $device->device_name,
            'device_id' => $device->device_id,
        ]);

        Event::assertDispatched(FailedToBiometricLoginDueToLackOfPublicKeyEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnMissingDeviceChallenge(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->emptyChallenge()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(422)
            ->assertJsonFragment(['error_code' => DeviceDoesNotHaveChallenge::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnMissingDeviceChallenge(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->emptyChallenge()
            ->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'signed_data' => Str::random(),
            'device_name' => $device->device_name,
            'device_id' => $device->device_id,
        ]);

        Event::assertDispatched(FailedToBiometricLoginDueToLackOfChallengeEvent::class);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnWrongCredentials(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(400);
    }

    /**
     * @test
     */
    public function itTriggersEventOnWrongCredentials(): void
    {
        $user = $this->createClientWithDefaultSystem();
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        Event::fake();

        $this->postJson($this->endpoint, [
            'signed_data' => Str::random(),
            'device_name' => $device->device_name,
            'device_id' => $device->device_id,
        ]);

        Event::assertDispatched(WrongBiometricCredentialsEvent::class);
    }

    /**
     * @test
     */
    public function itLogins(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($this->vBankAccountDTO);

        [$privateKey, $publicKey] = $this->createKeyPair();
        $challenge = Str::random();
        $user = $this->createClientWithSystemAndPassword();
        $device = Device::factory()->active()->create([
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $challenge,
            'client_id' => $user->id,
        ]);
        $signature = Rsa::signWith($challenge, $privateKey);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => $signature,
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'token',
                    'refresh_token',
                    'message',
                    'user' => [
                        'customer_id',
                        'creditcard_application_id',
                        "nickname",
                        "first_name",
                        "second_name",
                        "first_surname",
                        "second_surname",
                        "married_surname",
                        "dui",
                        "nit",
                        "email",
                        "phone_number",
                        "status",
                        "BackgroundImage",
                        "ProfileImage",
                        "last_login",
                    ]
                ],
            ]);
    }

    /**
     * @test
     */
    public function itTriggersEventOnSuccessfulLogin(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($this->vBankAccountDTO);

        [$privateKey, $publicKey] = $this->createKeyPair();
        $challenge = Str::random();
        $user = $this->createClientWithSystemAndPassword();
        $device = Device::factory()->active()->create([
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $challenge,
            'client_id' => $user->id,
        ]);
        $signature = Rsa::signWith($challenge, $privateKey);

        Event::fake();

        $this->postJson($this->endpoint, [
            'signed_data' => $signature,
            'device_name' => $device->device_name,
            'device_id' => $device->device_id,
        ]);

        Event::assertDispatched(function (SuccessfulBiometricLoginEvent $event) use ($user, $device) {
            return $event->user->id === $user->id
                and $event->device->device_id === $device->device_id;
        });
    }

    /**
     * @test
     */
    public function itFailsToLoginOnOldPassword(): void
    {
        $user = $this->createClientWithSystemAndPassword();
        $user->created_at = Carbon::now()->subDays(1000);
        $user->save();

        $lastPasswordChange = $user->passwords()->first();
        $lastPasswordChange->created_at = Carbon::now()->subDays(1000);
        $lastPasswordChange->save();

        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(400)
            ->assertJson(['error_code' => InvalidPasswordPeriod::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnBlockedByOtp(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->blockedByOtp()
                ->create()
        );
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(403);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnBlockedByFaceAuth(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->blockedByFaceAuth()
                ->create()
        );
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(403);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnDisabled(): void
    {
        $user = $this->createClientWithSystemAndPassword(
            Client::factory()
                ->disabled()
                ->create()
        );
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertStatus(403);
    }

    /**
     * @test
     */
    public function itFailsToLoginOnUserWithoutDefaultSystem(): void
    {
        $user = $this->createClientWithPassword();
        $device = Device::factory()
            ->active()
            ->create(['client_id' => $user->id]);

        $this
            ->postJson($this->endpoint, [
                'signed_data' => Str::random(),
                'device_name' => $device->device_name,
                'device_id' => $device->device_id,
            ])
            ->assertForbidden()
            ->assertJson(['error_code' => UserCantAccessSystem::getErrorCode()]);
    }

    /**
     * @test
     */
    public function itRetunsCodeToInitializeHomologationForUsersWithVBank(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn($this->vBankAccountDTO);

        [$privateKey, $publicKey] = $this->createKeyPair();

        $challenge = Str::random();

        $user = $this->createClientWithSystemAndPassword();

        $device = Device::factory()->active()->create([
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $challenge,
            'client_id' => $user->id,
        ]);

        $signature = Rsa::signWith($challenge, $privateKey);

        Event::fake();

        $response = $this->postJson($this->endpoint, [
            'signed_data' => $signature,
            'device_id' => $device->device_id,
            'device_name' => $device->device_name
        ]);

        $response->assertJsonFragment([
            'result' => HomologatedLoginCode::NON_HOMOLOGATED_USER_VBANK
        ]);
    }

    /**
     * @test
     */
    public function itRetunsCodeToInitializeHomologationForUsersWithNoVBank(): void
    {
        $this->validateVBankAccountTask
            ->shouldReceive('do')
            ->andReturn(null);

        [$privateKey, $publicKey] = $this->createKeyPair();

        $challenge = Str::random();

        $user = $this->createClientWithSystemAndPassword();

        $device = Device::factory()->active()->create([
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $challenge,
            'client_id' => $user->id,
        ]);

        $signature = Rsa::signWith($challenge, $privateKey);

        Event::fake();

        $response = $this->postJson($this->endpoint, [
            'signed_data' => $signature,
            'device_id' => $device->device_id,
            'device_name' => $device->device_name
        ]);

        $response->assertJsonFragment([
            'result' => HomologatedLoginCode::NON_HOMOLOGATED_USER_NO_VBANK
        ]);
    }
}
