<?php

namespace Tests\Feature\Controllers\Auth;

use Tests\TestCase;
use App\Models\Client;
use Mo<PERSON>y\MockInterface;
use Illuminate\Support\Str;
use App\DTO\FaceAuthRequestDTO;
use PHPUnit\Framework\Attributes\Test;
use App\Tasks\Auth\CallFaceAuthenticationTask;
use App\Tasks\Auth\CallFaceAuthenticationExtTask;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class FaceAuthenticationExtControllerTest extends TestCase
{
    use WithoutMiddleware;

    private const ENDPOINT = '/api/faceauthentication-extension';

    private Client $client;

    private FaceAuthRequestDTO $faceAuthData;

    private MockInterface|CallFaceAuthenticationTask $mockedFaceAuthentication;

    private MockInterface|CallFaceAuthenticationExtTask $mockedFaceAuthenticationExt;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockedFaceAuthentication = $this->mock(CallFaceAuthenticationTask::class);
        $this->mockedFaceAuthentication
            ->shouldReceive('withFaceAuthData')
            ->andReturn($this->mockedFaceAuthentication);

        $this->mockedFaceAuthenticationExt = $this->mock(CallFaceAuthenticationExtTask::class);
        $this->mockedFaceAuthenticationExt
            ->shouldReceive('withFaceAuthData')
            ->andReturn($this->mockedFaceAuthenticationExt);


        $this->app->instance(CallFaceAuthenticationTask::class, $this->mockedFaceAuthentication);
        $this->app->instance(CallFaceAuthenticationExtTask::class, $this->mockedFaceAuthenticationExt);

        $this->client = Client::factory()->create();

        $this->actingAs($this->client);

        $this->faceAuthData = new FaceAuthRequestDTO(
            ccApplicationId: $this->client->creditcard_application_id,
            method: 'ME_AUTH_FACIAL_2',
            query: $this->client->dui,
            imageBuffer: base64_encode(Str::random(60)),
            deviceId: 'device-id',
            customerId: $this->client->client_id
        );
    }

    #[Test]
    public function validates_require_fields(): void
    {
        $this->actingAs($this->client, 'api');

        $response = $this->post(
            self::ENDPOINT,
            [],
            [
                'Accept' => 'application/json',
                'DeviceAuth' => 'device-id',
                'UserAuth' => $this->client->id
            ]
        );

        $response
            ->assertUnprocessable()
            ->assertJsonValidationErrors([
                'CCApplicationId',
                'Method',
                'Querry',
                'ImageBuffer',
            ]);
    }

    #[Test]
    public function calls_faceauthenticationtask_if_user_is_not_foreigner(): void
    {
        $this->mockedFaceAuthentication
            ->shouldReceive('do')
            ->once();

        $this->post(
            uri: self::ENDPOINT,
            data: $this->faceAuthData->toArray(),
            headers: ['DeviceAuth' => 'device-id', 'UserAuth' => $this->client->id]
        );
    }

    #[Test]
    public function calls_faceauthenticationexttask_if_user_is_foreigner(): void
    {
        $this->client->update(['foreigner' => true]);

        $this->client->refresh();

        $this->mockedFaceAuthenticationExt
            ->shouldReceive('do')
            ->once()
            ->andReturn('successful');

        $this->post(
            uri: self::ENDPOINT,
            data: $this->faceAuthData->toArray(),
            headers: ['DeviceAuth' => 'device-id', 'UserAuth' => $this->client->id]
        );
    }
}
