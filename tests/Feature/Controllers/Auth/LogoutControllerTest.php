<?php

namespace Tests\Feature\Controllers\Auth;

use App\Http\Controllers\API\Auth\LogoutController;
use App\Http\Responses\ApiResponse;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;
use App\Models\Client;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\MockObject\MockObject;

class LogoutControllerTest extends TestCase
{
    use WithoutMiddleware;

    private MockObject|LogoutController $mockedController;

    public function setUp(): void
    {
        parent::setUp();

        $response = new ApiResponse();

        $this->mockedController = $this->getMockBuilder(LogoutController::class)
            ->setConstructorArgs([$response])
            ->onlyMethods(['logBank'])
            ->getMock();
    }

    #[Test]
    public function test_calls_log_application_endpont(): void
    {
        $client = Client::factory()->create();

        $this->actingAs($client, 'api');

        $this->mockedController
            ->expects($this->once())
            ->method('logBank');

        $this->app->instance(LogoutController::class, $this->mockedController);

        $response = $this->post('/api/logoutapp');

        $response->assertOk();
    }
}
