<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\Parameterizables;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestDox;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithoutMiddleware;

class ParameterizablesControllerTest extends TestCase
{
    use WithoutMiddleware;
    use RefreshDatabase;

    #[Test]
    #[TestDox('parameterizables returns correct data for a given group')]
    public function parameterizables_returns_data_for_group(): void
    {
        Parameterizables::create([
            'name' => 'test',
            'group' => $group = 'test-group',
            'code' => 'CODE1',
            'parameter_type' => 'type1',
            'parameter' => 'param1',
            'storehouse_id' => 1,
        ]);

        $response = $this->getJson("/api/parameterizables?group={$group}");

        $response
            ->assertOk()
            ->assertJsonFragment([
                'code' => 'CODE1',
                'parameter_type' => 'type1',
                'parameter' => 'param1',
                'group' => $group,
            ]);
    }

    #[Test]
    #[TestDox('configuration returns configuration when allowed')]
    public function configuration_returns_config_when_allowed(): void
    {
        $code = 'LONGITUD_MINIMA_PASSWORD';

        $response = $this->get("/api/configuration?code={$code}");

        $response
            ->assertOk()
            ->assertJsonStructure([
                'result',
                'status',
            ]);
    }

    #[Test]
    #[TestDox('configuration returns not allowed when not allowed')]
    public function configuration_returns_not_allowed_when_denied(): void
    {
        $code = 'not-allowed';

        $response = $this->get("/api/configuration?code={$code}");

        $response
            ->assertOk()
            ->assertJsonStructure([
                'result',
                'status',
            ]);
    }
}
