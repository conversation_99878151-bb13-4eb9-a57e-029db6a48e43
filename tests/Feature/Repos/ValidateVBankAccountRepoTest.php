<?php

namespace Tests\Feature\Repos;

use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use App\Repos\ValidateVBankAccountRepo;
use Symfony\Component\HttpFoundation\Response;

class ValidateVBankAccountRepoTest extends TestCase
{
    #[Test]
    public function send_request_to_vbank_validate_account_repo(): void
    {
        $username = 'username';
        $requiresPasswordReset = false;

        Http::fake([
            'ValidateVBankAccount*' => Http::response([
                'Data' => [
                    'Username' => $username,
                    'RequirePasswordReset' => $requiresPasswordReset
                ]
            ], Response::HTTP_OK)
        ]);

        $params = [
            'CustomerId' => 'fake_customer_id',
            'Dui' => 'fake_customer_dui',
        ];

        $response = app()->make(ValidateVBankAccountRepo::class)
            ->prepare($params)
            ->fetch();

        $this->assertEquals($username, $response->username);
        $this->assertEquals($requiresPasswordReset, $response->requiresPasswordReset);
    }
}
