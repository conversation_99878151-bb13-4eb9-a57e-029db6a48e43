<?php

namespace Tests\Feature\Repos;

use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use App\Repos\ValidateVBankCredentialsRepo;

class ValidateVBankCredentialsRepoTest extends TestCase
{
    #[Test]
    public function sends_request_to_login_vbank_account_endpoint(): void
    {
        Http::fake();

        /** @var ValidateVBankCredentialsRepo */
        $repo = app()->make(ValidateVBankCredentialsRepo::class);

        $credentials = [
            'Username' => 'vbank_nickname',
            'Password' => 'vbank_password'
        ];

        $repo
            ->prepare($credentials)
            ->fetch();

        Http::assertSent(fn($request) => parse_url(
            url: $request->url(),
            component: PHP_URL_PATH
        ) === '/LoginVBankAccount');
    }
}
