<?php

namespace Tests\Feature\Repos;

use App\Repos\RefreshTokenRepo;
use Illuminate\Http\Client\Factory as HttpFactory;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\App;
use Tests\TestCase;

class RefreshTokenRepoTest extends TestCase
{
    /**
     * External endpoint to mock.
     */
    private string $externalEndpoint;

    protected function setUp(): void
    {
        parent::setUp();

        $this->externalEndpoint = config('services.baes.base_url') . 'TokenAuthorization';
    }

    /**
     * @test
     */
    public function itFetchesData(): void
    {
        App::when(RefreshTokenRepo::class)
            ->needs(HttpFactory::class)
            ->give(
                fn () => (new HttpFactory())->fake([
                    $this->externalEndpoint => HttpFactory::response(
                        ['some key' => 'some value'],
                        200,
                    )
                ])
            );
        $repo = App::make(RefreshTokenRepo::class);

        $result = $repo
            ->prepare([
                'DeviceAuth' => 'some value',
                'BiometryId' => 'some value',
                'DUI' => 'some value',
                'RefreshTokenAuthorization' => 'some value',
            ])
            ->fetchRaw();

        $this->assertSame(['some key' => 'some value'], $result);
    }

    /**
     * @test
     */
    public function itThrowsOnFailedRequest(): void
    {
        App::when(RefreshTokenRepo::class)
            ->needs(HttpFactory::class)
            ->give(
                fn () => (new HttpFactory())->fake([
                    $this->externalEndpoint => HttpFactory::response(
                        ['some key' => 'some value'],
                        403,
                    )
                ])
            );
        $repo = App::make(RefreshTokenRepo::class);

        $this->expectException(RequestException::class);
        $repo->fetch();
    }
}
