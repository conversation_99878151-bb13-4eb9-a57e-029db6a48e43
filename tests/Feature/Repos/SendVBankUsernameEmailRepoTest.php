<?php

namespace Tests\Feature\Repos;

use App\Repos\SendVBankUsernameEmailRepo;
use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;

class SendVBankUsernameEmailRepoTest extends TestCase
{
    private const FAKE_CUSTOMER_ID = 'fake_customer_id';

    private const FAKE_DEVICE_ID = 'fake_device_id';

    private const URL_PATH = '/SendUsernameByEmail';

    #[Test]
    public function sends_request_to_send_vbank_username_by_email_endpoint(): void
    {
        Http::fake();

        /** @var SendVBankUsernameEmailRepo */
        $repo = app()->make(SendVBankUsernameEmailRepo::class);

        $data = [
            'customerId' => self::FAKE_CUSTOMER_ID,
            'deviceId' => self::FAKE_DEVICE_ID
        ];

        $repo
            ->prepare($data)
            ->fetch();

        Http::assertSent(fn($request) => parse_url(
            url: $request->url(),
            component: PHP_URL_PATH
        ) === self::URL_PATH);
    }
}
