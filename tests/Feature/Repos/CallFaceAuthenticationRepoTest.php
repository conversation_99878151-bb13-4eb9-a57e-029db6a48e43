<?php

namespace Tests\Feature\Repos;

use App\DTO\FaceAuthRequestDTO;
use Tests\TestCase;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use App\Repos\CallFaceAuthenticationRepo;

class CallFaceAuthenticationRepoTest extends TestCase
{
    #[Test]
    public function sends_request_to_faceauthentication_endpoint(): void
    {
        Http::fake();

        $repo = app()->make(CallFaceAuthenticationRepo::class);

        $faceAuthData = new FaceAuthRequestDTO(
            ccApplicationId: 1234,
            method: 'ME_AUTH_FACIAL_2',
            query: '123456-7',
            imageBuffer: Str::random(60),
            deviceId: 'device_id',
            customerId: '1234'
        );

        $repo->prepare($faceAuthData)->fetchRaw();

        Http::assertSent(fn($request) => parse_url(
            url: $request->url(),
            component: PHP_URL_PATH
        ) === '/faceauthentication');
    }
}
