<?php

namespace Tests\Feature\Repos;

use App\Repos\CallFaceAuthenticationExtRepo;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class CallFaceAuthenticationExtRepoTest extends TestCase
{
    #[Test]
    public function path_points_to_faceauthenticationext_instead_of_faceauthentication(): void
    {
        $repo = app()->make(CallFaceAuthenticationExtRepo::class);

        $repoReflection = new \ReflectionClass($repo);

        $repoPathProperty = $repoReflection->getProperty('path');

        $repoPathProperty->setAccessible(true);

        $repoPath = $repoPathProperty->getValue($repo);

        $this->assertEquals('faceauthenticationext', $repoPath);
    }
}
