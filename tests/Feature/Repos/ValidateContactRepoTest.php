<?php

namespace Tests\Feature\Repos;

use App\Repos\ValidateContactRepo;
use Illuminate\Http\Client\Factory as HttpFactory;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\App;
use Tests\TestCase;

class ValidateContactRepoTest extends TestCase
{
    /**
     * External endpoint to mock.
     */
    private string $externalEndpoint;

    protected function setUp(): void
    {
        parent::setUp();

        $this->externalEndpoint = config('services.baes.base_url') . 'CCApplicationContacts' . '*';
    }

    /**
     * @test
     */
    public function itFetchesData(): void
    {
        App::when(ValidateContactRepo::class)
            ->needs(HttpFactory::class)
            ->give(
                fn () => (new HttpFactory())->fake([
                    $this->externalEndpoint => HttpFactory::response(
                        ['some key' => 'some value'],
                        200,
                    )
                ])
            );
        $repo = App::make(ValidateContactRepo::class);

        $result = $repo
            ->prepare('12345678')
            ->fetchRaw();

        $this->assertSame(['some key' => 'some value'], $result);
    }

    /**
     * @test
     */
    public function itThrowsOnFailedRequest(): void
    {
        App::when(ValidateContactRepo::class)
            ->needs(HttpFactory::class)
            ->give(
                fn () => (new HttpFactory())->fake([
                    $this->externalEndpoint => HttpFactory::response(
                        ['some key' => 'some value'],
                        403,
                    )
                ])
            );
        $repo = App::make(ValidateContactRepo::class);

        $this->expectException(RequestException::class);
        $repo->fetch();
    }

    /**
     * @test
     */
    public function itReturnsWhetherAContactExists(): void
    {
        App::when(ValidateContactRepo::class)
            ->needs(HttpFactory::class)
            ->give(
                fn () => (new HttpFactory())->fake([
                    $this->externalEndpoint => HttpFactory::response(
                        ['Data' => true],
                        200,
                    )
                ])
            );
        $repo = App::make(ValidateContactRepo::class);

        $this->assertTrue($repo->contactExists('12345678'));
    }
}
