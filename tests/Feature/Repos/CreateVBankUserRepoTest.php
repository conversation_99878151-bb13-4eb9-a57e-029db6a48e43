<?php

namespace Tests\Feature\Repos;

use App\Repos\CreateVBankUserRepo;
use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;

class CreateVBankUserRepoTest extends TestCase
{
    #[Test]
    public function sends_request_to_create_vbank_user_endpoint(): void
    {
        $customerId = "fake_customer_id";

        Http::fake([
            'CreateVBankUser*' => Http::response([
                'Data' => [
                    "CustomerId" => $customerId
                ],
                'Status' => [
                    'ResponseStatus' => [
                        'Code' => Response::HTTP_OK
                    ]
                ]
            ]),
        ]);

        $params = [
            'customerId' => $customerId,
            'deviceId' => 'fake_device_id',
        ];

        /** @var CreateVBankUserRepo */
        $createVBankUserRepo = app()->make(CreateVBankUserRepo::class);
        $response = $createVBankUserRepo->prepare($params)->fetch();

        $this->assertEquals($customerId, data_get($response, 'Data.CustomerId'));
        $this->assertEquals(Response::HTTP_OK, data_get($response, 'Status.ResponseStatus.Code'));
    }
}
