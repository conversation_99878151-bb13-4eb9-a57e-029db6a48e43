<?php

namespace Tests\Feature\Repos;

use App\Dtos\ExternalAuthBiometry;
use App\Repos\ValidateTokenRepo;
use Illuminate\Http\Client\Factory as Http;
use Illuminate\Http\Request;
use Mockery;
use ReflectionProperty;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class ValidateTokenRepoTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itPreparesFromExternalAuthBiometry(): void
    {
        // Set up.
        $request = Mockery::mock(Request::class)->makePartial();
        $request
            ->shouldReceive('bearerToken')
            ->andReturn('some token');
        $request
            ->shouldReceive('ip')
            ->andReturn('some ip');
        $request
            ->shouldReceive('header')
            ->andReturn('some header');
        $http = Mockery::mock(Http::class)->makePartial();
        $repo = new ValidateTokenRepo($http, $request);

        $dto = ExternalAuthBiometry::make(
            'device id',
            'ip',
            'token',
            'dui',
            'biometry',
        );

        $body = new ReflectionProperty(ValidateTokenRepo::class, 'body');

        // Preassert.
        $this->assertEquals([], $body->getValue($repo));

        // Run.
        $repo->prepare($dto);

        // Assert.
        $this->assertEquals([
            'DeviceAuth' => 'device id',
            'IpAddressAuth' => 'ip',
            'TokenAuthorization' => 'token',
            'DUI' => 'dui',
            'BiometryId' => 'biometry',
        ], $body->getValue($repo));
    }
}
