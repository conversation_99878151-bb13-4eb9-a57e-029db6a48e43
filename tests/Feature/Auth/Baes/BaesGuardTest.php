<?php

namespace Tests\Feature\Auth\Baes;

use App\Auth\Baes\BaesGuard;
use App\Dtos\ExternalToken;
use App\Dtos\LoginCredentials;
use App\Dtos\LoginForToken;
use App\Models\Client;
use App\Tasks\Auth\GenerateTokenForUserTask;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;
use Tests\Traits\ClosesMockery;

class BaesGuardTest extends TestCase
{
    use ClosesMockery;

    /**
     * @test
     */
    public function itValidatesCredentials(): void
    {
        $guard = App::make(BaesGuard::class);
        $password = Str::random();
        $user = Client::factory()->create(['password' => bcrypt($password)]);

        $result = $guard->validate([
            'nickname' => $user->nickname,
            'password' => $password,
        ]);

        $this->assertTrue($result);
    }

    /**
     * @test
     */
    public function itFailsToValidateEmptyCredentials(): void
    {
        $guard = App::make(BaesGuard::class);

        $this->assertFalse($guard->validate([]));
    }

    /**
     * @test
     */
    public function itFailsToValidateWrongCredentials(): void
    {
        $guard = App::make(BaesGuard::class);
        $password = Str::random();
        $user = Client::factory()->create(['password' => bcrypt($password)]);

        $result = $guard->validate([
            'nickname' => $user->nickname,
            'password' => 'wrong password',
        ]);
        $this->assertFalse($result);

        $result = $guard->validate([
            'nickname' => Str::random(),
            'password' => $password,
        ]);
        $this->assertFalse($result);
    }

    /**
     * @test
     */
    public function itReturnsToken(): void
    {
        $password = Str::random();
        $user = Client::factory()->create(['password' => bcrypt($password)]);
        $credentials = [
            'nickname' => $user->nickname,
            'password' => $password,
        ];
        $deviceId = Str::random();
        $token = ExternalToken::make(Str::random(), Str::random());

        $task = Mockery::mock(GenerateTokenForUserTask::class);
        $task
            ->shouldReceive('withGenerateExternalToken')
            ->once()
            ->andReturn($task);
        $task
            ->shouldReceive('do')
            ->once()
            ->andReturn($token);

        $guard = App::makeWith(BaesGuard::class, ['task' => $task]);

        $result = $guard
            ->login(LoginForToken::make(
                LoginCredentials::fromArray($credentials),
                $deviceId,
                $user->client_id,
            ))
            ->token;

        $this->assertSame($token->token, $result);
    }
}
