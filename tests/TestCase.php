<?php

namespace Tests;

use Illuminate\Foundation\Testing\RefreshDatabaseState;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Artisan;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        $this->runMigrations();
    }

    private function runMigrations(): void
    {
        if (RefreshDatabaseState::$migrated) {
            return;
        }

        Artisan::call('migrate:fresh');
        RefreshDatabaseState::$migrated = true;
    }
}
