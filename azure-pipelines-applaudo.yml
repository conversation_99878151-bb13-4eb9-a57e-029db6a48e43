# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

pool:
  vmImage: ubuntu-latest
trigger:
  branches:
    include:
      - '*'
pr:
  branches:
    include:
      - '*'   # Triggers on all PRs
resources:
  repositories:
  - repository: templates
    type: bitbucket
    name: 'applaudostudios/azure-shared-pipelines'
    endpoint: 'BITBUCKET_SERVICE' # Azure DevOps service connection
    ref: atlantida
stages:
- template: php-docker-compose-pipeline.yml@templates
  parameters:
    envFile: '.env'
    vmImage: 'ubuntu-latest'
    secretPath: 'atlantida-one-app-api-azure'
    enginePath: 'applaudo-jenkins'
    vaultKeys:
      - ACTIVE_DIRECTORY
      - ACTIVE_JOB
      - ANALYTICS_VIEW_ID
      - API_KEY
      - API_KEY_EXTERNAL_SERVICES
      - APP_DEBUG
      - APP_ENV
      - APP_KEY
      - APP_NAME
      - APP_URL
      - BASE_URL_EXTERNAL
      - BROADCAST_DRIVER
      - CACHE_DRIVER
      - DB_DATABASE
      - DB_HOST
      - DB_PASSWORD
      - DB_PORT
      - DB_USERNAME
      - JWT_SECRET
      - JWT_TTL
      - LOG_CHANNEL
      - LOG_LEVEL
      - MIX_PUSHER_APP_CLUSTER
      - MIX_PUSHER_APP_KEY
      - NOVA_GOOGLE_MAPS_API_KEY
      - Other
      - QUEUE_CONNECTION
      - REDIS_HOST
      - REDIS_PASSWORD
      - REDIS_PORT
      - SESSION_DOMAIN
      - SESSION_DRIVER
      - SESSION_LIFETIME
      - TOKEN_FIREBASE
      - APPCENTER_APP_NAME
