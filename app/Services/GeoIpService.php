<?php

namespace App\Services;

use App\Dtos\Coordinates;
use App\Events\CantFindLocationEvent;
use App\Events\CantReadGeoDbEvent;
use GeoIp2\Database\Reader;
use GeoIp2\Exception\AddressNotFoundException;
use Illuminate\Support\Facades\Storage;
use InvalidArgumentException;
use MaxMind\Db\Reader\InvalidDatabaseException;

class GeoIpService
{
    protected string $dbPath;

    public function getCoordinates(string $ip): Coordinates
    {
        $dbPath = Storage::disk('geo')->path('GeoLite2-City.mmdb');

        try {
            $geoIp = new Reader($dbPath);
            $city = $geoIp->city($ip)->location;

            if (!is_null($city->longitude)) {
                return Coordinates::fromFloat(
                    $city->longitude,
                    $city->latitude,
                );
            }
        } catch (InvalidDatabaseException $_) {
            CantReadGeoDbEvent::dispatch($dbPath);
        } catch (InvalidArgumentException | AddressNotFoundException $_) {
            // IP is not valid or it was not found in database
            CantFindLocationEvent::dispatch($ip);
        }

        return Coordinates::zero();
    }
}
