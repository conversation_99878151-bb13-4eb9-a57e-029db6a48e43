<?php

namespace App\Services;

use App\Events\NewUserRegistrationRequestEvent;
use App\Tasks\GetCustomerContactChannelsTask;
use App\Tasks\GetCustomerTask;

class RegisterNewUserService
{
    public function __construct(
        private readonly GetCustomerTask $getCustomerTask,
        private readonly GetCustomerContactChannelsTask $getCustomerContactChannelsTask,
    ) {
    }

    /**
     * Registers a new user in the platform by using external services for the info.
     */
    public function register(int $customerId, int $creditcardApplicationId): void
    {
        $customer = $this->getCustomerTask
            ->withCustomerId($customerId)
            ->do();

        $contactChannels = $this->getCustomerContactChannelsTask
            ->withCustomerId($customerId)
            ->do();

        event(new NewUserRegistrationRequestEvent($creditcardApplicationId, $customer, $contactChannels));
    }
}
