<?php

namespace App\Services;

use App\Enums\Code\Data;
use App\Models\MaintenanceModes;
use Carbon\Carbon;

class MaintenanceService
{
    private static $instance = null;


    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    /**
     * Exist maintenance
     *
     * @return MaintenanceModes | bool
     */
    public function getMaintenanceModeIfExists(): MaintenanceModes | bool
    {
        $today = Carbon::now()->tz("America/El_Salvador");
        $now = $today->format(Data::DATE_HOURS_FORMAT->value);
        $maintenance = MaintenanceModes::where('status', 'Activo')
        ->whereRaw("(CONVERT(datetime, '$now') >= DATEADD(minute, -previous_block, date_time_maintenance_start) AND
                CONVERT(datetime, '$now') < date_time_maintenance_end)")->first();
        return !empty($maintenance) ? $maintenance : false;
    }
}
