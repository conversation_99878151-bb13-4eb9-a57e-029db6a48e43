<?php

namespace App\Services;

use Illuminate\Contracts\Foundation\Application as App;
use Illuminate\Log\LogManager;

/**
 * Extends <PERSON><PERSON>'s logger.
 *
 * Composition is used because <PERSON><PERSON> boots the logger with some config.
 */
class Log
{
    /**
     * Whether the environment is production.
     *
     * @var bool
     */
    protected bool $isProduction = true;

    public function __construct(
        protected LogManager $log,
        App $app,
    ) {
        $this->isProduction = $app->environment('production');
    }

    /**
     * Generates a debug log.
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log->debug($message, $context);
    }

    /**
     * Generates an info log.
     */
    public function info(string $message, array $context = []): void
    {
        $this->log->info($message, $context);
    }

    /**
     * Generates a warning log.
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log->warning($message, $context);
    }

    /**
     * Generates an error log.
     */
    public function error(string $message, array $context = []): void
    {
        $this->log->error($message, $context);
    }
}
