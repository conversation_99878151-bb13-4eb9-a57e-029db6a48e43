<?php

namespace App\Services;

use App\Models\Client;
use Illuminate\Http\Request;

class DeviceService
{
    /**
     * refresh token by device
     *
     * @param Request $request
     * @param Client $clientAuth
     * @return
     */
    public function updateBiometricToDevice(Request $request, Client $client): void
    {
        $client->devices()
            ->active()
            ->where('device_id', $request->device_id)
            ->update(['biometric_public_key' => base64_decode($request->biometric_public_key)]);
    }
}
