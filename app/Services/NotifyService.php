<?php

namespace App\Services;

use App\Helpers\Notify;
use App\Models\NotificationsTemplates;

class NotifyService
{
    /**
     * Send notifications
     *
     * @param array $paramsDeepLink
     * @param array $tokens
     * @param array $tokensHuawei
     * @param NotificationsTemplates $template
     * @return string
     */
    public function sendNotificationCustom(
        array $paramsDeepLinks,
        array $tokens,
        array $tokensHuawei,
        NotificationsTemplates $template
    ): array {
        $dataDeepLink = ['link' => $paramsDeepLinks];

        $notify = new Notify();
        $notify->title = $template->title;
        $notify->body = $template->content;
        $notify->data = $dataDeepLink;
        $result = '';

        if (!empty($tokens)) {
            $notify->registrationIds = $tokens;
            $result = $notify->sendNotify();
        }


        $huaweiResult = '';
        if (!empty($tokensHuawei)) {
            $notify->huaweiTokenIds = $tokensHuawei;
            $huaweiResult = $notify->sendNotificationHUAWEI();
        }

        return [$result, str_replace('\'', '', $huaweiResult)];
    }
}
