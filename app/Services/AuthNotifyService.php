<?php

namespace App\Services;

use App\Factory\ClientFactory;
use App\Helpers\NotifySender;
use App\Models\Client;

class AuthNotifyService
{
    private ClientFactory $clientFactory;

    public function __construct(ClientFactory $http)
    {
        $this->clientFactory = $http;
    }

    /**
     * Channels otp email
     *
     * @var string
     */
    private $channelsEmail = '[3,1]';

    /**
     * Channels otp others
     *
     * @var string
     */
    private $channels = '[2]';

    /**
     * Send success login notification
     *
     * @param Client $client
     * @return void
     */
    public function successLogin(Client $client): void
    {
        NotifySender::init($this->clientFactory)
            ->client($client)
            ->template('APP_NOTISE')
            ->subjectByTemplateCode()
            ->addParameter('Browser', 'App')
            ->addParameter('Version', 'One')
            ->chanelID(NotifySender::EMAIL)
            ->alternativeChanelsIDs($this->channelsEmail)
            ->send();
    }

    /**
     * Send fail login notification
     *
     * @param Client $client
     * @return void
     */
    public function failLoginAttempt(Client $client): void
    {
        NotifySender::init($this->clientFactory)
            ->client($client)
            ->template('APP_FAINSE')
            ->subjectByTemplateCode()
            ->chanelID(NotifySender::EMAIL)
            ->alternativeChanelsIDs($this->channels)
            ->send();
    }

    /**
     * Send block time by login attempts notification
     *
     * @param Client $client
     * @return void
     */
    public function blockTime(Client $client): void
    {
        NotifySender::init($this->clientFactory)
            ->client($client)
            ->template('APP_BLOUSU')
            ->subjectByTemplateCode()
            ->chanelID(NotifySender::EMAIL)
            ->alternativeChanelsIDs($this->channels)
            ->send();
    }
}
