<?php

namespace App\Services;

use App\Models\Configurations as ConfigurationsModel;

class Configurations
{
    /**
     * Stablish configurations for being requested for the mobile app.
     * If there is another new configuration for the mobile app, add the key here.
     *
     * @var array
     */
    private static $allowForRequest = [
      'LONGITUD_MINIMA_PASSWORD',
      'LONGITUD_MAXIMA_PASSWORD',
      'DIAS_VALIDOS_PREVIEW_OFERTA',
      'TASA_INTERES_CASHBACK',
      'MODULO_RSA_PIN',
    ];

    private static $instance = null;


    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }


    /**
     * Verify if a given key configurations is allowed to request
     *
     * @param string $key
     * @return boolean
     */
    public function isAllowedToRequest(string $key): bool
    {
        return in_array($key, self::$allowForRequest);
    }


    /**
     * Get general settings of the BAES DB
     *
     * @param string $key
     * @return bool|string
     */
    public function getConfigurations(string $key): null | string
    {
        $valor = ConfigurationsModel::select("value")
                                    ->where("key", $key)
                                    ->first();

        if (is_null($valor) || empty($valor)) {
            return null;
        }

        return $valor->value;
    }

    /**
     * Get configuratios LOGIN ALLOWED ATTEMPTS
     *
     * @return $value
     */
    public function maxAttempts(): int
    {
        return (int)  $this->getConfigurations("INTENTOS_PERMITIDOS_LOGIN");
    }

    /**
     * Get configuratios time max block
     *
     * @return $value
     */
    public function decayMinutes(): int
    {
        return (int) $this->getConfigurations("TIEMPO_MAXIMO_BLOQUEO");
    }

    /**
     * Get configuratios time life token
     *
     * @return $value
     */
    public function tokenTimeLife(): int
    {
        return (int)  $this->getConfigurations("TIEMPO_VIDA_TOKEN");
    }

    /**
     * Get configuratios max number devices
     *
     * @return $value
     */
    public function maximunDevices(): int
    {
        return (int)  $this->getConfigurations("MAXIMO_DISPOSITIVOS_REGISTRADOS");
    }
}
