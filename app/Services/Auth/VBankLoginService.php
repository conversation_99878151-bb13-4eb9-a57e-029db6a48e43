<?php

declare(strict_types=1);

namespace App\Services\Auth;

use App\Dtos\Login;
use App\Models\Client;
use App\Dtos\LoginSession;
use App\Dtos\LoginForToken;
use Illuminate\Auth\AuthManager;
use App\Enums\Message\AuthMessage;
use App\Services\Auth\LoginService;
use App\DTO\ExternalRequestResponse;
use App\Enums\Code\PasswordLoginCode;
use App\Events\SuccessfulLoginEvent;
use App\Exceptions\Auth\UserIsDisabled;
use App\Tasks\CheckUserCanUseDeviceTask;
use App\Exceptions\Auth\WrongCredentials;
use App\Enums\Code\VBankLoginResponseCodes;
use App\Services\Configurations as Config;
use App\Tasks\Auth\CheckPasswordPeriodTask;
use App\Tasks\Auth\CheckSignedChallengeTask;
use Symfony\Component\HttpFoundation\Response;
use App\Tasks\Auth\ValidateVBankCredentialsTask;
use App\Exceptions\Auth\UnknownAuthenticationError;

class VBankLoginService extends LoginService
{
    /**
     * A list of response codes that indicate a successful login.
     */
    protected array $successfulResponses = [
        Response::HTTP_OK,
        VBankLoginResponseCodes::SUCCESSFUL_LOGIN->value
    ];

    /**
     * A mapping of response codes to exception classes.
     * 
     * @var array<int, class-string<\Throwable>> 
     */
    protected array $exceptionMap = [
        Response::HTTP_NOT_FOUND => WrongCredentials::class,
        VBankLoginResponseCodes::USER_NOT_FOUND->value => WrongCredentials::class,
        VBankLoginResponseCodes::WRONG_PASSWORD->value => WrongCredentials::class,
        VBankLoginResponseCodes::USER_WITHOUT_EBANK_CONTRACT->value => WrongCredentials::class,
        VBankLoginResponseCodes::USER_MIGRATED->value => WrongCredentials::class,
        VBankLoginResponseCodes::USER_DISABLED->value => UserIsDisabled::class,
        VBankLoginResponseCodes::USER_BLOCKED->value => UserIsDisabled::class,
        VBankLoginResponseCodes::USER_BLOCK_BY_TOO_MANY_ATTEMPTS->value => UserIsDisabled::class,
    ];

    public function __construct(
        protected CheckSignedChallengeTask $checkSignedChallengeTask,
        CheckUserCanUseDeviceTask $checkUserCanUseDeviceTask,
        CheckPasswordPeriodTask $checkPasswordPeriodTask,
        AuthManager $authManager,
        Config $config,
        private readonly ValidateVBankCredentialsTask $validateCredentialsTask
    ) {
        parent::__construct(
            $checkUserCanUseDeviceTask,
            $checkPasswordPeriodTask,
            $authManager,
            $config
        );

        $this->auth = auth()->guard('api');
    }

    /**
     * Logs in a user with the provided login information and client.
     *
     * @param Login $loginInfo The login information containing credentials and device details.
     * @param Client|null $client The client information, if available.
     * @return LoginSession The login session containing the authentication token and client details.
     * @throws \Throwable If an error occurs during the login process.
     */
    public function login(mixed $loginInfo, ?Client $client = null): LoginSession
    {
        $this->validate($client);

        $this->validateUserCanUseDevice($client, $loginInfo->device->id);

        $result = $this->validateCredentialsTask
            ->withCredentials($loginInfo->credentials)
            ->do();

        $responseData = ExternalRequestResponse::fromExternalResponse($result);

        $requiresPasswordReset = $this->validateResponse($responseData, $client);

        $tokenInfo = LoginForToken::make(
            $loginInfo->credentials,
            $loginInfo->device->id,
            $client->client_id
        );

        $token = $this->authenticate($client, $tokenInfo);

        SuccessfulLoginEvent::dispatch($client, $loginInfo, $token);

        $this->clearLoginAttempts(request: $loginInfo->request);

        $code = $requiresPasswordReset
            ? PasswordLoginCode::REQUIRES_PASSWORD_RESET->value
            : PasswordLoginCode::SUCCESSFUL_LOGIN->value;

        $message = $requiresPasswordReset ? AuthMessage::ENTER_PASSWORD->value : null;

        return new LoginSession(
            token: $token,
            clientId: (string) $client->client_id,
            creditcardApplicationId: (int) $client->creditcard_application_id,
            message: $message,
            code: $code,
        );
    }

    protected function validate(Client $client): void
    {
        $this->validateLoginAttempts();
        $this->validateDisabledUser($client);
        $this->validateUserIsBlockByOTP($client);
        $this->validateUserIsBlockByFaceAuth($client);
        $this->validateUserHasDefaultSystem($client);
    }

    /**
     * Validates the response from an external request and handles any errors.
     *
     * This method checks if the response status code indicates a successful response.
     * If the response is not successful, it handles the error response accordingly.
     * Finally, it returns whether a password reset is required based on the response data.
     *
     * @throws \Throwable If an error occurs during the validation process.
     */
    protected function validateResponse(ExternalRequestResponse $response, Client $client): bool
    {
        $responseStatusCode = $response->responseStatusCode;

        if (!$this->isSuccessfulResponse($responseStatusCode, $response)) {
            $this->handleErrorResponse($responseStatusCode, $client);
        }

        return data_get($response->data, 'RequirePasswordReset');
    }

    /**
     * Determines if the response is successful based on the status code and the presence of a password reset
     *  requirement.
     *
     * This method checks if the provided status code is within the list of successful responses and if the response
     *  data contains a 'RequirePasswordReset' field.
     */
    private function isSuccessfulResponse(?int $statusCode, ExternalRequestResponse $response): bool
    {
        $requiresPasswordReset = data_get($response->data, 'RequirePasswordReset');

        return in_array($statusCode, $this->successfulResponses, true)
            && isset($requiresPasswordReset);
    }

    /**
     * Handles error responses by throwing appropriate exceptions based on the status code.
     *
     * @throws UnknownAuthenticationError If the status code is not mapped to any exception.
     * @throws \Throwable If the status code corresponds to a known error from the exception map.
     */
    private function handleErrorResponse(?int $statusCode, Client $client): void
    {
        $this->incrementLoginAttempts(request());

        if (!isset($this->exceptionMap[$statusCode])) {
            throw new UnknownAuthenticationError();
        }

        $exceptionClass = $this->exceptionMap[$statusCode];

        if ($exceptionClass === WrongCredentials::class) {
            throw new $exceptionClass(request()->ip(), $client->nickname_vbank);
        } else {
            throw new $exceptionClass();
        }
    }
}
