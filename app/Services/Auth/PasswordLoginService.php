<?php

declare(strict_types=1);

namespace App\Services\Auth;

use App\Services\ClientService;
use App\Dtos\Login;
use App\Models\Client;
use App\Dtos\LoginSession;
use App\Dtos\LoginForToken;
use App\Dtos\LoginCredentials;
use App\Enums\Code\PasswordLoginCode;
use App\Services\Configurations;
use Illuminate\Auth\AuthManager;
use App\Enums\Message\AuthMessage;
use App\Events\SuccessfulLoginEvent;
use App\Events\WrongCredentialsEvent;
use App\Tasks\ValidateVBankAccountTask;
use App\Tasks\CheckUserCanUseDeviceTask;
use App\Exceptions\Auth\WrongCredentials;
use App\Tasks\Auth\CheckPasswordPeriodTask;
use App\Tasks\Auth\CheckFirstLoginPeriodTask;
use App\Exceptions\Auth\InvalidFirstLoginPeriod;

class PasswordLoginService extends LoginService
{
    public function __construct(
        protected ClientService $clientService,
        protected CheckFirstLoginPeriodTask $checkFirstLoginPeriodTask,
        protected CheckUserCanUseDeviceTask $checkUserCanUseDeviceTask,
        protected CheckPasswordPeriodTask $checkPasswordPeriodTask,
        AuthManager $authManager,
        Configurations $config,
        protected ?ValidateVBankAccountTask $validateVBankAccountTask
    ) {
        parent::__construct(
            checkUserCanUseDeviceTask: $checkUserCanUseDeviceTask,
            checkPasswordPeriodTask: $checkPasswordPeriodTask,
            validateVBankAccountTask: $validateVBankAccountTask,
            authManager: $authManager,
            config: $config
        );
    }

    /**
     * @param Login $loginInfo
     */
    public function login(mixed $loginInfo, ?Client $client = null): LoginSession
    {
        $this->validateCredentials($client, $loginInfo->credentials);

        $this->validate($client);

        $this->validateUserCanUseDevice($client, $loginInfo->device->id);

        $client->update(['password_login_attempts' => 0]);

        $tokenInfo = LoginForToken::make(
            $loginInfo->credentials,
            $loginInfo->device->id,
            $client->client_id
        );

        $token = $this->authenticate($client, $tokenInfo);

        // If user is using a temporary password, let them know that it has to be changed.
        $message = $client->hasOnlyFirstPassword()
            ? AuthMessage::ENTER_PASSWORD->value
            : null;

        $code = $client->hasOnlyFirstPassword()
            ? PasswordLoginCode::REQUIRES_PASSWORD_RESET
            : PasswordLoginCode::SUCCESSFUL_LOGIN;

        $homologationCode = $client->homologated
            ? $code->getCodeForHomologatedUsers()?->value
            : $this->getUserHomologationPathCode($client);

        $sessionData = new LoginSession(
            token: $token,
            clientId: (string) $client->client_id,
            creditcardApplicationId: (int) $client->creditcard_application_id,
            message: $message,
            code: $code->value,
            homologationCode: $homologationCode->value
        );

        SuccessfulLoginEvent::dispatch($client, $loginInfo, $token);

        $this->clientService->resetPasswordAttempts($client);

        return $sessionData;
    }

    protected function validate(Client $client): void
    {
        $this->validateDisabledUser($client);
        $this->validateFirstLoginPeriod($client);
        $this->validatePasswordExpirationPeriod($client);
        $this->validateUserIsBlockByOTP($client);
        $this->validateUserIsBlockByFaceAuth($client);
        $this->validateUserHasDefaultSystem($client);
    }

    /**
     * Calculates user remaining login attempts.
     */
    protected function calculateAttemptsLeft(?Client $user): int
    {
        if (is_null($user)) {
            return $this->maxAttempts;
        }

        $attemptsLeft = $this->maxAttempts - $user->fresh()->password_login_attempts;

        if ($attemptsLeft < 0) {
            $attemptsLeft = 0;
        }

        return $attemptsLeft;
    }

    /**
     * Validates login credentials.
     *
     * @throws WrongCredentials
     */
    protected function validateCredentials(Client $client, LoginCredentials $credentials): void
    {
        if ($this->auth->validate($credentials->toArray())) {
            return;
        }

        WrongCredentialsEvent::dispatch(request(), $credentials->nickname);

        $remainingAttempts = $this->calculateAttemptsLeft($client);

        throw new WrongCredentials(
            request()->ip(),
            $credentials->nickname,
            $remainingAttempts
        );
    }

    /**
     * Validates if user is sign in within the predefined first login valid period. Usually 24 hours.
     *
     * @throws InvalidFirstLoginPeriod
     */
    protected function validateFirstLoginPeriod(Client $client): void
    {
        $isFirstLoginPeriodValid = $this->checkFirstLoginPeriodTask
            ->withUser($client)
            ->do();

        if (!$isFirstLoginPeriodValid) {
            throw new InvalidFirstLoginPeriod();
        }
    }
}
