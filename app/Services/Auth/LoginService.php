<?php

namespace App\Services\Auth;

use App\Enums\Code\HomologatedLoginCode;
use Exception;
use App\Models\Client;
use App\Dtos\LoginSession;
use App\Dtos\ExternalToken;
use App\Dtos\LoginForToken;
use App\Auth\Baes\BaesGuard;
use Illuminate\Auth\AuthManager;
use App\Events\UserIsDisabledEvent;
use App\Events\MaxDevicesReachedEvent;
use App\Exceptions\Auth\UserIsDisabled;
use App\Tasks\ValidateVBankAccountTask;
use App\Tasks\CheckUserCanUseDeviceTask;
use App\Events\UserCantAccessSystemEvent;
use App\Exceptions\Auth\MaxDevicesReached;
use App\Services\Configurations as Config;
use App\Exceptions\Auth\UserIsBlockedByOtp;
use App\Tasks\Auth\CheckPasswordPeriodTask;
use App\Exceptions\Auth\TooManyLoginAttempts;
use App\Exceptions\Auth\UserCantAccessSystem;
use App\Exceptions\Auth\InvalidPasswordPeriod;
use Illuminate\Foundation\Auth\ThrottlesLogins;
use App\Exceptions\Auth\UserIsBlockedByFaceAuth;
use App\Events\TooManyBiometricLoginAttemptsEvent;
use App\Exceptions\Auth\UnknownAuthenticationError;
use App\Exceptions\Auth\ExternalTokenGenerationFailed;

abstract class LoginService
{
    use ThrottlesLogins;

    /**
     * Auth manager.
     */
    protected BaesGuard $auth;

    /**
     * The failed login max attempts before raising an error.
     *
     * @see ThrottlesLogins
     */
    protected int $maxAttempts;

    public function __construct(
        protected CheckUserCanUseDeviceTask $checkUserCanUseDeviceTask,
        protected CheckPasswordPeriodTask $checkPasswordPeriodTask,
        AuthManager $authManager,
        Config $config,
        protected ?ValidateVBankAccountTask $validateVBankAccountTask = null,
    ) {
        $this->auth = $authManager->guard('api');
        $this->maxAttempts = $config->maxAttempts() ?? 5;
    }

    /**
     * Attemps authentication.
     */
    abstract public function login(mixed $loginInfo, ?Client $client = null): LoginSession;

    /**
     * Allows login services to customize validation using existing validation methods 
     * or defining its own inside each service.
     */
    abstract protected function validate(Client $client): void;

    /**
     * Tries to generate external token if it succeds it logs the users essentially authenticating them.
     *
     * @throws ExternalTokenGenerationFailed
     */
    protected function authenticate(Client $client, LoginForToken $tokenInfo): ExternalToken
    {
        $token = $this->auth->login($tokenInfo);

        if (!$token) {
            throw new ExternalTokenGenerationFailed();
        }

        $this->auth->setUser($client);

        return $token;
    }

    /**
     * Get the login username to be used by the controller to throttle requests.
     *
     * @see ThrottlesLogins
     */
    public function username(): string
    {
        return 'email';
    }

    /**
     * Validates if user is blocked by OTP.
     *
     * @throws UserIsBlockedByOtp
     */
    protected function validateUserIsBlockByOTP(Client $client): void
    {
        if ($client->isBlockedByOtp()) {
            throw new UserIsBlockedByOtp();
        }
    }

    /**
     * Validates if user is blocked by face authentication.
     *
     * @throws UserIsBlockedByFaceAuth
     */
    protected function validateUserIsBlockByFaceAuth(Client $client): void
    {
        if ($client->isBlockedByFaceAuth()) {
            throw new UserIsBlockedByFaceAuth();
        }
    }

    /**
     * Validates if user has access to default system. Usually the defaul system is APP.
     *
     * @throws UserCantAccessSystem
     */
    protected function validateUserHasDefaultSystem(Client $client): void
    {
        if (!$client->hasDefaultSystem()) {
            UserCantAccessSystemEvent::dispatch(request(), $client);
            throw new UserCantAccessSystem();
        }
    }

    /**
     * Validates if user password is expired. Usually the expiration period is 60 days.
     *
     * @throws InvalidPasswordPeriod
     */
    protected function validatePasswordExpirationPeriod(Client $client): void
    {
        $isPasswordPeriodValid = $this->checkPasswordPeriodTask
            ->withUser($client)
            ->do();

        if (!$isPasswordPeriodValid) {
            throw new InvalidPasswordPeriod();
        }
    }

    /**
     * Validates if user is disabled.
     *
     * @throws UserIsDisabled
     */
    protected function validateDisabledUser(?Client $user): void
    {
        if ($user && $user->isDisabled()) {
            UserIsDisabledEvent::dispatch($user);
            throw new UserIsDisabled();
        }
    }

    /**
     * Validates whether the device used is valid or if user has reached maximum registered devices.
     *
     * @throws MaxDevicesReached
     */
    protected function validateUserCanUseDevice(Client $client, string $deviceId): void
    {
        $canUserUseDevice = $this
            ->checkUserCanUseDeviceTask
            ->withUser($client)
            ->withDeviceId($deviceId)
            ->do();

        if (!$canUserUseDevice) {
            MaxDevicesReachedEvent::dispatch($client);
            throw new MaxDevicesReached();
        }
    }

    /**
     * Validates if user has exceeded or not the maximum login attemps. Usually the maximum login attempts is 3.
     *
     * @throws \App\Exceptions\Auth\TooManyLoginAttempts
     */
    protected function validateLoginAttempts(): void
    {
        if (!$this->hasTooManyLoginAttempts(request())) {
            return;
        }

        TooManyBiometricLoginAttemptsEvent::dispatch(request());
        throw new TooManyLoginAttempts(request()->ip(), $this->decayMinutes());
    }

    protected function getUserHomologationPathCode(Client $client): ?HomologatedLoginCode
    {
        if ($client->homologated) {
            return null;
        }

        try {
            $vBankAccount = $this->validateVBankAccountTask
                ->withCustomerId((string) $client->client_id)
                ->do();

            return isset($vBankAccount)
                ? HomologatedLoginCode::NON_HOMOLOGATED_USER_VBANK
                : HomologatedLoginCode::NON_HOMOLOGATED_USER_NO_VBANK;
        } catch (Exception) {
            throw new UnknownAuthenticationError();
        }
    }
}
