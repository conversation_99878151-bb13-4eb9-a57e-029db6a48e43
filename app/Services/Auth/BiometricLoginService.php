<?php

namespace App\Services\Auth;

use App\Models\Client;
use App\Models\Device;
use App\Dtos\LoginSession;
use App\Dtos\LoginForToken;
use App\Dtos\BiometricLogin;
use App\Dtos\LoginCredentials;
use App\Enums\Code\HomologatedLoginCode;
use Illuminate\Auth\AuthManager;
use App\Exceptions\Auth\DeviceNotFound;
use App\Tasks\CheckUserCanUseDeviceTask;
use App\Services\Configurations as Config;
use App\Tasks\Auth\CheckPasswordPeriodTask;
use App\Tasks\Auth\CheckSignedChallengeTask;
use App\Events\SuccessfulBiometricLoginEvent;
use App\Events\WrongBiometricCredentialsEvent;
use App\Exceptions\Auth\WrongBiometricCredentials;
use App\Events\DeviceNotFoundOnBiometricLoginEvent;
use App\Exceptions\Auth\DeviceDoesNotHaveChallenge;
use App\Exceptions\Auth\DeviceDoesNotHaveAPublicKey;
use App\Events\FailedToBiometricLoginDueToLackOfChallengeEvent;
use App\Events\FailedToBiometricLoginDueToLackOfPublicKeyEvent;
use App\Tasks\ValidateVBankAccountTask;

class BiometricLoginService extends LoginService
{
    public function __construct(
        protected CheckSignedChallengeTask $checkSignedChallengeTask,
        protected CheckUserCanUseDeviceTask $checkUserCanUseDeviceTask,
        protected CheckPasswordPeriodTask $checkPasswordPeriodTask,
        AuthManager $authManager,
        Config $config,
        protected ?ValidateVBankAccountTask $validateVBankAccountTask
    ) {
        parent::__construct(
            checkUserCanUseDeviceTask: $checkUserCanUseDeviceTask,
            checkPasswordPeriodTask: $checkPasswordPeriodTask,
            authManager: $authManager,
            config: $config,
            validateVBankAccountTask: $validateVBankAccountTask
        );
    }

    /**
     * @param BiometricLogin $loginInfo
     */
    public function login(mixed $loginInfo, ?Client $client = null): LoginSession
    {
        $device = Device::active()->latest()->firstWhere([
            'device_id' => $loginInfo->deviceId,
            'device_name' => $loginInfo->deviceName,
        ]);

        $this->validateDevice($device, $loginInfo);

        $client = $device->client;

        $this->validate($client);
        $this->validateSignedChallenge($device, $loginInfo);

        $credentials = new LoginCredentials(
            nickname: $client->nickname_vbank ?? $client->nickname,
            password: $client->password
        );

        $tokenInfo = LoginForToken::make(
            $credentials,
            $loginInfo->deviceId,
            $client->client_id
        );

        $token = $this->authenticate($client, $tokenInfo);

        $homologationCode = $client->homologated
            ? HomologatedLoginCode::SUCCESSFUL_LOGIN
            : $this->getUserHomologationPathCode($client);

        $sessionData = new LoginSession(
            token: $token,
            clientId: $client->client_id,
            creditcardApplicationId: $client->creditcard_application_id,
            homologationCode: $homologationCode->value
        );

        $this->clearLoginAttempts($loginInfo->request);

        SuccessfulBiometricLoginEvent::dispatch($client, $device, $token);

        return $sessionData;
    }

    protected function validate(Client $client): void
    {
        if (!$client->homologated) {
            $this->validatePasswordExpirationPeriod($client);
        }

        $this->validateDisabledUser($client);
        $this->validateUserIsBlockByOTP($client);
        $this->validateUserIsBlockByFaceAuth($client);
        $this->validateUserHasDefaultSystem($client);
    }

    protected function validateDevice(?Device $device, BiometricLogin $loginInfo): void
    {
        $this->validateDeviceIsNotNull($device, $loginInfo);
        $this->validateLoginAttempts();
        $this->validateDeviceHasPublicKey($device, $loginInfo);
        $this->validateDeviceHasAChallenge($device, $loginInfo);
    }

    /**
     * Validates if the device is not null during biometric login.
     *
     * @throws DeviceNotFound
     */
    protected function validateDeviceIsNotNull(?Device $device, BiometricLogin $loginInfo): void
    {
        if (empty($device)) {
            DeviceNotFoundOnBiometricLoginEvent::dispatch(
                $loginInfo->request,
                $loginInfo->deviceId,
                $loginInfo->deviceName,
            );

            throw new DeviceNotFound();
        }
    }

    /**
     * Validates if the device has a public key during biometric login.
     *
     * @throws DeviceDoesNotHaveAPublicKey
     */
    protected function validateDeviceHasPublicKey(Device $device, BiometricLogin $loginInfo): void
    {
        if (empty($device->biometric_public_key)) {
            FailedToBiometricLoginDueToLackOfPublicKeyEvent::dispatch(
                $loginInfo->request,
                $loginInfo->deviceId,
                $loginInfo->deviceName,
                $loginInfo->deviceChallenge,
            );

            throw new DeviceDoesNotHaveAPublicKey();
        }
    }

    /**
     * Validates if the device has a challenge during biometric login.
     *
     * @throws DeviceDoesNotHaveChallenge
     */
    protected function validateDeviceHasAChallenge(Device $device, BiometricLogin $loginInfo): void
    {
        if (empty($device->verification_challenge)) {
            FailedToBiometricLoginDueToLackOfChallengeEvent::dispatch(
                $loginInfo->request,
                $loginInfo->deviceId,
                $loginInfo->deviceName,
                $loginInfo->deviceChallenge,
            );
            throw new DeviceDoesNotHaveChallenge();
        }
    }

    /**
     * Validates the signed challenge for the device during biometric login.
     *
     * @throws WrongBiometricCredentials
     */
    protected function validateSignedChallenge(Device $device, BiometricLogin $loginInfo): void
    {
        $isCorrectChallenge = $this
            ->checkSignedChallengeTask
            ->withDevice($device)
            ->withSignedChallenge($loginInfo->deviceChallenge)
            ->do();

        if (!$isCorrectChallenge) {
            WrongBiometricCredentialsEvent::dispatch(
                $loginInfo->request,
                $loginInfo->deviceId,
                $loginInfo->deviceName,
                $loginInfo->deviceChallenge,
            );
            throw new WrongBiometricCredentials();
        }
    }
}
