<?php

namespace App\Services;

use App\Models\Client;
use App\Traits\ActionLogger;
use App\Helpers\NotifySender;
use App\Factory\ClientFactory;
use App\Enums\Message\Generate;
use App\Enums\LogApplicationCode;
use Illuminate\Support\Facades\Hash;

class ClientPasswordService
{
    use ActionLogger;

    private Configurations $configurations;

    public function __construct(
        private readonly ClientFactory $clientFactory,
        private readonly ClientService $clientService,
    ) {
        $this->configurations = Configurations::getInstance();
    }
    /**
     * Update the user's password
     *
     * @param int $clientId
     * @param string $password
     * @return bool
     */
    public function updatePassword(Client $client, string $password): bool
    {
        $client->passwords()->create(['password' => $client->password]);

        $status = match ($client->status) {
            Client::DELETED => $client->status,
            default => Client::ENABLE
        };

        $client->password = bcrypt($password);
        $client->status = $status;
        $client->password_login_attempts = 0;

        $result = $client->save();

        if ($result) {
            $this->notifyClientOfPasswordUpdate($client);
            $this->addPasswordChangeHistory($client);
        }

        $this->logBank($client->client_id, LogApplicationCode::RESET_PASSWORD);

        return $result;
    }

    /**
     * Verify if the user's password hasn't been used earlier
     *
     * @param string $password
     * @param string $clientId
     * @return bool
     */
    public function verifyExistingPassword(Client $client, string $password): bool
    {
        $verifyOldPasswords = (int) $this->configurations->getConfigurations('VERIFICAR_PASSWORD_ANTERIORES');
        $limit = (int) $this->configurations->getConfigurations('MAXIMO_PASS_ANTERIORES_VERIFICAR');

        if ($verifyOldPasswords != 1) {
            return false;
        }

        $passwordHistories = $client->passwords()->latest()->take($limit)->get();

        foreach ($passwordHistories as $pass) {
            if (Hash::check($password, $pass->password)) {
                if (Hash::needsRehash($pass->password)) {
                    $pass->password = Hash::make($password);
                    $pass->update();
                }
                return true;
            }
        }
        return false;
    }

    public function verifyFirstPasswordUsed(string $newPassword, $clientPassword): bool
    {
        return Hash::check($newPassword, $clientPassword);
    }

    private function notifyClientOfPasswordUpdate(Client $client): void
    {
        NotifySender::init($this->clientFactory)
            ->client($client)
            ->template('APP_COCOMO')
            ->subjectByTemplateCode()
            ->chanelID(NotifySender::EMAIL)
            ->alternativeChanelsIDs('[3,1]')
            ->send();
    }

    private function addPasswordChangeHistory(Client $client): void
    {
        $this->clientService->addClientHistory(
            $client,
            'EMAIL',
            Generate::CHANGED_PASSWORD->value
        );
    }
}
