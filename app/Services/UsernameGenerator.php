<?php

namespace App\Services;

use App\Models\Client;
use Illuminate\Support\Str;

class UsernameGenerator
{
    /**
     * Generates a new unique username.
     *
     * It will try to generate a username like $firstPart + $secondPart + number.
     * If that's not possible, it will use a random string + number.
     *
     * @param string $firstPart
     * @param string $secondPart
     * @return string
     */
    public function generate(string $firstPart = '', string $secondPart = ''): string
    {
        // Concatenate both parts, if any. Or generate a random string.
        $letterPart = $firstPart . $secondPart;
        if (empty($letterPart)) {
            $letterPart = Str::random(8);
        }

        // Remove non a-z chars.
        $letterPart = Str::of($letterPart)
            ->ascii()
            ->lower()
            ->__toString();
        $letterPart = $this->filterAllowedChars($letterPart);

        $username = null;
        do {
            // Add a random number for uniqueness.
            $numberPart = (string) random_int(0, 9999);
            $username = $letterPart . $numberPart;

            // Check that it is not registered, otherwise try again.
        } while (is_null($username) || Client::usernameExists($username));

        return $username;
    }

    /**
     * Removes chars that are not allowed from $string.
     *
     * @param string $string
     * @return string
     */
    protected function filterAllowedChars(string $string): string
    {
        $allowedChars = range('a', 'z');

        return collect(str_split($string))
            ->filter(fn ($char) => in_array($char, $allowedChars))
            ->join('');
    }
}
