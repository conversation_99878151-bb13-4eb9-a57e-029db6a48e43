<?php

namespace App\Services;

/**
 * A "Documento único de identidad" (DUI) is a salvadorian number (and document)
 * used to legally identify a person. A DUI is a 10 chars long string composed of:
 *
 * 1. 8 digits.
 * 2. A dash (-).
 * 3. One digit.
 *
 * For example: 00016297-5. To compute one (and also check if it is valid):
 *
 * 1. Take an 8 digit number (pad with zeroes if needed): 00016297.
 * 2. Multiply each from left to right times 9 to 2 and add the results:
 * (9*0)+(8*0)+(7*0)+(6*1)+(5*6)+(4*2)+(3*9)+(2*7) = 85
 * 3. Take the difference of 10 and the modulo of the previous sum and 10:
 * 10 - (85 % 10).
 * 4. The result is attached to the first digits with a dash: 00016297-5.
 *
 * Any DUI with a 0 as last digit is also valid.
 */
class Dui
{
    /**
     * Every integer as string.
     */
    private const INTS_AS_STRINGS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    /**
     * List of blacklisted DUIs. They are computationally sound
     * but are invalid for other reasons.
     */
    private const BLACKLIST = [
        '00000000-0',
    ];

    /**
     * Generates a computationally valid DUI. It may not be attached to
     * a real person or legal reason.
     *
     * @return string
     */
    public function generate(): string
    {
        $baseNumber = random_int(0, 99_999_999);
        $fullDigits = sprintf("%'.08d", $baseNumber);
        $lastDigit = $this->getLastDigit($fullDigits);

        $dui = $fullDigits . '-' . $lastDigit;

        return $this->isBlacklisted($dui) ? $this->generate() : $dui;
    }

    /**
     * Whether $potentialDui is valid.
     *
     * @param mixed $potentialDui
     * @return bool
     */
    public function isValid($potentialDui): bool
    {
        if (
            !is_string($potentialDui) ||
            strlen($potentialDui) !== 10 ||
            $this->isBlacklisted($potentialDui)
        ) {
            return false;
        }

        $digitsAndLastDigit = explode('-', $potentialDui);
        if (count($digitsAndLastDigit) !== 2) {
            return false;
        }

        [$digits, $lastDigit] = $digitsAndLastDigit;
        if (
            strlen($digits) !== 8 ||
            strlen($lastDigit) !== 1 ||
            !in_array($lastDigit, self::INTS_AS_STRINGS)
        ) {
            return false;
        }

        foreach (str_split($digits) as $digit) {
            if (!in_array($digit, self::INTS_AS_STRINGS, true)) {
                return false;
            }
        }

        $validLastDigit = $this->getLastDigit($digits);

        return $validLastDigit === 0 || $validLastDigit === intval($lastDigit);
    }

    /**
     * The last digit of a DUI is the sum from i = 9 to 2 of i * d,
     * where d is one of the digits of $digits. If it's 0, we are done.
     * Otherwise compute 10 - (previousSum % 10).
     *
     * @param string $digits Must be 8 chars long.
     * @return string
     */
    private function getLastDigit(string $digits): int
    {
        $sum = 0;
        $factor = 9;
        foreach (str_split($digits) as $digit) {
            $sum += intval($digit) * $factor;
            $factor--;
        }

        $mod = $sum % 10;
        if ($mod === 0) {
            return 0;
        }

        return 10 - $mod;
    }

    /**
     * Whether $dui is in the list of blacklisted ones.
     *
     * @param string $dui
     * @return bool
     */
    private function isBlacklisted(string $dui): bool
    {
        return in_array($dui, self::BLACKLIST);
    }
}
