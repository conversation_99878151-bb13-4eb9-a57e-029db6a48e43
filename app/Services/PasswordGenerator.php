<?php

namespace App\Services;

use LogicException;
use Random\Randomizer;

class PasswordGenerator
{
    /**
     * Whitelist of characters for the password. It will contain
     * at least one character from every subarray. The rest will
     * be picked from the whole pool.
     *
     * Some chars are missing due to UX.
     *
     * @var array
     */
    public const ALLOWED_CHARS = [
        ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'],
        ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
        ['2', '3', '4', '5', '6', '7', '8', '9'],
        ['@', '/', '-', '$', '#'],
    ];

    public const MIN_LENGTH = 8;
    public const MAX_LENGTH = 10;

    public function __construct(private Randomizer $random)
    {
        if (self::MIN_LENGTH < count(self::ALLOWED_CHARS)) {
            throw new LogicException('The minimal length of the password should be at least equals to the amount of minimal distinct characters');
        }
    }

    /**
     * Generates a random string.
     *
     * @return string
     */
    public function generate(): string
    {
        // Length of the final password.
        $length = $this
            ->random
            ->getInt(self::MIN_LENGTH, self::MAX_LENGTH);

        $password = [];

        // Pick characters that should be in the password.
        // Picking is done iteratively due to $this->random distinct picking.
        foreach (self::ALLOWED_CHARS as $charSet) {
            $password[] = $this->pick($charSet);
        }

        // Fill the rest from the whole pool.
        $allChars = array_merge(...self::ALLOWED_CHARS);
        foreach (range(count($password) + 1, $length) as $_) {
            $password[] = $this->pick($allChars);
        }

        // Shuffle to avoid patterns and join into a string.
        return implode($this->random->shuffleArray($password));
    }

    /**
     * Picks one element from $array at random and returns it.
     *
     * @param array $array
     * @return mixed
     */
    protected function pick(array $array): mixed
    {
        $key = $this->random->pickArrayKeys($array, 1)[0];

        return $array[$key];
    }
}
