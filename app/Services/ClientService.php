<?php

namespace App\Services;

use Exception;
use App\Models\Client;
use App\Models\Device;
use App\Models\Systems;
use App\Models\NicknameLog;
use App\Helpers\NotifySender;
use App\Factory\ClientFactory;
use App\Enums\Message\Generate;
use App\Exceptions\ApiException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ClientService
{
    public function __construct(private readonly ClientFactory $clientFactory) {}

    /**
     * Set the stored customer's data in the bank database to the new $client object
     *
     * @param string $customerId
     * @return Client $client
     */
    public function clientInfoExternalDataBase(string $customerId, ClientFactory $clientFactory): Client
    {
        $customerData = $clientFactory->customeResponse(
            url: 'CustomerData',
            parameters: $customerId,
        )["content"];

        $customerContacts = $clientFactory->customeResponse(
            url: 'CustomerContacts',
            parameters: $customerId,
        )["content"];

        if (
            !isset($customerContacts["Data"]["ContactTypes"][0]["CustomerContacts"][0]["ContactValue"]) ||
            !isset($customerContacts["Data"]["ContactTypes"][1]["CustomerContacts"][0]["ContactValue"])
        ) {
            throw new ApiException("Usuario no encontrado", -1);
        }

        $customerContact            = $customerContacts['Data']['ContactTypes'];
        $client = new Client();
        $client->first_name         = $customerData['Data']['FirstName'];
        $client->second_name        = $customerData['Data']['SecondName'];
        $client->first_surname      = $customerData['Data']['FirstSurname'];
        $client->second_surname     = $customerData['Data']['SecondSurname'];
        $client->married_surname    = $customerData['Data']['MarriedName'];
        $client->dui                = $customerData['Data']['DUI'];
        $client->nit                = $customerData['Data']['NIT'];
        $client->birthdate          = $customerData['Data']['BirthDate'];
        $phoneIndex                = ($customerContact[0]['ContactTypeCode'] == 'CT_PHONE_CONFIRMED') ? 0 : 1;
        $client->phone_number       = $customerContact[$phoneIndex]['CustomerContacts'][0]['ContactValue'];
        return $client;
    }

    /**
     * Update client status to blocked by face authentication.
     */
    public function lockClientByFaceAuth(Client $client): void
    {
        $client->status = Client::BLOCK_BY_FACE_AUTH;
        $client->save();
    }

    /**
     * Save log for remember nickname
     *
     * @param Client $client
     * @return NicknameLog
     */
    public function saveNicknameLog(Client $client): NicknameLog
    {
        $nicknameLog = new NicknameLog();
        $nicknameLog->key = hash('sha256', time());
        $nicknameLog->client_id = $client->id;
        $nicknameLog->faceauthentication = Generate::PENDING_FACE_BASE->value;
        $nicknameLog->save();
        return $nicknameLog;
    }

    /**
     * @param string $id
     * @param string $faceauthentication
     * @return NicknameLog
     */
    public function updateNicknameLogFace(string $id, string $faceauthentication): NicknameLog
    {
        $nicknameLog =  NicknameLog::find($id);
        $nicknameLog->faceauthentication = $faceauthentication;
        $nicknameLog->save();
        return $nicknameLog;
    }

    /**
     * Add client to system to get perrmissions
     *
     * @param string $system_code
     * @param int $clientId
     * @return boolean
     */
    public function addClientToSystem(string $systemCode, int $clientId): bool
    {
        $system = Systems::where('system_code', $systemCode)->first();
        if (empty($system)) {
            return false;
        }
        $systemClient = DB::table('clients_systems')
            ->where('system_id', '=', $system->id)
            ->where('client_id', $clientId)
            ->first();
        if (!$systemClient) {
            DB::table('clients_systems')->insert([
                'client_id' => $clientId,
                'system_id' =>  $system->id
            ]);
            DB::commit();
        }
        return true;
    }

    public function addClientHistory(Client $client, string $type, string $message): void
    {
        try {
            $client->notificationHistories()->create(['type' => $type, 'message' => $message]);
        } catch (Exception $ex) {
            Log::error($ex);
        }
    }

    public function getClientFromRequest(string $identifier, string $value): ?Client
    {
        return match ($identifier) {
            'CLIENT_ID' => $this->clientBySystemId($value),
            'DUI' => $this->clientByDUI($value),
            default => null
        };
    }

    /**
     * Return client related to the system code ID
     *
     * @param string $clientSystemId
     * @return Client|null
     */
    public function clientBySystemId(string $clientSystemId): Client|null
    {
        return Client::where('client_id', $clientSystemId)->first();
    }

    /**
     * Return client that matches DUI.
     */
    public function clientByDUI(string $dui): ?Client
    {
        return Client::where('dui', $dui)->first();
    }

    /**
     * Remove the client from asigned system and denie permissions
     *
     * @param string $systemCode
     * @param int $clientId
     * @return mixed
     */
    public function removeClientFromSystem(string $systemCode, Client $client): mixed
    {
        $system = Systems::where('system_code', $systemCode)->first();

        if ($system) {
            return $client->systems()->detach($system);
        }

        return false;
    }

    /**
     * Save process to lock or unlock client
     */
    public function updateStatus(Client $client, int $status): void
    {
        $client->status = $status;
        $client->save();

        if ($status == Client::DISABLE) {
            $client->devices()->update(['online' => Device::OFFLINE]);
        }
    }

    /**
     * Send notification with nickname
     *
     * @param Client $client
     * @return void
     */
    public function sendNickname(Client $client): void
    {
        //send notyfy
        NotifySender::init($this->clientFactory)
            ->client($client)
            ->template('APP_NOTUSU')
            ->chanelID(NotifySender::EMAIL)
            ->subject('Reestablecimiento de usuario')
            ->alternativeChanelsIDs('[1,2]')
            ->addParameter('User', $client->nickname)
            ->send();

        $this->addClientHistory(
            client: $client,
            type: 'MAIL',
            message: 'Su usuario es: ' . $client->nickname
        );
    }

    public function resetPasswordAttempts(Client $client): void
    {
        $client->password_login_attempts = 0;
        $client->save();
    }

    public function getLatestLogin(Client $client): ?string
    {
        $latestLogin = $client->registerClientsApp()
            ->where('endpoint_name', 'LOGIN')
            ->where('status_response', Response::HTTP_OK)
            ->where('created_at', '<', now())
            ->latest()
            ->first();

        if (!$latestLogin) {
            return null;
        }

        $latestLogin = date('d/m/Y h:i a', strtotime($latestLogin->created_at));

        return 'Última sesión ' . str_replace(["am", "pm"], ["a.m.", "p.m."], $latestLogin);
    }
}
