<?php

namespace App\Services;

use App\Enums\Code\PasswordCode;
use App\Factory\ClientFactory;
use App\Models\Client;
use App\Rules\SuperIntendancyRules;
use App\Services\Crypt\Rsa;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PasswordValidationService
{
    private int $passMaxLengthConf;
    private int $passMinLengthConf;
    private bool $isSameLengthConf;
    private int $validationCode;
    private string $validationError = 'Algo salio mal, intenta más tarde.';
    private Configurations $configurations;
    private ClientFactory $clientFactory;

    public function __construct(
        private readonly Rsa $rsa,
        private readonly ClientService $clientService,
        private Client $client,
    ) {
        $this->configurations = Configurations::getInstance();
        $this->client = $client;
        $this->passMaxLengthConf = (int)  $this->configurations->getConfigurations('LONGITUD_MAXIMA_PASSWORD') ?: 8;
        $this->passMinLengthConf = (int)  $this->configurations->getConfigurations('LONGITUD_MINIMA_PASSWORD') ?: 8;
        $this->isSameLengthConf = (bool) ($this->passMinLengthConf === $this->passMaxLengthConf);
    }

    /**
     * Aplies the password rules verifications
     *
     * @param Request $request
     * @param int $clientID
     * @return boolean
     */
    public function validatePasswordRules(Request $request, string $customerId): bool
    {
        $clientExternal = $this->clientService->clientInfoExternalDataBase($customerId, $this->clientFactory);
        $passWord = $this->rsa->decrypt($request->password);
        $passWordConfirm = $this->rsa->decrypt($request->password_confirm ?: $request->password);
        $passFormatMessage = (string)  $this->configurations->getConfigurations('MENSAJE_PASS_INVALIDA');

        $data = [
            'password' => $passWord,
            'password_confirm' => $passWordConfirm,
        ];
        $rules = [
            'password_confirm' => [
                'required',
                'same:password'
            ],
            'password' => [
                "required",
                $this->minLengthRule(),
                $this->maxLengthRule(),
                $this->lengthPasswordVerification(),
                new SuperIntendancyRules($clientExternal),
            ],
        ];
        $messages = [
            'password.required' => 'La contraseña es requerida.',
            'password_confirm.required' => 'La confirmación de contraseña es requerida.',
            'password_confirm.same' => 'Las contraseñas no coinciden.',
            'password.regex' => $passFormatMessage
        ];
        $validator = Validator::make($data, $rules, $messages);
        if ($validator->fails()) {
            $failed = $validator->failed();
            $msg    = $validator->errors()->first();
            $this->setErrorCodesByRules($failed, $msg);
            return false;
        }
        return true;
    }

    /**
     * Get validation error
     *
     * @return string
     */
    public function validationError(): string
    {
        return $this->validationError;
    }

    /**
     * Get validation code
     *
     * @return integer
     */
    public function validationCode(): int
    {
        return $this->validationCode;
    }

    /**
     * Set client factory
     */
    public function setClientFactory(ClientFactory $clientF): self
    {
        $this->clientFactory = $clientF;

        return $this;
    }

    /**
     * Set the min length to the password by configuration.
     *
     * @return string
     */
    private function minLengthRule(): string
    {
        return !$this->isSameLengthConf ? "min:{$this->passMinLengthConf}" : "";
    }

    /**
     * Set the max length to the password by configuration.
     *
     * @return string
     */
    private function maxLengthRule(): string
    {
        return !$this->isSameLengthConf ? "max:{$this->passMaxLengthConf}" : "";
    }

    /**
     * Set a new rule if the password length configurations have the same value.
     * It evaluates if the password has and specific length
     *
     * @return callable
     */
    private function lengthPasswordVerification(): ?callable
    {
        $passLengthConf  = $this->passMaxLengthConf;
        if ($this->isSameLengthConf) {
            return function ($attribute, $value, $fail) use ($passLengthConf) {
                if (strlen($value) !== $passLengthConf) {
                    $fail('La contraseña debe tener una longitud de ' . $passLengthConf . 'caracteres');
                }
            };
        }

        return null;
    }

    /**
     * Validate and set the error code to adittional data for password reset.
     *
     * @param array $data
     * @param array $rules
     * @return boolean
     */
    public function validateAditionalData(array $data, array $rules): bool
    {
        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            $failed = $validator->failed();
            $msg    = $validator->errors()->first();
            $this->setErrorCodesByRules($failed, $msg);
            return false;
        }
        return true;
    }

    /**
     * Set the error code by attribute and rule filed.
     *
     * @param array $failed
     * @param string $message
     * @return void
     */
    private function setErrorCodesByRules(array $failed, string $message): void
    {
        $attrFailed = array_key_first($failed);
        $ruleFailed = strtolower(array_key_first($failed[$attrFailed]));
        $errorCodeByRule = $attrFailed . "." . $ruleFailed;
        $this->validationError  = $message;
        $this->validationCode = in_array($errorCodeByRule, $this->errorAttrCode())
            ? $this->errorAttrCode()[$errorCodeByRule]
            : PasswordCode::PASS_ERROR->value;
    }

    /**
     * Stablish the error code by attribute and rule failed.
     *
     */
    private function errorAttrCode(): array
    {
        return  [
            'dui.required'          => PasswordCode::DUI_ERROR->value,
            'password_confirm.same' => PasswordCode::PASSCONF_ERROR->value,
            'password.min'          => PasswordCode::PASS_MIN_ERROR->value,
            'password.max'          => PasswordCode::PASS_MAX_ERROR->value,
            'password.regex'        => PasswordCode::PASS_ERROR->value,
            'password.app\\rules\\superintendancyrules' => PasswordCode::PASS_ERROR->value,
        ];
    }
}
