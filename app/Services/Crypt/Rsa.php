<?php

namespace App\Services\Crypt;

use App\Models\Config;
use <PERSON>tie\Crypto\Rsa\PrivateKey;
use <PERSON>tie\Crypto\Rsa\PublicKey;

class Rsa
{
    /**
     * The parsed public key.
     */
    private ?PublicKey $publicKey;

    /**
     * The parsed private key.
     */
    private ?PrivateKey $privateKey;

    public function __construct()
    {
        $this->publicKey = null;
        $this->privateKey = null;
    }

    /**
     * Signs $data with the private $key.
     *
     * @param string $data
     * @param string $key
     * @return string Signed data as base 64.
     */
    public static function signWith(string $data, string $key): string
    {
        $privateKey = PrivateKey::fromString($key);

        return $privateKey->sign($data);
    }

    /**
     * Encrypts $data using $this->publicKey and returns the result as base 64.
     *
     * @param string $data
     * @return string As base 64 encoded.
     */
    public function encrypt(string $data): string
    {
        $this->initKeys();

        return base64_encode($this->publicKey->encrypt($data));
    }

    /**
     * Encrypts $data using the provided public key and returns the result as base 64.
     *
     * @param string $data
     * @param string $key
     * @return string As base 64 encoded.
     */
    public function encryptWith(string $data, string $key): string
    {
        $publicKey = PublicKey::fromString($this->trimByLines($key));

        return base64_encode($publicKey->encrypt($data));
    }

    /**
     * Decodes $data from base 64 and decrypts it using $this->privateKey.
     *
     * @param string $data Base 64 encoded.
     * @return string
     */
    public function decrypt(string $data): string
    {
        $this->initKeys();

        return $this->privateKey->decrypt(base64_decode($data));
    }

    /**
     * Decodes $data from base 64 and decrypts it using the provided private key.
     *
     * @param string $data Base 64 encoded.
     * @param string $key
     * @return string
     */
    public function decryptWith(string $data, string $key): string
    {
        $privateKey = PrivateKey::fromString($this->trimByLines($key));

        return $privateKey->decrypt(base64_decode($data));
    }

    /**
     * Whether $data is a valid base 64 encoded and encrypted string.
     *
     * @param mixed $data
     * @return bool
     */
    public function canDecrypt($data): bool
    {
        $this->initKeys();

        if (is_null($data)) {
            return false;
        }
        if (!is_string($data)) {
            return false;
        }

        return $this->privateKey->canDecrypt(base64_decode($data));
    }

    /**
     * Verifies that $signature was signed with the private counterpart of
     * the public $key and that it is $data.
     *
     * @param string $data
     * @param string $signature As base 64.
     * @param string $key
     * @return bool
     */
    public function verifyWith(string $data, string $signature, string $key): bool
    {
        $publicKey = PublicKey::fromString($this->trimByLines($key));

        return $publicKey->verify($data, $signature);
    }

    /**
     * Whether the key pair is ready to be used.
     *
     * @return bool
     */
    private function areKeysSet(): bool
    {
        return !is_null($this->publicKey) && !is_null($this->privateKey);
    }

    /**
     * Inits the key pair from DB.
     *
     * @return void
     */
    private function initKeys(): void
    {
        if ($this->areKeysSet()) {
            return;
        }

        $publicKey = $this->trimByLines(Config::findOrFail('PASSWORD_PUBLIC_KEY')->value);
        $this->publicKey = PublicKey::fromString($publicKey);

        $privateKey = $this->trimByLines(Config::findOrFail('PASSWORD_PRIVATE_KEY')->value);
        $this->privateKey = PrivateKey::fromString($privateKey);
    }

    /**
     * Removes spaces from the start and end of each line in $string.
     *
     * @param string $string
     * @return string
     */
    private function trimByLines(string $string): string
    {
        $lines = [];
        foreach (explode("\n", $string) as $line) {
            $lines[] = trim($line);
        }

        return implode("\n", $lines);
    }
}
