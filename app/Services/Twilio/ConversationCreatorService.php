<?php

namespace App\Services\Twilio;

use App\Dtos\TwilioAccessToken;
use App\Events\ConversationCreatedEvent;
use App\Tasks\Twilio\AddParticipantTask;
use App\Tasks\Twilio\CreateConversationTask;
use App\Tasks\Twilio\CreateTokenWithChatGrantTask;
use Twilio\Rest\Client as TwilioClient;

class ConversationCreatorService
{
    public function __construct(
        protected TwilioClient $twilioClient,
        protected CreateConversationTask $createConversationTask,
        protected AddParticipantTask $addParticipantTask,
        protected CreateTokenWithChatGrantTask $createTokenWithChatGrantTask,
    ) {
    }

    /**
     * Creates a Twilio conversation, adds a participant and
     * an access token for such.
     *
     * @param string $identity
     * @return TwilioAccessToken
     */
    public function createConversationWithWebParticipant(string $identity): TwilioAccessToken
    {
        $conversation = $this
            ->createConversationTask
            ->withTwilioClient($this->twilioClient)
            ->do();

        $this
            ->addParticipantTask
            ->withTwilioClient($this->twilioClient)
            ->withConversation($conversation)
            ->withIdentity($identity)
            ->do();

        $accessToken = $this
            ->createTokenWithChatGrantTask
            ->withConversation($conversation)
            ->withIdentity($identity)
            ->do();

        ConversationCreatedEvent::dispatch($conversation, $identity);

        return TwilioAccessToken::make($accessToken->toJWT(), $conversation->sid);
    }
}
