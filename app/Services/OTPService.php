<?php

namespace App\Services;

use App\Enums\Code\Data;
use App\Enums\Message\OTPMessage;
use App\Enums\OTP\Generate;
use App\Enums\OTP\Validate;
use App\Helpers\Utils;
use App\Models\Client;
use App\Models\OTPBlackList;
use App\Models\OtpLogs;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Str;

class OTPService
{
    //chanles names
    private const WSP_CH    = 'WSP';
    private const SMS_CH    = 'SMS';
    private const EMAIL_CH  = 'EMAIL';

    //cahenels IDs
    public const WSP    = 1;
    public const SMS    = 2;
    public const EMAIL  = 3;

    /**
     * Time to block the code generation by max codes generated
     *
     * @var int
     */
    private int $blockTime;

    /**
     * Max OTP code to generate for one emitter user|email|phone number
     *
     * @var int
     */
    private int $maxCodesGenerated;

    /**
     * Max OTP validation attempts
     *
     * @var int
     */
    private int $maxAttempts;

    /**
     * Time for each code generation in seconds
     *
     * @var int
     */
    private int $secondsTimePeriod;

    /**
     * OTP code life time in seconds
     *
     * @var int
     */
    private int $secondsOTPLifeTime;

    /**
     * Block time by validations attempts
     *
     * @var int
     */
    private int $blockTimeValidation;

    /**
     * Result response OTP
     *
     * @var string
     */
    private string $responseResult;

    /**
     * Result response code
     *
     * @var integer
     */
    private int $responseCode;

    /**
     * Result status code
     *
     * @var integer
     */
    private int $responseStatus;

    /**
     * configuration services
     *
     * @var Configurations
     */
    private Configurations $configurations;


    public function __construct()
    {
        $this->configurations = Configurations::getInstance();
        $this->blockTime           = (int) $this->configurations->getConfigurations('OTP_TIEMPO_BLOQUEO') ?: 5;
        $this->maxAttempts         = (int) $this->configurations->getConfigurations('OTP_INTENTOS_VALIDACION') ?: 3;
        $this->maxCodesGenerated   = (int) $this->configurations->getConfigurations('OTP_MAX_CODIGOS_GENERADOS') ?: 3;
        $this->secondsTimePeriod   = (int) $this->configurations->getConfigurations('OTP_TIEMPO_GENERACION') ?: 60;
        $this->secondsOTPLifeTime  = (int) $this->configurations->getConfigurations('OTP_TIEMPO_VIDA') ?: 60;
        $this->blockTimeValidation = (int) $this->configurations->getConfigurations('OTP_TIEMPO_BLOQUEO_VALIDACION') ?: 5;
    }

    /**
     * Get chanel value from given string and get the int chanel value
     *
     * @param string $requestChanel
     * @return integer
     */
    public function getChanel(string $requestChanel): int
    {
        return match ($requestChanel) {
            self::WSP_CH => self::WSP,
            self::SMS_CH => self::SMS,
            self::EMAIL_CH => self::EMAIL,
            default => self::EMAIL
        };
    }

    /**
     * Verify if OTP generation has failed by emitter validations
     *
     * @param string $authorization
     * @param string $emitter
     * @param string $userID
     * @return boolean
     */
    public function generationHasFailedByEmitter(string $authorization, string $emitter, string $userID): bool
    {
        if (($authorization === OtpLogs::APIKEY && $this->emitterInBlackList($emitter)) ||
            ($authorization === OtpLogs::SESSION && $this->userIsBlockByOTPValidation((int) $userID))
        ) {
            $this->responseStatus   = Response::HTTP_BAD_REQUEST;
            $this->responseResult   = OTPMessage::NOT_GENERATE_CODES->value;
            $this->responseCode     = Generate::BLOCK_BY_ATTEMPTS->value;
            return true;
        }

        return false;
    }

    /**
     * Verify if OTP generation has failed by emitter time validations
     *
     * @param string $emitter
     * @return boolean
     */
    public function generationHasFailedByTime(string $emitter): bool
    {
        if ($this->isNotAbleByMaxAttempts($emitter)) {
            $this->responseStatus = Response::HTTP_BAD_REQUEST;
            $this->responseResult = (int) $this->configurations->getConfigurations('OTP_MENSAJE_MAX_CODIGOS')
                ?: OTPMessage::MAX_GENERATES_CODE->value;
            $this->responseCode     = Generate::BLOCK_TIME->value;
            return true;
        }

        return false;
    }

    /**
     * Result getter
     *
     * @return string
     */
    public function getResult(): string
    {
        return $this->responseResult;
    }

    /**
     * Status getter
     *
     * @return integer
     */
    public function getStatus(): int
    {
        return $this->responseStatus;
    }

    /**
     * Code getter
     *
     * @return integer
     */
    public function getCode(): int
    {
        return $this->responseCode;
    }

    /**
     * verify if the user has exceeded the otp validation attempts
     *
     * @param integer $attempt
     * @return boolean
     */
    public function exceededAttempts(int $attempt): bool
    {
        return $attempt >= $this->maxAttempts;
    }

    /**
     * To many OTP codes generated by one emitter email|phonenumber
     *
     * @param string $emitter
     * @return boolean
     */
    public function isNotAbleByMaxAttempts(string $emitter): bool
    {
        $now = Carbon::now()->tz(Data::TZ_DATE->value);
        $backTime = $now->copy()->addMinutes(-$this->blockTime);
        $otpRequest = OtpLogs::orderBy('created_at', 'DESC')
            ->where(['value' => $emitter])
            ->where('verified', OtpLogs::NOT_VERIFIED)
            ->where('created_at', '<=', $now->format(Data::DATE_HOURS_MINUTE_FORMAT->value))
            ->where('created_at', '>=', $backTime->format(Data::DATE_HOURS_MINUTE_FORMAT->value))
            ->get();

        if ($otpRequest->count() === 0) {
            return false;
        }
        $lastOTPRequest = $otpRequest->first();
        $diffTime = Utils::datesDifference(
            $lastOTPRequest->created_at,
            $now->format(Data::DATE_HOURS_MINUTE_FORMAT->value)
        );
        return ($otpRequest->count() >= $this->maxCodesGenerated && $diffTime->minutes < $this->blockTime);
    }

    /**
     * Check OTP life time with log register
     *
     * @param string $createdAt
     * @return boolean
     */
    public function otpStillValid(string $createdAt): bool
    {
        $now = Carbon::now()->tz(Data::TZ_DATE->value);
        $diffTime = utils::datesDifference(
            $createdAt,
            $now->format(Data::DATE_HOURS_FORMAT->value)
        );
        return $diffTime->seconds <= $this->secondsOTPLifeTime;
    }

    /**
     * Verify if the emitter is in the black list OTP to disable generation code
     *
     * @param string $emitter
     * @return boolean
     */
    public function emitterInBlackList(string $emitter): bool
    {
        $now = Carbon::now()->tz(Data::TZ_DATE->value);
        $blackList = OTPBlackList::where('emitter', $emitter)->first();
        if (empty($blackList)) {
            return false;
        }

        $minutesToUnblock = $now->diffInMinutes(Carbon::parse($blackList->block_time_end), false);
        return $minutesToUnblock > 0;
    }

    /**
     * Verify if the user is block by OTP code validation and needs to reset the password
     *
     * @param integer $id
     * @return boolean
     */
    public function userIsBlockByOTPValidation(int $id): bool
    {
        $user = Client::find($id);
        return isset($user) && (int) $user->status === Client::BLOCKOTP;
    }

    /**
     * Get code response by authorization type
     *
     * @param string $authorization
     * @return int
     */
    public function codeByAuthorization(string $authorization): int
    {
        return $authorization === OtpLogs::SESSION
            ? Validate::MAX_ATTEMPTS_OTP_SESS->value
            : Validate::MAX_ATTEMPTS_OTP_NO_SESS->value;
    }

    /**
     * Get block message by given authorization system as string
     *
     * @param string $authorization
     * @return string
     */
    public function getBlockMessage(string $authorization): string
    {
        $isSession = $authorization === OtpLogs::SESSION;
        $messageConf = $this->getBlockMessageConf($isSession);
        return $isSession
            ? $messageConf
            : str_replace('MINS', $this->blockTimeValidation, $messageConf);
    }

    /**
     * Parse message configuration by authorization type
     *
     * @param boolean $isSession
     * @return string
     */
    private function getBlockMessageConf(bool $isSession): string
    {
        if ($isSession) {
            return (string) $this->configurations->getConfigurations('OTP_MENSAJE_BLOQUEO_SESION')
                ?: OTPMessage::MAX_ATTEMPTS->value;
        }

        return (string) $this->configurations->getConfigurations('OTP_MENSAJE_BLOQUEO_APIKEY')
            ?: Str::of(OTPMessage::SUCCESSED_ATTEMPTS->value)->swap([":valor" => $this->blockTimeValidation]);
    }

    /**
     * Block emitters such as the user or phone number or email to prevent arbitrary OTP generation.
     *
     * @param string $authorization
     * @param string $emitter
     * @param integer $userID
     * @return void
     */
    public function blockEmitter(string $authorization, string $emitter, int $userID): void
    {
        if ($authorization === OtpLogs::SESSION) {
            return;
        }

        $start  = Carbon::now()->tz(Data::TZ_DATE->value);
        $end    = $start->copy()->addMinutes($this->blockTimeValidation);

        $blackList = OTPBlackList::where('emitter', $emitter)->first() ?? new OTPBlackList();
        $blackList->emitter = $emitter;
        $blackList->block_time_start = $start->format(Data::DATE_HOURS_FORMAT->value);
        $blackList->block_time_end = $end->format(Data::DATE_HOURS_FORMAT->value);
        $blackList->save();
    }
}
