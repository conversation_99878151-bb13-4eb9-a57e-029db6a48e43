<?php

namespace App\Repos;

use App\Traits\IncludesAuthHeaders;
use Illuminate\Http\Client\Response;

class CreateCustomerAddressesRepo extends ApiRepo
{
    use IncludesAuthHeaders;

    protected string $path = 'CustomerAddresses';

    public function prepare($values): self
    {
        $this->body = $values;

        return $this;
    }

    protected function call(): Response
    {
        return $this
            ->http
            ->post($this->path, $this->body);
    }
}
