<?php

namespace App\Repos;

use App\Dtos\Customer;
use App\Traits\IncludesAuthHeaders;
use Illuminate\Http\Client\Response;

class GetCustomerRepo extends ApiRepo
{
    use IncludesAuthHeaders;

    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'CustomerData';

    /**
     * Prepares the call with $customerId.
     *
     * @param string $customerId
     * @return self
     */
    public function prepare($customerId): self
    {
        $this->queryParams = [
            'CustomerId' => $customerId,
        ];

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->get($this->path, $this->queryParams);
    }

    /**
     * Transforms the response array.
     *
     * @param array $json
     * @return mixed
     */
    protected function into(array $json)
    {
        $data = $json['Data'];

        return Customer::make(
            $data['CustomerId'],
            $data['FirstName'],
            $data['SecondName'],
            $data['FirstSurname'],
            $data['SecondSurname'],
            $data['MarriedName'],
            $data['BirthDate'],
            $data['DUI'],
            $data['NIT'],
        );
    }
}
