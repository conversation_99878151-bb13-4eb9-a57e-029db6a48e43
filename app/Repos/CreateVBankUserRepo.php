<?php

declare(strict_types=1);

namespace App\Repos;

use Illuminate\Http\Client\Response;

class CreateVBankUserRepo extends ApiRepo
{
    protected string $path = 'CreateVBankUser';

    protected bool $shouldDowngradeApiVersion = true;

    /**
     * @param array{customerId: string, deviceId: string} $values
     */
    public function prepare($values): self
    {
        ['customerId' => $customerId, 'deviceId' => $deviceId] = $values;

        $this->addHeaders([
            'UserAuth' => $customerId,
            'DeviceAuth' => $deviceId,
        ], shouldFilter: false);

        $this->body = ['CustomerId' => $customerId];

        return $this;
    }

    public function call(): Response
    {
        return $this->http->post($this->path, $this->body);
    }
}
