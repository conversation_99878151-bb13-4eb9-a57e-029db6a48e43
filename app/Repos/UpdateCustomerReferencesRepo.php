<?php

namespace App\Repos;

use Illuminate\Http\Client\Response;

class UpdateCustomerReferencesRepo extends ApiRepo
{
    protected string $path = 'CustomerReferences';

    public function prepare($values): self
    {
        $this->body = $values;

        return $this;
    }

    protected function call(): Response
    {
        return $this
            ->http
            ->put($this->path, $this->body);
    }
}
