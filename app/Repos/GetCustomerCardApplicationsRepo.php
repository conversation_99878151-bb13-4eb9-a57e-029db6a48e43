<?php

namespace App\Repos;

use App\Dtos\CreditCardApplication;
use App\Dtos\Customer;
use App\Traits\IncludesBaseAuthHeaders;
use App\Traits\IncludesBiometryHeaders;
use Illuminate\Http\Client\Response;

/**
 * Returns credit card application and customer info.
 */
class GetCustomerCardApplicationsRepo extends ApiRepo
{
    use IncludesBiometryHeaders;
    use IncludesBaseAuthHeaders;

    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'CCApplications';

    /**
     * Prepares the call.
     *
     * @param string $dui
     * @return self
     */
    public function prepare($dui): self
    {
        $this->queryParams = [
            'queryString' => $dui,
            'queryType' => 'DUI',
        ];

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->get($this->path, $this->queryParams);
    }

    /**
     * Transforms the response array.
     *
     * @param array $json
     * @return CreditCardApplication
     */
    protected function into(array $json)
    {
        $customerData = $json['Data']['CustomerData'];

        return CreditCardApplication::make(Customer::make(
            $customerData['CustomerId'],
            $customerData['FirstName'],
            $customerData['SecondName'],
            $customerData['FirstSurname'],
            $customerData['SecondSurname'],
            null,
            null,
            $customerData['DUI'],
            $customerData['NIT'],
        ));
    }
}
