<?php

namespace App\Repos;

use App\Dtos\ExternalAuthBiometry;
use App\Dtos\ExternalAuthUser;
use Illuminate\Http\Client\Response;

class ValidateTokenRepo extends ApiRepo
{
    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'ValidateTokenAuth';

    /**
     * Whether the external call should include the auth token
     * that came in the original request.
     *
     * @var bool
     */
    protected bool $includeSourceRequestToken = false;

    /**
     * Prepares the call with $values.
     *
     * @param ExternalAuthBiometry|ExternalAuthUser $values
     * @return self
     */
    public function prepare($values): self
    {
        if ($values::class === ExternalAuthBiometry::class) {
            $this->body = $values->toCustomArray([
                'deviceId' => 'DeviceAuth',
                'ip' => 'IpAddressAuth',
                'token' => 'TokenAuthorization',
                'dui' => 'DUI',
                'biometry' => 'BiometryId',
            ]);
        }

        if ($values::class === ExternalAuthUser::class) {
            $this->body = $values->toCustomArray([
                'deviceId' => 'DeviceAuth',
                'ip' => 'IpAddressAuth',
                'token' => 'TokenAuthorization',
                'userId' => 'UserAuth',
                'username' => 'UserName',
            ]);
        }

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->post($this->path, $this->body);
    }

    /**
     * Transforms the response array.
     *
     * @param array $json
     * @return bool
     */
    protected function into(array $json)
    {
        return $json['Data'] === 'OK';
    }
}
