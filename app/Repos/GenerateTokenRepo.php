<?php

namespace App\Repos;

use Exception;
use App\Dtos\ExternalToken;
use App\Dtos\GenerateExternalToken;
use Illuminate\Http\Client\Response;

/**
 * Gets a new token and refresh token for users with
 * an account.
 */
class GenerateTokenRepo extends ApiRepo
{
    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'TokenAuthorization';

    /**
     * Whether the external call should include the auth token
     * that came in the original request.
     *
     * @var bool
     */
    protected bool $includeSourceRequestToken = false;

    /**
     * Prepares the call with $values.
     *
     * @param GenerateExternalToken $loginData
     * @return self
     */
    public function prepare($loginData): self
    {
        $this->body = [
            'DeviceAuth' => $loginData->deviceId,
            'IpAddressAuth' => $this->sourceIp,
            'UserAuth' => $loginData->customerId,
            'UserName' => $loginData->nickname,
        ];

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->post($this->path, $this->body);
    }

    /**
     * Transforms the response array.
     *
     * @param array $json
     * @return ExternalToken
     */
    protected function into(array $json): ?ExternalToken
    {
        try {
            return ExternalToken::make(
                $json['Data']['TokenAuthorization'],
                $json['Data']['RefreshTokenAuthorization'],
            );
        } catch (Exception $e) {
            return null;
        }
    }
}
