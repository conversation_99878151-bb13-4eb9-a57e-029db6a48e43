<?php

namespace App\Repos;

use App\Dtos\ExternalToken;
use App\Dtos\RefreshToken;
use App\Exceptions\Auth\InvalidRefreshToken;
use App\Exceptions\Auth\UnknownErrorRefreshToken;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Arr;

class RefreshTokenRepo extends ApiRepo
{
    public const INVALID_TOKEN_STATUS = 2102;

    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'TokenAuthorization';

    /**
     * Whether the external call should include the auth token
     * that came in the original request.
     *
     * @var bool
     */
    protected bool $includeSourceRequestToken = false;

    /**
     * Prepares the call with $values.
     *
     * @param array|RefreshToken $values
     * @return self
     */
    public function prepare($values): self
    {
        // If dealing with an array.
        if (is_array($values)) {
            $this->body = $values;
            $this->body['IpAddressAuth'] = $this->sourceIp;

            return $this;
        }

        // If dealing with a RefreshToken DTO.
        $this->addHeaders([
            'UserAuth' => $values->generateTokenData->customerId,
            'DeviceAuth' => $values->generateTokenData->deviceId,
        ]);

        $this->body = [
            'DeviceAuth' => $values->generateTokenData->deviceId,
            'IpAddressAuth' => $this->sourceIp,
            'UserAuth' => $values->generateTokenData->customerId,
            'UserName' => $values->generateTokenData->nickname,
            'RefreshTokenAuthorization' => $values->refreshToken,
        ];

        return $this;
    }

    public function fetch()
    {
        $this->fetchRaw();

        $response = $this->response->throw()->json() ?? [];
        $responseStatus = Arr::get($response, 'Status.ResponseStatus.Code');

        if ($responseStatus === self::INVALID_TOKEN_STATUS) {
            throw new InvalidRefreshToken();
        }
        if ($responseStatus !== 0) {
            throw new UnknownErrorRefreshToken();
        }

        return $this->into($response);
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->put($this->path, $this->body);
    }

    protected function into(array $json)
    {
        $token = Arr::get($json, 'Data.TokenAuthorization');
        $refreshToken = Arr::get($json, 'Data.RefreshTokenAuthorization');

        if (empty($token) || empty($refreshToken)) {
            throw new UnknownErrorRefreshToken();
        }

        return ExternalToken::make($token, $refreshToken);
    }
}
