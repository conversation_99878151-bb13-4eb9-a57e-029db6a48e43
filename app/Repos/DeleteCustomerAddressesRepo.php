<?php

namespace App\Repos;

use App\Traits\IncludesAuthHeaders;
use Illuminate\Http\Client\Response;

class DeleteCustomerAddressesRepo extends ApiRepo
{
    use IncludesAuthHeaders;

    protected string $path = 'CustomerAddresses';

    public function prepare($values): self
    {
        $this->queryParams = $values;

        return $this;
    }

    protected function call(): Response
    {
        return $this
            ->http
            ->delete($this->path, $this->queryParams);
    }
}
