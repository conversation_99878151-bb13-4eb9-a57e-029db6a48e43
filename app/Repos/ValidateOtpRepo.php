<?php

namespace App\Repos;

use App\Dtos\OtpValidation;
use Illuminate\Http\Client\Response;

class ValidateOtpRepo extends ApiRepo
{
    public const SYSTEM_CODE = 'SYS_APP01';
    public const VALID_OTP_CODE = 0;

    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'OTPCodes';

    /**
     * Prepares the call with $values.
     *
     * @param OtpValidation $values
     * @return self
     */
    public function prepare($values): self
    {
        $this->queryParams = $values->toCustomArrayWith(
            [
                'sharedKey' => 'SharedKey',
                'otp' => 'OTPCode',
            ],
            ['SystemCode' => self::SYSTEM_CODE],
        );

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->get($this->path, $this->queryParams);
    }

    public function isValidOtp(OtpValidation $data): bool
    {
        return $this
            ->prepare($data)
            ->fetch()['Status']['ResponseStatus']['Code'] === self::VALID_OTP_CODE;
    }
}
