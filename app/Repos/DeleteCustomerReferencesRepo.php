<?php

namespace App\Repos;

use Illuminate\Http\Client\Response;

class DeleteCustomerReferencesRepo extends ApiRepo
{
    protected string $path = 'CustomerReferences';

    public function prepare($values): self
    {
        $this->queryParams = $values;

        return $this;
    }

    protected function call(): Response
    {
        return $this
            ->http
            ->delete($this->path, $this->queryParams);
    }
}
