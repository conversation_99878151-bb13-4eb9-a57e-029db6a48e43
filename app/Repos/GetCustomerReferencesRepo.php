<?php

namespace App\Repos;

use Illuminate\Http\Client\Response;

class GetCustomerReferencesRepo extends ApiRepo
{
    protected string $path = 'CustomerReferences';

    public function prepare($values): self
    {
        $this->queryParams = $values;

        return $this;
    }

    protected function call(): Response
    {
        return $this
            ->http
            ->get($this->path, $this->queryParams);
    }
}
