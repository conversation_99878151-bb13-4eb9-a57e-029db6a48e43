<?php

namespace App\Repos;

use App\Traits\FetchesDirectly;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Str;

class DummyGenerateTokenRepo extends GenerateTokenRepo
{
    use FetchesDirectly;

    /**
     * Calls the source for the info and returns response array.
     *
     * @return array
     */
    public function fetchRaw(): array
    {
        return [
            'Data' => [
                'TokenAuthorization' => Str::random(100),
                'RefreshTokenAuthorization' => Str::random(100),
            ]
        ];
    }
}
