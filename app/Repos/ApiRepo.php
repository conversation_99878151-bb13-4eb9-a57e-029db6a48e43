<?php

namespace App\Repos;

use App\Contracts\ApiRepo as ApiRepoContract;
use App\Dtos\ExternalAuth;
use App\Dtos\ExternalAuthBiometry;
use App\Dtos\ExternalAuthUser;
use App\Events\ExternalCallMadeEvent;
use App\Events\ExternalCallToBeMadeEvent;
use Illuminate\Http\Client\Factory as Http;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Http\Request;

/**
 * Base class for HTTP calls to external services.
 *
 * One repo, one call.
 */
abstract class ApiRepo implements ApiRepoContract
{
    public const HEADER_WHITE_LIST = [
        'deviceauth',
        'duiauth',
        'biometryauth',
        'userauth',
    ];

    /**
     * Base URL of the request.
     */
    protected string $baseUrl = '';

    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = '';

    /**
     * Body of the request, if any.
     *
     * May be prepared.
     */
    protected array $body = [];

    /**
     * Query params of the request, if any.
     *
     * May be prepared.
     */
    protected array $queryParams = [];

    /**
     * API key for external services.
     */
    protected string $apiKey = '';

    /**
     * Auth token for external services, if any.
     */
    protected string $token = '';

    /**
     * Flag for switching from `/v1` to `/v0`
     */
    protected bool $shouldDowngradeApiVersion = false;

    /**
     * Headers of the request, if any.
     *
     * May be prepared.
     */
    protected array $headers = [];

    /**
     * Once the call has been made, the result is stored here.
     */
    protected Response $response;

    /**
     * Identifier for this call.
     */
    protected int $id;

    /**
     * HTTP call builder.
     */
    protected PendingRequest $http;

    /**
     * IP of the source of the request.
     */
    protected string $sourceIp = '';

    /**
     * Header value that came with original request.
     */
    protected string $deviceHeader = '';

    /**
     * Header value that came with original request.
     */
    protected string $userHeader = '';

    /**
     * Header value that came with original request.
     */
    protected string $duiHeader = '';

    /**
     * Header value that came with original request.
     */
    protected string $biometryHeader = '';

    /**
     * Whether the external call should include the auth token
     * that came in the original request.
     *
     * @var bool
     */
    protected bool $includeSourceRequestToken = true;

    public function __construct(
        Http $httpFactory,
        Request $request,
    ) {
        $this->id = random_int(0, PHP_INT_MAX);
        $this->apiKey = config('services.baes.key');
        $this->sourceIp = $request->ip();

        $this->baseUrl = config('services.baes.base_url');
        if ($this->shouldDowngradeApiVersion) {
            $this->baseUrl = downgradeApiVersionInUrl($this->baseUrl);
        }

        // Header info.
        $this->token = $request->bearerToken() ?? '';
        $this->deviceHeader = $request->header(ExternalAuth::DEVICE_HEADER, '');
        $this->userHeader = $request->header(ExternalAuthUser::USER_AUTH_HEADER, '');
        $this->duiHeader = $request->header(ExternalAuthBiometry::DUI_HEADER, '');
        $this->biometryHeader = $request->header(ExternalAuthBiometry::BIOMETRY_HEADER, '');

        $this->http = $httpFactory->baseUrl($this->baseUrl);
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    abstract protected function call(): Response;

    /**
     * Calls the source for the info and may return data.
     *
     * Throws in case of non-successful status.
     *
     * @return mixed
     */
    public function fetch()
    {
        $this->fetchRaw();

        return $this->into($this->response->throw()->json() ?? []);
    }

    /**
     * Calls the source for the info and returns response array.
     *
     * @return array
     */
    public function fetchRaw(): array
    {
        $this->readyCall();

        $this->beforeCall();
        $this->response = $this->call();
        $this->afterCall();

        return $this->response->json() ?? [];
    }

    /**
     * Method called just before making the HTTP request.
     *
     * @return void
     */
    protected function beforeCall(): void
    {
        ExternalCallToBeMadeEvent::dispatch(
            $this->baseUrl,
            $this->path,
            get_class($this),
            $this->id,
        );
    }

    /**
     * Method called just after making the HTTP request.
     *
     * @return void
     */
    protected function afterCall(): void
    {
        ExternalCallMadeEvent::dispatch(
            $this->baseUrl,
            $this->path,
            get_class($this),
            $this->getStatus(),
            $this->headers,
            $this->body,
            $this->queryParams,
            $this->response->headers(),
            $this->response->json() ?? [],
        );
    }

    /**
     * Transforms the response array.
     *
     * @param array $json
     * @return mixed
     */
    protected function into(array $json)
    {
        return $json;
    }

    /**
     * Set ups the HTTP client with base info.
     *
     * @return void
     */
    protected function readyCall(): void
    {
        // Minimal required Headers.
        $this->addHeaders([
            'APIKey' => $this->apiKey,
            'IpAddressAuth' => $this->sourceIp,
        ], shouldFilter: false);

        // Customizable headers.
        if ($this->includeSourceRequestToken && $this->token) {
            $this->addHeaders(['TokenAuth' => $this->token], false);
        }
        if (property_exists($this, 'includeDeviceHeader') && $this->includeDeviceHeader && $this->deviceHeader) {
            $this->addHeaders([ExternalAuth::DEVICE_HEADER => $this->deviceHeader], false);
        }
        if (property_exists($this, 'includeUserHeader') && $this->includeUserHeader && $this->userHeader) {
            $this->addHeaders([ExternalAuthUser::USER_AUTH_HEADER => $this->userHeader], false);
        }
        if (property_exists($this, 'includeDuiHeader') && $this->includeDuiHeader && $this->duiHeader) {
            $this->addHeaders([ExternalAuthBiometry::DUI_HEADER => $this->duiHeader], false);
        }
        if (property_exists($this, 'includeBiometryHeader') && $this->includeBiometryHeader && $this->biometryHeader) {
            $this->addHeaders([ExternalAuthBiometry::BIOMETRY_HEADER => $this->biometryHeader], false);
        }

        // Finally add all of them.
        $this->http->withHeaders($this->headers);

        $this
            ->http
            ->asJson()
            ->acceptJson();
    }

    /**
     * Returns the status of the response.
     *
     * @return int
     */
    public function getStatus(): int
    {
        return $this->response->status();
    }

    /**
     * Calls the source for the info and returns based on successful
     * response.
     *
     * @return bool
     */
    public function ping(): bool
    {
        try {
            $this->fetch();
        } catch (RequestException) {
            return false;
        }

        return true;
    }

    /**
     * Adds headers to the call.
     *
     * @param array $headers
     * @param bool $shouldFilter
     * @return self
     */
    public function addHeaders(array $headers, bool $shouldFilter = true): self
    {
        $this->headers = array_merge(
            $this->headers,
            $shouldFilter ? $this->filterHeaders($headers) : $headers,
        );

        return $this;
    }

    /**
     * Removes some entries from $headers.
     *
     * @param array $headers
     * @return array
     */
    private function filterHeaders(array $headers): array
    {
        return collect($headers)
            ->filter(fn($_, $header) => in_array(strtolower($header), self::HEADER_WHITE_LIST))
            ->all();
    }
}
