<?php

declare(strict_types=1);

namespace App\Repos;

use App\DTO\FaceAuthRequestDTO;
use Illuminate\Http\Client\Response;

class CallFaceAuthenticationRepo extends ApiRepo
{
    protected string $path = 'faceauthentication';

    /**
     * @param FaceAuthRequestDTO $values
     */
    public function prepare(mixed $values): self
    {
        $this->addHeaders([
            'UserAuth' => $values->customerId,
            'DeviceAuth' => $values->deviceId
        ]);

        $this->body = $values->toArray();

        return $this;
    }

    public function call(): Response
    {
        return $this->http->post($this->path, $this->body);
    }
}
