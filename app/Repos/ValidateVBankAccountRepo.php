<?php

declare(strict_types=1);

namespace App\Repos;

use App\DTO\VBankAccountDTO;
use App\DTO\ExternalRequestResponse;
use Illuminate\Http\Client\Response;
use Symfony\Component\HttpFoundation\Response as ResponseStatus;

class ValidateVBankAccountRepo extends ApiRepo
{
    protected string $path = "ValidateVBankAccount";

    protected bool $shouldDowngradeApiVersion = true;

    public function prepare($values): self
    {
        $this->queryParams = $values;

        return $this;
    }

    public function call(): Response
    {
        return $this->http->get($this->path, $this->queryParams);
    }

    public function fetch(): ?VBankAccountDTO
    {
        $this->fetchRaw();

        $response = $this->response->throw()->json();

        if (
            $this->response->status() === ResponseStatus::HTTP_OK &&
            data_get($response, 'Status.ResponseStatus.Code') === ResponseStatus::HTTP_NOT_FOUND
        ) {
            return null;
        }

        return $this->into($response);
    }

    protected function into(array $json): VBankAccountDTO
    {
        $responseData = ExternalRequestResponse::fromExternalResponse($json);

        return VBankAccountDTO::make($responseData->data);
    }
}
