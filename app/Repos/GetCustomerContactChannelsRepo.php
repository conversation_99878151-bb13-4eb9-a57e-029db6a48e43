<?php

namespace App\Repos;

use App\Dtos\ContactChannels;
use App\Dtos\Customer;
use App\Dtos\Email;
use App\Dtos\Phone;
use App\Traits\IncludesAuthHeaders;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;

/**
 * Returns contact info of a customer. Email, phone number.
 */
class GetCustomerContactChannelsRepo extends ApiRepo
{
    use IncludesAuthHeaders;

    public const PHONE_TYPE_CODE = 'CT_PHONE_CONFIRMED';
    public const EMAIL_TYPE_CODE = 'CT_EMAIL_CONFIRMED';

    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'CustomerContacts';

    /**
     * Prepares the call with $customerId.
     *
     * @param string $customerId
     * @return self
     */
    public function prepare($customerId): self
    {
        $this->queryParams = [
            'CustomerId' => $customerId,
        ];

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->get($this->path, $this->queryParams);
    }

    /**
     * Transforms the response array.
     *
     * @param array $json
     * @return ContactChannels
     */
    protected function into(array $json)
    {
        $data = $json['Data'];
        $channels = collect();
        foreach ($data['ContactTypes'] as $contactContainer) {
            $channelTypeClass = match ($contactContainer['ContactTypeCode']) {
                self::PHONE_TYPE_CODE => Phone::class,
                self::EMAIL_TYPE_CODE => Email::class,
            };

            $channels = $channels->merge($this->intoChannels(
                $contactContainer['CustomerContacts'],
                $channelTypeClass,
            ));
        }

        return ContactChannels::fromCollection($channels);
    }

    /**
     * Extracts a value into an implementation of App\Contracts\ContactChannel.
     *
     * @param array $channelsInfo
     * @param string $channelType A class of App\Dtos\ContactChannel.
     * @return Collection Of ContactChannel.
     */
    private function intoChannels(array $channelsInfo, string $channelType): Collection
    {
        return collect($channelsInfo)
            ->map(fn ($info) => $channelType::make($info['ContactValue']));
    }
}
