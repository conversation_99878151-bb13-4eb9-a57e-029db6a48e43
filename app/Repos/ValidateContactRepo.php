<?php

namespace App\Repos;

use Illuminate\Http\Client\Response;

class ValidateContactRepo extends ApiRepo
{
    /**
     * Path of the request.
     *
     * May be prepared.
     */
    protected string $path = 'CCApplicationContacts';

    /**
     * Prepares the call with $emailOrPhone.
     *
     * @param string $emailOrPhone
     * @return self
     */
    public function prepare($emailOrPhone): self
    {
        $this->queryParams = [
            'Contact' => $emailOrPhone,
        ];

        return $this;
    }

    /**
     * Make the actual call by calling the internal HTTP client.
     *
     * @return \Illuminate\Http\Client\Response
     */
    protected function call(): Response
    {
        return $this
            ->http
            ->get($this->path, $this->queryParams);
    }

    /**
     * Whether $emailOrPhone already has credit card or a request
     * in process.
     *
     * Assumes true if there is an error making the call.
     *
     * @param string $emailOrPhone
     * @return bool
     */
    public function contactExists(string $emailOrPhone): bool
    {
        return $this
            ->prepare($emailOrPhone)
            ->fetchRaw()['Data'] ?? true;
    }
}
