<?php

declare(strict_types=1);

namespace App\Repos;

use App\Repos\ApiRepo;
use Illuminate\Http\Client\Response;

class ValidateVBankCredentialsRepo extends ApiRepo
{
    protected string $path = 'LoginVBankAccount';

    protected bool $shouldDowngradeApiVersion = true;

    public function prepare($values): self
    {
        $this->body = $values;

        return $this;
    }

    public function call(): Response
    {
        return $this->http->post($this->path, $this->body);
    }
}
