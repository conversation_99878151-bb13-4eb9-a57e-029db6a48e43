<?php

declare(strict_types=1);

namespace App\Repos;

use App\Repos\ApiRepo;
use Illuminate\Http\Client\Response;

class SendVBankUsernameEmailRepo extends ApiRepo
{
    protected string $path = 'SendUsernameByEmail';

    protected bool $shouldDowngradeApiVersion = false;

    /**
     * @param array{customerId: string, deviceId: string} $values
     */
    public function prepare($values): self
    {
        ['customerId' => $customerId, 'deviceId' => $deviceId] = $values;

        $this->addHeaders([
            'UserAuth' => $customerId,
            'DeviceAuth' => $deviceId,
        ], shouldFilter: false);

        $this->body = ['CustomerId' => $customerId];

        return $this;
    }

    public function call(): Response
    {
        return $this->http->post($this->path, $this->body);
    }
}
