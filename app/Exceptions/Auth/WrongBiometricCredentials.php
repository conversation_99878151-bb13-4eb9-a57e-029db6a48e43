<?php

namespace App\Exceptions\Auth;

use Illuminate\Http\JsonResponse;
use App\Enums\Code\PasswordLoginCode;
use App\Contracts\BaseDomainException;
use Symfony\Component\HttpFoundation\Response;

class WrongBiometricCredentials extends BaseDomainException
{
    protected const ID = 'wrong_biometric_credentials';

    protected $message = 'Tus datos de inicio no son correctos. Intenta nuevamente.';

    /**
     * @var PasswordLoginCode
     */
    protected $errorCode = PasswordLoginCode::WRONG_BIOMETRIC_CREDENTIALS;

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => $this->message,
            'error_code' => self::getErrorCode(),
            'code' => $this->errorCode,
            'result' => $this->errorCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_BAD_REQUEST);
    }
}
