<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeviceDoesNotHaveChallenge extends BaseDomainException
{
    protected const ID = 'device_without_challenge';

    protected $message = '<PERSON><PERSON> lacks a challenge';

    /**
     * @var PasswordLoginCode
     */
    protected $errorCode = PasswordLoginCode::DEVICE_MISSING_CHALLENGE;

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => $this->message,
            'error_code' => self::getErrorCode(),
            'code' => $this->errorCode->value,
            'result' => $this->errorCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
