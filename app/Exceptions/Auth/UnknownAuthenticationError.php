<?php

declare(strict_types=1);

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use Illuminate\Http\JsonResponse;
use App\Enums\Code\PasswordLoginCode;
use Symfony\Component\HttpFoundation\Response;

class UnknownAuthenticationError extends BaseDomainException
{
    protected const ID = 'unknown_authentication_error';

    protected $message = 'Ocurrió un error inesperado al authenticar al usuario.';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::UNEXPECTED_ERROR;

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => $this->message,
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value,
            'result' => $this->exceptionCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
