<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class WrongCredentials extends BaseDomainException
{
    protected const ID = 'wrong_credentials';

    protected $message = 'No pudimos iniciar sesión. Asegúrate de que tu usuario y contraseña estén correctos.';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::WRONG_CREDENTIALS;

    public function __construct(
        public readonly string $ip,
        public readonly string $nickname,
        public readonly ?int $retriesLeft = null,
    ) {}

    public function render(): JsonResponse
    {
        $responseData = [
            'message' => $this->message,
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value,
            'result' => $this->exceptionCode->getCodeForHomologatedUsers()?->value
        ];

        if ($this->retriesLeft) {
            $responseData = array_merge($responseData, ['available_attempts' => $this->retriesLeft]);
        }

        return response()->json(data: $responseData, status: Response::HTTP_BAD_REQUEST);
    }
}
