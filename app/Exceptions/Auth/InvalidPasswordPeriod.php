<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use App\Services\Configurations as Config;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class InvalidPasswordPeriod extends BaseDomainException
{
    protected const ID = 'invalid_password_period';

    protected $message = 'The password is outside of the valid period. Password must be updated';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::EXPIRE_PASSWORD;

    public function render(): JsonResponse
    {
        $expiredPasswordMessage = app(Config::class)->getConfigurations('MENSAJE_PASS_EXPIRADA');

        return response()->json([
            'message' => $expiredPasswordMessage,
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value
        ], status: Response::HTTP_BAD_REQUEST);
    }
}
