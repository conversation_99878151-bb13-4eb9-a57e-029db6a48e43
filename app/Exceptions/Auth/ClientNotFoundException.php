<?php

declare(strict_types=1);

namespace App\Exceptions\Auth;

use Exception;

class ClientNotFoundException extends Exception
{
    public $message = "Customer not found";

    public static function withCustomerId(string $customerId): self
    {
        $exception = new self("El cliente con customer id {$customerId} no fue encontrado.");

        $exception->code = "client_not_found";

        return $exception;
    }
}
