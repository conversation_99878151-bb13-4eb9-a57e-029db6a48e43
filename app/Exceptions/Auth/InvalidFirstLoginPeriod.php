<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class InvalidFirstLoginPeriod extends BaseDomainException
{
    protected const ID = 'invalid_first_login_period';

    protected $message = 'The user must change the first password';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::EXPIRE_PASSWORD;

    public function render(): JsonResponse
    {
        return response()->json([
            'message' => trans('passwords.invalid_first_login_period'),
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value
        ], status: Response::HTTP_BAD_REQUEST);
    }
}
