<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class UserIsDisabled extends BaseDomainException
{
    protected const ID = 'user_disabled';

    protected $message = 'A disabled user cannot use the system';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::DISABLED_USER;

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => trans('auth.user_blocked'),
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value,
            'result' => $this->exceptionCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_FORBIDDEN);
    }
}
