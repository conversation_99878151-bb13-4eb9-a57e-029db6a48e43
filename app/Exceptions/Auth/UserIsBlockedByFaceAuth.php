<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class UserIsBlockedByFaceAuth extends BaseDomainException
{
    protected const ID = 'user_blocked_by_face_auth';

    protected $message = 'The user is blocked by face auth';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::DISABLED_USER;

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => trans('auth.user_blocked'),
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value,
            'result' => $this->exceptionCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_FORBIDDEN);
    }
}
