<?php

namespace App\Exceptions\Auth;

use App\Dtos\ErrorMessage;
use App\Traits\HasErrorCode;
use DomainException;
use Illuminate\Http\JsonResponse;

class InvalidRefreshToken extends DomainException
{
    use HasErrorCode;

    const ID = 'invalid_refresh_token';

    protected $message = 'Invalid refresh token';

    public function render(): JsonResponse
    {
        return response()->json(
            ErrorMessage::make(
                'El refresh token es inválido o ha caducado. Ingresa con tus credenciales de nuevo.',
                self::getErrorCode(),
            )->toSnakeCaseArray(),
            400,
        );
    }
}
