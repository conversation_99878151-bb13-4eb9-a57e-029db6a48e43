<?php

namespace App\Exceptions\Auth;

use Illuminate\Http\JsonResponse;
use App\Enums\Code\PasswordLoginCode;
use App\Contracts\BaseDomainException;
use Symfony\Component\HttpFoundation\Response;

class DeviceNotFound extends BaseDomainException
{
    protected const ID = 'device_not_found';

    protected $message = 'Device not found';

    /**
     * @var PasswordLoginCode
     */
    protected $errorCode = PasswordLoginCode::DEVICE_NOT_FOUND;

    public function render(): JsonResponse
    {
        return response()->json([
            'message' => trans('passwords.device_not_found'),
            'error_code' => self::getErrorCode(),
            'code' => $this->errorCode->value,
            'result' => $this->errorCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_BAD_REQUEST);
    }
}
