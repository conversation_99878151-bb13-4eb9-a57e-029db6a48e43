<?php

declare(strict_types=1);

namespace App\Exceptions\Auth;

use App\Http\Responses\Auth\LoginResponse;
use App\Traits\HasErrorCode;
use DomainException;

class ExternalTokenGenerationFailed extends DomainException
{
    use HasErrorCode;

    protected const ID = 'failed_to_create_external_token';

    protected LoginResponse $response;

    public function __construct()
    {
        $this->response = new LoginResponse();
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render()
    {
        return $this->response->externalTokenServiceFailure(code: self::ID);
    }
}
