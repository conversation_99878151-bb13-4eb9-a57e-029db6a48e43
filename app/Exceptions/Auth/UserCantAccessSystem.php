<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use App\Enums\Message\AuthMessage;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class UserCantAccessSystem extends BaseDomainException
{
    protected const ID = 'no_permission_for_system';

    protected $message = 'The user does not have permission to access the default system';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::INVALID_SYSTEM_PERMISSIONS;

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => AuthMessage::NOT_PERMISSIONS_SYSTEM->value,
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value,
            'result' => $this->exceptionCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_FORBIDDEN);
    }
}
