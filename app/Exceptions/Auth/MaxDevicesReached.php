<?php

namespace App\Exceptions\Auth;

use App\Contracts\BaseDomainException;
use App\Enums\Code\PasswordLoginCode;
use App\Enums\Message\AuthMessage;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class MaxDevicesReached extends BaseDomainException
{
    protected const ID = 'max_devices_reached';

    protected $message = 'The user cannot register another device';

    /**
     * @var PasswordLoginCode
     */
    protected $exceptionCode = PasswordLoginCode::MAXIMUM_DEVICES_REACHED;

    public function render(): JsonResponse
    {
        return response()->json([
            'message' => AuthMessage::MAXIMUM_REGISTERED_DEVICES->value,
            'error_code' => self::getErrorCode(),
            'code' => $this->exceptionCode->value,
            'result' => $this->exceptionCode->getCodeForHomologatedUsers()?->value
        ], status: Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
