<?php

namespace App\Exceptions\Auth;

use Illuminate\Http\JsonResponse;
use App\Enums\Code\PasswordLoginCode;
use App\Contracts\BaseDomainException;
use Symfony\Component\HttpFoundation\Response;

class TooManyLoginAttempts extends BaseDomainException
{
    const ID = 'too_many_attempts';

    protected $message = 'Demasiados intentos.';

    /**
     * @var PasswordLoginCode
     */
    protected $errorCode = PasswordLoginCode::TOO_MANY_LOGIN_ATTEMPTS;

    public function __construct(
        public readonly string $ip,
        public readonly int $lockedOutMinutes = 5,
    ) {}

    public function render(): JsonResponse
    {
        return response()->json(data: [
            'message' => $this->message,
            'error_code' => self::getErrorCode(),
            'code' => $this->errorCode->value,
            'result' => $this->errorCode->getCodeForHomologatedUsers()?->value,
            'blocking_time' => (string) $this->lockedOutMinutes
        ], status: Response::HTTP_TOO_MANY_REQUESTS);
    }
}
