<?php

declare(strict_types=1);

namespace App\Factory;

use App\Events\ExternalCallMadeLegacyEvent;
use App\Models\ServicesRouter;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Psr7\Response;

class ClientFactory extends GuzzleClient
{
    public const RE_NAME = 'DEL';
    public const PIVOTE = "?";

    public function customeResponse(
        string $url,
        mixed $parameters = null,
        ?string $urlbase = "",
        ?string $fullUrl = "",
        null|string|Response $content = "",
        ?array $headers = [],
    ): array {
        $array = explode("/", explode(self::PIVOTE, $url)[0]);
        $service = $this->getServiceRoute($array);
        $arrayHeader = $this->getHeadersServices([$service->token]);
        $method =  $this->getMethodService([$service->method]);
        $repleaceUrl = $this->getUrlService([
            $urlbase,
            $service->third_parties_url,
            $parameters,
            $fullUrl
        ]);
        $stream = \GuzzleHttp\Psr7\Utils::streamFor($content);
        $response = $this->request(
            $method,
            $repleaceUrl,
            [
                "headers" => $arrayHeader,
                "body" => $stream
            ]
        );
        $responseContent = json_decode($response->getBody()->getContents(), true);

        ExternalCallMadeLegacyEvent::dispatch(
            $repleaceUrl,
            $service->exposed_service_url,
            $method,
            $arrayHeader,
            $parameters,
            (string)$stream,
            $response->getStatusCode(),
            $responseContent,
        );

        return [
            "content" => $responseContent,
            "status" => $response->getStatusCode()
        ];
    }

    public function customeResponseAsync(
        string $url,
        mixed $parameters,
        string $urlbase,
        string $fullUrl,
        string | Response $content
    ): PromiseInterface {
        $array = explode("/", explode(self::PIVOTE, $url)[0]);
        $service = $this->getServiceRoute($array);
        $arrayHeader = $this->getHeadersServices([$service->token]);
        $method =  $this->getMethodService([$service->method]);
        $repleaceUrl = $this->getUrlService([
            $urlbase,
            $service->third_parties_url,
            $parameters,
            $fullUrl
        ]);
        $stream = \GuzzleHttp\Psr7\Utils::streamFor($content);

        ExternalCallMadeLegacyEvent::dispatch(
            $repleaceUrl,
            $service->exposed_service_url,
            $method,
            $arrayHeader,
            $parameters,
            (string)$stream,
            null,
        );

        return $this->requestAsync(
            $method,
            $repleaceUrl,
            [
                "headers" => $arrayHeader,
                "body" => $stream
            ]
        );
    }

    /**
     * get route of services
     *
     * @param array $array
     * @return ServicesRouter
     */
    private function getServiceRoute(array $array): ServicesRouter
    {
        // Constructs the exposed service URL by using the relevant parts of the provided array.
        $exposedServiceUrl = count($array) > 3
            ? implode('/', array_slice($array, 2))
            : end($array);

        return ServicesRouter::select(
            "service_name",
            "exposed_service_url",
            "method",
            "third_parties_url",
            "token"
        )
            ->where("exposed_service_url", $exposedServiceUrl)
            ->first();
    }

    /**
     * get route of array Headers
     *
     * @param array $array
     * @return array
     */
    private function getHeadersServices(array $array): array
    {
        $arrayHeader['apiKey']          = $array[0];
        $arrayHeader['Content-Type']    = 'application/json';
        $arrayHeader['Accept'] = 'application/json';
        $arrayHeader['IpAddressAuth'] = request()->ip();

        if (request()->hasHeader('DeviceAuth')) {
            $arrayHeader['DeviceAuth'] = request()->header('DeviceAuth');
        }
        if (request()->hasHeader('DuiAuth')) {
            $arrayHeader['DuiAuth'] = request()->header('DuiAuth');
        }
        if (request()->hasHeader('BiometryAuth')) {
            $arrayHeader['BiometryAuth'] = request()->header('BiometryAuth');
        }
        if (request()->hasHeader('UserAuth')) {
            $arrayHeader['UserAuth'] = request()->header('UserAuth');
        }
        if (request()->bearerToken()) {
            $arrayHeader['TokenAuth'] = request()->bearerToken();
        }

        return $arrayHeader;
    }

    /**
     * get route of array Metod
     *
     * @param array $array
     * @return string
     */
    private function getMethodService(array $array): string
    {
        return $array[0] !== self::RE_NAME ? $array[0] : "DELETE";
    }

    /**
     * get route of array url
     *
     * @param array $array
     * @return string
     */
    private function getUrlService(array $array): string
    {
        $repleaceUrl = str_replace($array[0], $array[1] . $array[2], $array[3]);
        if (empty($repleaceUrl)) {
            $repleaceUrl = $array[1] . $array[2];
        }
        return $repleaceUrl;
    }

    public function convertParameterToString(array $params): string
    {
        $stringConvert = self::PIVOTE;
        end($params);
        $lastKey = key($params);
        foreach ($params as $key => $value) {
            if ($key === $lastKey) {
                $stringConvert = $stringConvert . "$key=$value";
            } else {
                $stringConvert = $stringConvert . "$key=$value&";
            }
        }
        if ($stringConvert === self::PIVOTE) {
            $stringConvert = "";
        }
        return $stringConvert;
    }
}
