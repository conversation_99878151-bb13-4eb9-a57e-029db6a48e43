<?php

declare(strict_types=1);

namespace App\DTO;


readonly class FaceAuthRequestDTO
{
    public function __construct(
        public int $ccApplicationId,
        public string $method,
        public string $query,
        public string $imageBuffer,
        public string $deviceId,
        public string $customerId,
    ) {}

    public static function make(array $data): self
    {
        return new self(
            ccApplicationId: (int) data_get($data, 'CCApplicationId'),
            method: data_get($data, 'Method'),
            query: data_get($data, 'Querry'),
            imageBuffer: data_get($data, 'ImageBuffer'),
            deviceId: data_get($data, 'DeviceId'),
            customerId: data_get($data, 'CustomerId')
        );
    }

    public function toArray(): array
    {
        return [
            'CCApplicationId' => $this->ccApplicationId,
            'Method' => $this->method,
            'Querry' => $this->query,
            'ImageBuffer' => $this->imageBuffer,
        ];
    }
}
