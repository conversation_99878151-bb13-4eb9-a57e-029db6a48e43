<?php

declare(strict_types=1);

namespace App\DTO;

readonly class VBankAccountDTO
{
    public function __construct(
        public string $username,
        public bool $requiresPasswordReset,
    ) {}

    public static function make(array $data): self
    {
        return new self(
            username: data_get($data, 'Username'),
            requiresPasswordReset: data_get($data, 'RequirePasswordReset')
        );
    }

    public function toArray(): array
    {
        return [
            'username' => $this->username,
            'requiresPasswordReset' => $this->requiresPasswordReset
        ];
    }
}
