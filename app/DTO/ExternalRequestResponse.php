<?php

declare(strict_types=1);

namespace App\DTO;

use App\Dtos\Dto;

class ExternalRequestResponse extends Dto
{
    public function __construct(
        public readonly mixed $data = null,
        public ?int $requestStatusCode = 200,
        public readonly ?string $requestStatusMessage = '',
        public ?int $responseStatusCode = 0,
        public readonly ?string $responseStatusMessage = '',
    ) {}

    public static function fromLegacyExternalResponse(array $data): self
    {
        return new self(
            data: data_get($data, 'content.Data'),
            requestStatusCode: data_get($data, 'content.Status.RequestStatus.Code'),
            requestStatusMessage: data_get($data, 'content.Status.RequestStatus.Message'),
            responseStatusCode: data_get($data, 'content.Status.ResponseStatus.Code'),
            responseStatusMessage: data_get($data, 'content.Status.ResponseStatus.Message'),
        );
    }

    public static function fromExternalResponse(array $data): self
    {
        return new self(
            data: data_get($data, 'Data'),
            requestStatusCode: data_get($data, 'Status.RequestStatus.Code'),
            requestStatusMessage: data_get($data, 'Status.RequestStatus.Message'),
            responseStatusCode: data_get($data, 'Status.ResponseStatus.Code'),
            responseStatusMessage: data_get($data, 'Status.ResponseStatus.Message'),
        );
    }

    public function toResponseArray(): array
    {
        return [
            'Data' => $this->data,
            'Status' => [
                'RequestStatus' => [
                    'Code' => $this->requestStatusCode,
                    'Message' => $this->requestStatusMessage,
                ],
                'ResponseStatus' => [
                    'Code' => $this->responseStatusCode,
                    'Message' => $this->responseStatusMessage,
                ],
            ],
        ];
    }
}
