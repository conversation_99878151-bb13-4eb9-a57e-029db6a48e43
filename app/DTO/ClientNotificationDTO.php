<?php

declare(strict_types=1);

namespace App\DTO;

use App\Enums\Message\Email;
use App\Enums\Code\EmailCode;

readonly class ClientNotificationDTO
{
    public function __construct(
        public string $clientName,
        public string $emailTo,
        public string $phoneNumber,
        public string $templateCode = Email::TemplateCodeEmail->value,
        public int $channelId = EmailCode::ChannelIdEmail->value,
        public string $phoneCountryCode = Email::PhoneCountryCode->value,
        public string $emailFrom = Email::EmailFrom->value,
        public string $emailCC = Email::EmailCC->value,
        public string $emailBCC =  Email::EmailBCC->value,
        public string $subject = Email::Subject->value,
        public string $otp = Email::OTP->value,
        public string $alternativeChannelIds = Email::AlternativeChannelIdsEmail->value,
    ) {
    }

    public function toJson(): string
    {
        return '{
            "TemplateCode": "' . $this->templateCode . '",
            "ChannelId": "' . $this->channelId . '",
            "PhoneNumber": "' . $this->phoneNumber . '",
            "PhoneCountryCode": "' . $this->phoneCountryCode . '",
            "EmailFrom": "' . $this->emailFrom . '",
            "EmailFromName": "Banco Atlántida",
            "EmailTo": "' . $this->emailTo . '",
            "EmailCC": "' . $this->emailCC . '",
            "EmailBCC": "' . $this->emailBCC . '",
            "Subject": "' . $this->subject . '",
            "Parameters": {
                "ClientName": "' . $this->clientName . '",
                "OTP": "' . $this->otp . '",
                "AlternativeChannelIds": ' . $this->alternativeChannelIds . ',
        }';
    }
}
