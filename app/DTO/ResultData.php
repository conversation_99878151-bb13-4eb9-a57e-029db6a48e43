<?php

declare(strict_types=1);

namespace App\DTO;

use App\Abstract\DataTransferObject;

class ResultData extends DataTransferObject
{
    public int $status;
    public string | null $result;
    public string | null | int $code;
    public string | null $message;
    public string | null $ip;
    public string | null $endpointName;
    public string | null $deviceId;
    public int | null $clientId;

    public function toResponseArray(): array
    {
        return [
            'result' => $this->result,
            'status' => $this->status,
            'code' => data_get($this, 'code', null),
            'message' => data_get($this, 'message', null),
        ];
    }
}
