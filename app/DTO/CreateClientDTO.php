<?php

declare(strict_types=1);

namespace App\DTO;

use App\Dtos\ContactChannels;
use App\Dtos\Customer;

readonly class CreateClientDTO
{
    public function __construct(
        public Customer $customer,
        public ContactChannels $contactChannels,
        public string $creditCardApplicationId,
        public bool $homologated,
        public string $password,
        public ?string $nickname,
        public ?string $nicknameVbank,
    ) {}

    public function toArray(): array
    {
        return [
            'mode' => 'CREATE',
            'group' => 'BLACK',
            'send_notification' => true,
            'nickname' => $this->nickname,
            'nickname_vbank' => $this->nicknameVbank,
            'client_id' => $this->customer->id,
            'first_name' => $this->customer->firstName,
            'second_name' => $this->customer->secondName,
            'first_surname' => $this->customer->firstSurname,
            'password' => bcrypt($this->password),
            'second_surname' => $this->customer->secondSurname,
            'married_surname' => $this->customer->marriedName,
            'dui' => $this->customer->dui,
            'nit' => $this->customer->nit,
            'email' => $this->contactChannels->getFirstEmail(),
            'phone_number' => $this->contactChannels->getFirstPhone(),
            'creditcard_application_id' => $this->creditCardApplicationId,
            'homologated' => $this->homologated,
        ];
    }
}
