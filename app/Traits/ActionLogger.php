<?php

namespace App\Traits;

use App\DTO\ResultData;
use App\Enums\Code\Data;
use App\Enums\LogApplicationCode;
use App\Factory\ClientFactory;
use App\Models\EndpointsLog;
use App\Models\RegisterClientsApp;
use App\Services\Log;
use Illuminate\Support\Facades\App;

/**
 * Log client actions
 *
 */
trait ActionLogger
{
    /**
     * Create log service register in BAES DB
     *
     * @param string $customerId
     * @param string $process
     * @param string $description
     * @param string $comments
     */
    public function logBank(
        string $customerId,
        LogApplicationCode $code,
        string $comments = 'OK'
    ) {
        if (App::environment('testing')) {
            return;
        }

        try {
            $clientFactory = app(ClientFactory::class);

            $params = json_encode([
                'CustomerId' => $customerId,
                'LogItemsCollection' => [
                    [
                        'LogApplicationCode' => $code->getValue(),
                        'Description' => $code->getDescription(),
                        'Comments' => $comments
                    ]
                ]
            ]);

            $clientFactory->customeResponseAsync(
                'LogApplication',
                "",
                "",
                "",
                $params
            );
        } catch (\Exception $exception) {
            /** @var Log */
            $logger = app()->make(Log::class);
            $logger->error('Failed to log on bank: ' . $exception->getMessage());
        }
    }

    /**
     * Create a register for the client's actions
     *
     * @param ResultData $data
     * @return bool
     */
    public function registerClientApp(ResultData $data): bool
    {
        if (App::environment('testing')) {
            return true;
        }

        $registerClientApp                     =  new RegisterClientsApp();
        $registerClientApp->endpoint_name      = $data->endpointName;
        $registerClientApp->status_response    = $data->status;
        $registerClientApp->client_id          = $data->clientId;
        $registerClientApp->device_id          = $data->deviceId;
        $registerClientApp->ip                 = $data->ip;
        $registerClientApp->result             = $data->result;
        $registerClientApp->response_time_ms = $this->getRequestElapsedTime();

        return $registerClientApp->save();
    }

    /**
     * Create a endpoint for the api's actions
     *
     * @param ResultData $data
     * @return bool
     */
    public function endpointLog(ResultData $data): bool
    {
        $endpointLog                    = new EndpointsLog();
        $endpointLog->endpoint_name     = $data->endpointName;
        $endpointLog->status_response   = $data->status;
        $endpointLog->system_code       = Data::SYSTEM_CODE_APP->value;
        $endpointLog->result            = $data->result;
        $endpointLog->ip                = $data->ip;
        $endpointLog->response_time_ms = $this->getRequestElapsedTime();

        return $endpointLog->save();
    }

    /**
     * Returns the current elapsed time in ms since this request
     * reached Laravel.
     *
     * @return int Time in ms.
     */
    private function getRequestElapsedTime(): int
    {
        return (int) floor((microtime(true) - LARAVEL_START) * 1000);
    }
}
