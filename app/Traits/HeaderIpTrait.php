<?php

declare(strict_types=1);

namespace App\Traits;

use App\Services\Crypt\Rsa;
use Illuminate\Http\Request;

use function is_null;

trait HeaderIpTrait
{
    public function getIpHeader(Request $request): string
    {
        $ip = $request->header('X-Project-Device');
        if (is_null($request->header('X-Project-Device'))) {
            $ip = $request->ip();
        } else {
            $rsa = new Rsa();
            $ip = $rsa->decrypt($ip);
        }
        return $ip;
    }
}
