<?php

declare(strict_types=1);

namespace App\Traits\Client;

use App\Models\Client;
use App\Models\Systems;

trait HasClientStatusChecks
{
    /**
     * Whether user has access to the default system.
     *
     * @return bool
     */
    public function hasDefaultSystem(): bool
    {
        return $this
            ->systems()
            ->where('system_code', Systems::DEFAULT)
            ->exists();
    }

    /**
     * Whether user has registered a device with $deviceId.
     *
     * @param string $deviceId
     * @return bool
     */
    public function hasDevice(string $deviceId): bool
    {
        return $this
            ->devices()
            ->where('device_id', $deviceId)
            ->exists();
    }

    /**
     * Whether this user has not changed their first password.
     *
     * @return bool
     */
    public function hasOnlyFirstPassword(): bool
    {
        return empty($this->passwords()->count());
    }

    /**
     * Whether user status is DISABLE.
     *
     * @return bool
     */
    public function isDisabled(): bool
    {
        return $this->status == Client::DISABLE;
    }

    /**
     * Whether user status is B<PERSON><PERSON><PERSON><PERSON>.
     *
     * @return bool
     */
    public function isBlockedByOtp(): bool
    {
        return $this->status == Client::BLOCKOTP;
    }

    /**
     * Whether user status is BLOCK_BY_FACE_AUTH.
     *
     * @return bool
     */
    public function isBlockedByFaceAuth(): bool
    {
        return $this->status == Client::BLOCK_BY_FACE_AUTH;
    }

    /**
     * Whether there is a user with $username as nickname.
     *
     * @param string $username
     * @return bool
     */
    public static function usernameExists(string $username): bool
    {
        return self::where('nickname', $username)->exists();
    }
}
