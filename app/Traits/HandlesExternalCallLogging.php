<?php

namespace App\Traits;

use App\DTO\ExternalRequestResponse;
use Symfony\Component\HttpFoundation\Response;

trait HandlesExternalCallLogging
{
    private const VALID_STATUS_CODES = [0, Response::HTTP_OK];

    private const WHITELISTED_ENDPOINTS = [
        'AppBaMessages',
        'LogApplications'
    ];

    private function isUnsuccessfulResponse(string $path, int $statusCode, array $response = []): bool
    {
        if (in_array($path, self::WHITELISTED_ENDPOINTS)) {
            return true;
        }

        if (empty($response)) {
            return true;
        }

        $responseDTO = ExternalRequestResponse::fromExternalResponse($response);

        return $statusCode >= Response::HTTP_BAD_REQUEST
            || !in_array($responseDTO->responseStatusCode, self::VALID_STATUS_CODES)
            || !in_array($responseDTO->requestStatusCode, self::VALID_STATUS_CODES);
    }

    private function buildContext(array $baseContext, array $additionalContext, bool $isUnsuccessful): array
    {
        if ($isUnsuccessful || !app()->isProduction()) {
            return array_merge($baseContext, $additionalContext);
        }

        return $baseContext;
    }
}
