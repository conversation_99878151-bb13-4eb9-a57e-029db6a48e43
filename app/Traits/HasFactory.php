<?php

namespace App\Traits;

use App\Contracts\DtoFactory;

trait HasFactory
{
    public static function factory(): DtoFactory
    {
        return new (self::resolveFactoryPath());
    }

    private static function resolveFactoryPath(): string
    {
        $path = static::getFactoryPath();
        if (!empty($path)) {
            return $path;
        }

        $dtoPath = explode('\\', static::class);
        $className = array_pop($dtoPath) . 'Factory';
        $dtoPath[] = 'Factories';
        $dtoPath[] = $className;
        $fullPath = implode('\\', $dtoPath);

        return $fullPath;
    }

    protected static function getFactoryPath(): string
    {
        return '';
    }
}
