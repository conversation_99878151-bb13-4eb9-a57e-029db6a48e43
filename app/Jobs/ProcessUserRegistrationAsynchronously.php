<?php

namespace App\Jobs;

use Exception;
use App\Dtos\Customer;
use App\Dtos\ContactChannels;
use App\Tasks\CreateUserTask;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use App\Tasks\ValidateVBankAccountTask;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessUserRegistrationAsynchronously implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job should wait before retrying.
     *
     * @var int
     */
    public $backoff = 60;

    public function __construct(
        public int $creditcardApplicationId,
        public Customer $customer,
        public ContactChannels $contactChannels,
    ) {
    }

    public function handle(
        CreateUserTask $createUserTask,
        ValidateVBankAccountTask $validateVBankAccountTask
    ): void {
        try {
            $createUserTask
                ->withCustomer($this->customer)
                ->withContactChannels($this->contactChannels)
                ->withCreditcardApplicationId($this->creditcardApplicationId);

            if (env('HOMOLOGATED_USERS')) {
                $vBankAccount = $validateVBankAccountTask
                    ->withCustomerId($this->customer->id)
                    ->do();

                $createUserTask->withVBankNickname($vBankAccount->username);
            }

            $createUserTask->do();
        } catch (Exception $exception) {
            Log::warning('VBankValidateAccount failed: ' . $exception->getMessage());
            throw $exception;
        }
    }
}
