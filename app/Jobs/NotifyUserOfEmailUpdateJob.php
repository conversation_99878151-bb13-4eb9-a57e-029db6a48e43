<?php

namespace App\Jobs;

use App\DTO\ClientNotificationDTO;
use App\Factory\ClientFactory;
use App\Models\Client;
use App\Models\NotificationHistories;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Log\LogManager as Log;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class NotifyUserOfEmailUpdateJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(private Client $user)
    {
    }

    /**
     * Notifies user.
     *
     * @param ClientFactory $http
     * @param Request $request
     * @param Log $log
     * @return void
     */
    public function handle(ClientFactory $http, Request $request, Log $log)
    {
        $body  = new ClientNotificationDTO(
            clientName: $this->user->full_name,
            emailTo: $this->user->email,
            phoneNumber: $this->user->phone_number
        );

        try {
            $http->customeResponse(
                url: 'BaMessagesSendMessage',
                content: $body->toJson(),
                headers: array_merge($request->headers->all(), ['IpAddressAuth' => $request->ip()]),
            );

            NotificationHistories::create([
                'client_id' => $this->user->id,
                'type' => 'MAIL',
                'message' => 'Su email se cambió satisfactoriamente',
            ]);
        } catch (Throwable $e) {
            $log->warning('Could not notify user of email updated', [
                'id' => $this->user->id,
                'exception' => $e->getMessage(),
            ]);
        }
    }
}
