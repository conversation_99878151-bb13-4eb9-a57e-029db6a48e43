<?php

namespace App\Tasks;

use App\Models\Device;

class ClearChallengeOfDeviceTask extends Task
{
    /**
     * The device to modify.
     */
    private ?Device $device;

    /**
     * Adds the device to be modified.
     *
     * @param \App\Models\Device $device
     * @return self
     */
    public function withDevice(Device $device): self
    {
        $this->device = $device;

        return $this;
    }

    /**
     * Clears the field `verification_challenge` of a device
     * and stores the result in DB.
     *
     * @return \App\Models\Device
     */
    public function do(): Device
    {
        $this->device->verification_challenge = null;
        $this->device->save();

        return $this->device;
    }
}
