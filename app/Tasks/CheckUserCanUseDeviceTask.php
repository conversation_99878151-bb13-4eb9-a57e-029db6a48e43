<?php

namespace App\Tasks;

use App\Models\Client;
use App\Services\Configurations as Config;

class CheckUserCanUseDeviceTask extends Task
{
    /**
     * User to check.
     */
    private ?Client $user = null;

    /**
     * Device ID to check.
     */
    private ?string $deviceId = null;

    public function __construct(private Config $config)
    {
    }

    public function withUser(Client $user)
    {
        $this->user = $user;

        return $this;
    }

    public function withDeviceId(string $deviceId)
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * Checks whether user already has a device registered, if not, whether
     * the user could register it.
     *
     * @return bool
     */
    public function do(): bool
    {
        if ($this->user->hasDevice($this->deviceId)) {
            return true;
        }

        return $this->user->devices()->count() < $this->config->maximunDevices();
    }
}
