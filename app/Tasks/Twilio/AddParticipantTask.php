<?php

namespace App\Tasks\Twilio;

use App\Tasks\Task;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Rest\Conversations\V1\Conversation\ParticipantInstance as TwilioParticipant;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class AddParticipantTask extends Task
{
    /**
     * The participant will be added to this Twilio conversation.
     */
    private ?TwilioConversation $conversation = null;

    /**
     * Twilio client to use.
     */
    private ?TwilioClient $twilioClient = null;

    /**
     * Identity of the participant.
     */
    private ?string $identity = null;

    public function withConversation(TwilioConversation $conversation): self
    {
        $this->conversation = $conversation;

        return $this;
    }

    public function withTwilioClient(TwilioClient $twilioClient): self
    {
        $this->twilioClient = $twilioClient;

        return $this;
    }

    public function withIdentity(string $identity): self
    {
        $this->identity = $identity;

        return $this;
    }

    /**
     * Adds a participant to $this->conversation.
     *
     * @return TwilioParticipant
     */
    public function do(): TwilioParticipant
    {
        return $this
            ->twilioClient
            ->conversations
            ->v1
            ->conversations($this->conversation->sid)
            ->participants
            ->create(['identity' => $this->identity]);
    }
}
