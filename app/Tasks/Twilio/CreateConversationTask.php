<?php

namespace App\Tasks\Twilio;

use App\Tasks\Task;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class CreateConversationTask extends Task
{
    /**
     * Twilio client for requests.
     */
    private ?TwilioClient $twilioClient = null;

    public function withTwilioClient(TwilioClient $twilioClient): self
    {
        $this->twilioClient = $twilioClient;

        return $this;
    }

    /**
     * Creates a Twilio conversation.
     *
     * @return TwilioConversation
     */
    public function do(): TwilioConversation
    {
        return $this
            ->twilioClient
            ->conversations
            ->v1
            ->conversations
            ->create(['friendlyName' => 'Conversation']);
    }
}
