<?php

namespace App\Tasks\Twilio;

use App\Services\Configurations as Config;
use App\Tasks\Task;
use Twilio\Jwt\AccessToken;
use Twilio\Jwt\Grants\ChatGrant;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class CreateTokenWithChatGrantTask extends Task
{
    /**
     * Conversation to which the token will be linked.
     */
    private ?TwilioConversation $conversation = null;

    /**
     * Identity of the participant.
     */
    private ?string $identity = null;

    public function __construct(
        private Config $config,
        private ChatGrant $chatGrant,
    ) {
    }

    public function withConversation(TwilioConversation $conversation): self
    {
        $this->conversation = $conversation;

        return $this;
    }

    public function withIdentity(string $identity): self
    {
        $this->identity = $identity;

        return $this;
    }

    /**
     * Creates an AccessToken.
     *
     * @return AccessToken
     */
    public function do(): AccessToken
    {
        $this
            ->chatGrant
            ->setServiceSid($this->conversation->chatServiceSid);

        $token = new AccessToken(
            $this->config->getConfigurations('TWILIO_ACCOUNT_SID'),
            $this->config->getConfigurations('TWILIO_APIKEY'),
            $this->config->getConfigurations('TWILIO_API_SECRET'),
            $this->config->getConfigurations('TWILIO_TOKEN_LIFETIME'),
            $this->identity,
        );

        return $token->addGrant($this->chatGrant);
    }
}
