<?php

namespace App\Tasks\Surveys;

use App\Models\Answer;
use App\Tasks\Task;
use Illuminate\Support\Collection;

class CreateAnswersTask extends Task
{
    /**
     * Answers to store in DB.
     */
    private ?Collection $answers = null;

    /**
     * Respondent ID.
     */
    private ?int $customerId = null;

    /**
     * Adds $answers.
     *
     * @param Collection A collection of \App\Dtos\Answer.
     * @return self
     */
    public function withAnswers(Collection $answers)
    {
        $this->answers = $answers;

        return $this;
    }

    /**
     * Adds $customerId.
     *
     * @param int $customerId
     * @return self
     */
    public function withCustomerId(int $customerId)
    {
        $this->customerId = $customerId;

        return $this;
    }

    /**
     * Stores $this->answers in DB.
     *
     * @return void
     */
    public function do(): void
    {
        $currentDateTime = now();

        $mappedAnswers = $this
            ->answers
            ->map(fn($answer) => $answer->toCustomArrayWith(
                [
                    'optionId' => 'option_id',
                    'other' => 'other_answers',
                ],
                [
                    'customer_id' => $this->customerId,
                    'created_at' => $currentDateTime,
                    'updated_at' => $currentDateTime,
                ],
            ));

        Answer::insert($mappedAnswers->toArray());
    }
}
