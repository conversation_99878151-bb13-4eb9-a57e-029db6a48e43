<?php

namespace App\Tasks\Surveys;

use App\Models\Option;
use App\Tasks\Task;

class CheckSurveyAndOptionRelationTask extends Task
{
    /**
     * Parent survey.
     */
    private ?int $surveyId = null;

    /**
     * Option to check
     */
    private ?int $optionId = null;

    public function withSurveyId(int $surveyId)
    {
        $this->surveyId = $surveyId;

        return $this;
    }

    public function withOptionId(int $optionId)
    {
        $this->optionId = $optionId;

        return $this;
    }

    /**
     * Returns true if the option and survey are related.
     *
     * @return bool
     */
    public function do(): bool
    {
        return Option::find($this->optionId)
            ->question
            ->survey
            ->id === $this->surveyId;
    }
}
