<?php

namespace App\Tasks;

use App\Models\Client;
use App\Services\AuthNotifyService;

class NotifyUserOfSuccesfulLoginTask extends Task
{
    /**
     * User to notify.
     */
    private ?Client $user = null;

    public function __construct(private AuthNotifyService $notifier)
    {
    }

    public function withUser(Client $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Notify the user, if any.
     *
     * @return void
     */
    public function do(): void
    {
        $this->notifier->successLogin($this->user);
    }
}
