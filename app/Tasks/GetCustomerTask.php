<?php

namespace App\Tasks;

use App\Dtos\Customer;
use App\Repos\GetCustomerRepo;

class GetCustomerTask extends Task
{
    /**
     * ID of customer to get.
     */
    private ?int $customerId = null;

    public function __construct(private GetCustomerRepo $repo)
    {
    }

    /**
     * ID of customer to get.
     *
     * @param int $customerId
     * @return self
     */
    public function withCustomerId(int $customerId)
    {
        $this->customerId = $customerId;

        return $this;
    }

    /**
     * Gets a customer info by calling an external service.
     *
     * @return Customer
     */
    public function do(): Customer
    {
        return $this
            ->repo
            ->prepare($this->customerId)
            ->fetch();
    }
}
