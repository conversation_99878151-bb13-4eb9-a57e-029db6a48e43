<?php

namespace App\Tasks\Auth;

use App\Models\Client;
use App\Models\PasswordHistories;
use App\Services\Configurations as Config;
use App\Tasks\Task;
use Carbon\Carbon;

class CheckPasswordPeriodTask extends Task
{
    /**
     * Owner of the password.
     */
    private ?Client $user = null;

    public function __construct(private Config $config)
    {
    }

    public function withUser(Client $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Checks whether the password of $this->user is inside the valid period.
     *
     * @return bool
     */
    public function do(): bool
    {
        if (!(bool) $this->config->getConfigurations('VERIFICAR_PERIODO_VALIDEZ_PASSWORD')) {
            return true;
        }

        // The user may not have a password change because it is inside the first login period
        // but outside the password valid period. So we check both.
        $validDays = (int) $this->config->getConfigurations('PERIODO_VALIDEZ_PASSWORD');
        $userRegisteredInsideValidPeriod = Carbon::parse($this->user->created_at)
            ->diffInDays(Carbon::now()) < $validDays;

        // Inside first login period, it does not matter that there is no password change or
        // if the user is an homologated user
        if ($userRegisteredInsideValidPeriod || $this->user->homologated) {
            return true;
        }

        // Outside of first login period, must have at least one password change.
        $lastPasswordChange = PasswordHistories::getLastPasswordOf($this->user);
        if (empty($lastPasswordChange)) {
            return false;
        }

        return Carbon::parse($lastPasswordChange->created_at)->diffInDays(Carbon::now()) < $validDays;
    }
}
