<?php

declare(strict_types=1);

namespace App\Tasks\Auth;

use App\Tasks\Task;
use App\Dtos\LoginCredentials;
use App\Repos\ValidateVBankCredentialsRepo;

class ValidateVBankCredentialsTask extends Task
{
    private LoginCredentials $credentials;

    public function __construct(private readonly ValidateVBankCredentialsRepo $validateVBankCredentialsRepo)
    {
    }

    public function withCredentials(LoginCredentials $credentials): self
    {
        $this->credentials = $credentials;

        return $this;
    }

    public function do(): array
    {
        $vBankCredentialsArray = $this->credentials->toVBankCredentialsArray();

        return $this->validateVBankCredentialsRepo
            ->prepare($vBankCredentialsArray)
            ->fetchRaw();
    }
}
