<?php

namespace App\Tasks\Auth;

use App\Dtos\ExternalToken;
use App\Dtos\GenerateExternalToken;
use App\Repos\GenerateTokenRepo;
use App\Tasks\Task;
use Exception;

class GenerateTokenForUserTask extends Task
{
    /**
     * Device for which the token will be generated.
     */
    private ?string $deviceId = null;

    /**
     * Owner of the token.
     */
    private ?string $nickname = null;

    /**
     * Client ID owner of the token.
     */
    private ?string $clientId = null;

    /**
     * DTO with all data needed.
     */
    private ?GenerateExternalToken $generateTokenData = null;

    public function __construct(private GenerateTokenRepo $repo)
    {
    }

    /**
     * Adds deviceId needed.
     *
     * @param string $deviceId
     * @return self
     */
    public function withDeviceId(string $deviceId): self
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * Adds nickname needed.
     *
     * @param string $nickname
     * @return self
     */
    public function withNickname(string $nickname): self
    {
        $this->nickname = $nickname;

        return $this;
    }

    /**
     * Adds client ID needed.
     *
     * @param string $clientId
     * @return self
     */
    public function withClientId(string $clientId): self
    {
        $this->clientId = $clientId;

        return $this;
    }

    /**
     * Adds all the data needed in one DTO.
     *
     * You may use this one method or the rest, not both.
     *
     * @param GenerateExternalToken $generateTokenData
     * @return self
     */
    public function withGenerateExternalToken(GenerateExternalToken $generateTokenData): self
    {
        $this->generateTokenData = $generateTokenData;

        return $this;
    }

    /**
     * Generates a new token by calling an external service.
     *
     * @return ExternalToken
     */
    public function do(): ?ExternalToken
    {
        $data = $this->generateTokenData;

        if (is_null($data)) {
            $data = GenerateExternalToken::make(
                $this->nickname,
                $this->deviceId,
                $this->clientId,
            );
        }

        try {
            return $this
                ->repo
                ->prepare($data)
                ->fetch();
        } catch (Exception $ex) {
            return null;
        }
    }
}
