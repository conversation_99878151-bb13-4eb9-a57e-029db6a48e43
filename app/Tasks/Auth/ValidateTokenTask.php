<?php

namespace App\Tasks\Auth;

use Exception;
use App\Tasks\Task;
use App\Dtos\ExternalAuthUser;
use App\Repos\ValidateTokenRepo;
use App\Dtos\ExternalAuthBiometry;
use Illuminate\Support\Facades\Log;

class ValidateTokenTask extends Task
{
    /**
     * Data needed to validate token.
     */
    private null|ExternalAuthBiometry|ExternalAuthUser $authData = null;

    public function __construct(private ValidateTokenRepo $repo)
    {
    }

    /**
     * Adds authData needed.
     *
     * @param ExternalAuthBiometry|ExternalAuthUser $authData
     * @return self
     */
    public function withAuthData(ExternalAuthBiometry|ExternalAuthUser $authData): self
    {
        $this->authData = $authData;

        return $this;
    }

    /**
     * Checks whether the token in $authData is valid by calling
     * an external service.
     *
     * @return bool
     */
    public function do(): ?bool
    {
        try {
            return $this
                ->repo
                ->prepare($this->authData)
                ->fetch();
        } catch (Exception $ex) {
            Log::error('Unexpected exception while validating token: ' . $ex);
            return null;
        }
    }
}
