<?php

declare(strict_types=1);

namespace App\Tasks\Auth;

use App\Tasks\Task;
use App\Enums\Code\Status;
use App\DTO\ExternalRequestResponse;
use App\Repos\SendVBankUsernameEmailRepo;
use App\Exceptions\Auth\ClientNotFoundException;

class SendVBankUsernameEmailTask extends Task
{
    protected string $customerId;

    protected string $deviceId;

    public function __construct(private readonly SendVBankUsernameEmailRepo $repo)
    {
        // 
    }

    public function withCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function withDeviceId(string $deviceId): self
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    public function do(): void
    {
        $data = [
            'customerId' => $this->customerId,
            'deviceId' => $this->deviceId
        ];

        $response = $this->repo->prepare($data)->fetch();

        $response = ExternalRequestResponse::fromExternalResponse($response);

        $this->validateResponse($response);
    }

    private function validateResponse(ExternalRequestResponse $response): void
    {
        $successfulResponseCodes = [Status::codeExternalOK->value, Status::codeExternalFind->value];

        if (in_array($response->responseStatusCode, $successfulResponseCodes) && $response->data) {
            return;
        }

        throw ClientNotFoundException::withCustomerId($this->customerId);
    }
}
