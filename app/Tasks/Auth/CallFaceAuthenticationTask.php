<?php

declare(strict_types=1);

namespace App\Tasks\Auth;

use App\DTO\FaceAuthRequestDTO;
use App\Repos\CallFaceAuthenticationRepo;
use App\Tasks\Task;

class CallFaceAuthenticationTask extends Task
{
    protected FaceAuthRequestDTO $faceAuthData;

    public function __construct(private readonly CallFaceAuthenticationRepo $repo)
    {
    }

    public function withFaceAuthData(FaceAuthRequestDTO $data): self
    {
        $this->faceAuthData = $data;

        return $this;
    }

    public function do(): array
    {
        $response = $this->repo
            ->prepare($this->faceAuthData)
            ->fetchRaw();

        return $response;
    }
}
