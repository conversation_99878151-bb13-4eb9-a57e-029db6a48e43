<?php

namespace App\Tasks\Auth;

use App\Repos\GetCustomerCardApplicationsRepo;
use App\Tasks\Task;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;

class GetCurrentCustomerIdTask extends Task
{
    /**
     * Auth manager.
     */
    private Guard $auth;

    /**
     * The DUI is needed if no auth user is found.
     */
    private ?string $dui = null;

    public function __construct(
        AuthManager $authManager,
        private GetCustomerCardApplicationsRepo $repo,
    ) {
        $this->auth = $authManager->guard('api');
    }

    public function withDui(?string $dui)
    {
        $this->dui = $dui;

        return $this;
    }

    /**
     * Tries to get the customer ID (external user ID) of the
     * current user, if any.
     *
     * @return null|int
     */
    public function do(): ?int
    {
        if ($this->auth->hasUser()) {
            return $this
                ->auth
                ->user()
                ->client_id;
        }

        if (!empty($this->dui)) {
            return $this
                ->repo
                ->prepare($this->dui)
                ->fetch()
                ->customer
                ->id;
        }

        return null;
    }
}
