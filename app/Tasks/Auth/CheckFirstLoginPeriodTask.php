<?php

namespace App\Tasks\Auth;

use App\Models\Client;
use App\Models\PasswordHistories;
use App\Services\Configurations as Config;
use App\Tasks\Task;
use Carbon\Carbon;

class CheckFirstLoginPeriodTask extends Task
{
    /**
     * Owner of the password.
     */
    private ?Client $user = null;

    public function __construct(private Config $config)
    {
    }

    public function withUser(Client $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Checks whether the first password of $this->user has been changed, if not
     * the user must change it.
     *
     * @return bool
     */
    public function do(): bool
    {
        if (!empty(PasswordHistories::getLastPasswordOf($this->user))) {
            return true;
        }

        $validHours = (int) $this->config->getConfigurations('PERIODO_VALIDEZ_PRIMER_LOGIN');
        $isValid = Carbon::parse($this->user->created_at)->diffInHours(Carbon::now()) < $validHours;

        return $isValid;
    }
}
