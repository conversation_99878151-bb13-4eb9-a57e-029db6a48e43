<?php

declare(strict_types=1);

namespace App\Tasks\Auth;

use App\DTO\ExternalRequestResponse;
use App\Exceptions\Auth\CouldNotCreateVBankUser;
use App\Repos\CreateVBankUserRepo;
use App\Tasks\Task;
use Symfony\Component\HttpFoundation\Response;

class CreateVBankUserTask extends Task
{
    protected string $customerId;

    protected string $deviceId;

    public function __construct(private readonly CreateVBankUserRepo $repo)
    {
        // 
    }

    public function withCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function withDeviceId(string $deviceId): self
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * @throws CouldNotCreateVBankUser
     */
    public function do(): string
    {
        $data = [
            'customerId' => $this->customerId,
            'deviceId' => $this->deviceId
        ];

        $response = $this->repo
            ->prepare($data)
            ->fetch();

        $response = ExternalRequestResponse::fromExternalResponse($response);

        $vBankUsername = data_get($response->data, 'Username');

        if (is_null($vBankUsername) && $response->responseStatusCode !== Response::HTTP_OK) {
            throw new CouldNotCreateVBankUser();
        }

        return $vBankUsername;
    }
}
