<?php

namespace App\Tasks\Auth;

use App\Models\Device;
use App\Services\Crypt\Rsa;
use App\Tasks\Task;

class CheckSignedChallengeTask extends Task
{
    /**
     * Device that claims the identity.
     */
    private ?Device $device = null;

    /**
     * Signed challenge.
     */
    private ?string $signedChallenge = null;

    public function __construct(private Rsa $rsa)
    {
    }

    public function withDevice(Device $device)
    {
        $this->device = $device;

        return $this;
    }

    public function withSignedChallenge(string $signedChallenge)
    {
        $this->signedChallenge = $signedChallenge;

        return $this;
    }

    /**
     * Checks whether $this->signedChallenge is signed with the private key
     * of $this->device and that it is the challenge of such.
     *
     * @return bool
     */
    public function do(): bool
    {
        return $this
            ->rsa
            ->verifyWith(
                $this->device->verification_challenge,
                $this->signedChallenge,
                $this->device->biometric_public_key,
            );
    }
}
