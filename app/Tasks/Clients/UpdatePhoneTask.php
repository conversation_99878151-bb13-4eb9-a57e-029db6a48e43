<?php

namespace App\Tasks\Clients;

use App\Models\Client;
use App\Tasks\Task;

class UpdatePhoneTask extends Task
{
    /**
     * User to update.
     */
    private ?Client $user;

    /**
     * New phone number.
     */
    private ?string $phoneNumber;

    public function withUser(Client $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function withPhoneNumber(string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function do(): null
    {
        $this->user->phone_number = $this->phoneNumber;

        $this->user->save();

        return null;
    }
}
