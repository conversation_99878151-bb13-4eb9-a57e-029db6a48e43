<?php

namespace App\Tasks\Clients;

use App\Dtos\ExternalToken;
use App\Dtos\RefreshToken;
use App\Events\UpdatedTokenForUserEvent;
use App\Models\Client;
use App\Repos\RefreshTokenRepo;
use App\Tasks\Task;

class RefreshTokenForUserTask extends Task
{
    /**
     * User to update.
     */
    private ?Client $user;

    /**
     * Device in which the token is being used.
     */
    private ?string $deviceId;

    /**
     * Refresh token of current user.
     */
    private ?string $refreshToken;

    public function __construct(private RefreshTokenRepo $repo)
    {
    }

    /**
     * Adds user needed.
     *
     * @param Client $user
     * @return self
     */
    public function withUser(Client $user): self
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Adds device ID needed.
     *
     * @param string $deviceId
     * @return self
     */
    public function withDeviceId(string $deviceId): self
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * Refreshes an external token for a registered user.
     *
     * @return ExternalToken
     */
    public function do(): ExternalToken
    {
        $token = $this
            ->repo
            ->prepare(RefreshToken::fromFlatArray([
                'refreshToken' => $this->user->baes_refresh_token,
                'nickname' => $this->user->nickname,
                'deviceId' => $this->deviceId,
                'customerId' => $this->user->client_id,
            ]))
            ->fetch();

        UpdatedTokenForUserEvent::dispatch($this->user, $token);

        return $token;
    }
}
