<?php

namespace App\Tasks\Clients;

use App\Models\Client;
use App\Tasks\Task;

class UpdateEmailTask extends Task
{
    /**
     * User to update.
     */
    private ?Client $user;

    /**
     * New email.
     */
    private ?string $email;

    public function withUser(Client $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function withEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function do(): null
    {
        $this->user->email = $this->email;

        $this->user->save();

        return null;
    }
}
