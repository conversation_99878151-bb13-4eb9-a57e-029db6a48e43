<?php

namespace App\Tasks\Clients;

use App\Dtos\ExternalToken;
use App\Models\Client;
use App\Tasks\Task;

class UpdateBaesTokenTask extends Task
{
    /**
     * User to update.
     */
    private ?Client $user;

    /**
     * New phone number.
     */
    private ?ExternalToken $token;

    public function withUser(Client $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function withToken(ExternalToken $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function do(): null
    {
        $this->user->baes_token = $this->token->token;
        $this->user->baes_refresh_token = $this->token->refreshToken;
        $this->user->save();

        return null;
    }
}
