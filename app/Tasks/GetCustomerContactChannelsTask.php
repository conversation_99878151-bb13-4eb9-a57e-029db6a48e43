<?php

namespace App\Tasks;

use App\Dtos\ContactChannels;
use App\Repos\GetCustomerContactChannelsRepo;

class GetCustomerContactChannelsTask extends Task
{
    /**
     * ID of customer to get.
     */
    private ?int $customerId = null;

    public function __construct(private GetCustomerContactChannelsRepo $repo)
    {
    }

    /**
     * ID of customer to get.
     *
     * @param int $customerId
     * @return self
     */
    public function withCustomerId(int $customerId)
    {
        $this->customerId = $customerId;

        return $this;
    }

    /**
     * Gets a customer info by calling an external service.
     *
     * @return ContactChannels
     */
    public function do(): ContactChannels
    {
        return $this
            ->repo
            ->prepare($this->customerId)
            ->fetch();
    }
}
