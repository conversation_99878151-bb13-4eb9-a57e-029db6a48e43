<?php

namespace App\Tasks;

use App\Events\UserDeletedEvent;
use App\Models\Client;
use App\Models\ClientSystem;
use App\Models\Device;
use App\Models\NicknameLog;
use App\Models\NotificationHistories;
use App\Models\PasswordHistories;
use App\Models\PerfilImages;
use Illuminate\Database\DatabaseManager;

class DeleteUserTask extends Task
{
    /**
     * Client to delete.
     */
    private ?Client $user = null;

    private bool $forceDelete = false;

    public function __construct(private DatabaseManager $db)
    {
    }

    /**
     * Client to delete.
     *
     * @param Client $user
     * @return self
     */
    public function withUser(Client $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Ignore the default soft delete process in model
     *
     * @return self
     */
    public function forceDelete()
    {
        $this->forceDelete = true;

        return $this;
    }

    /**
     * Deletes a user and some of its relations.
     *
     * @return void
     */
    public function do(): void
    {
        $this->db->transaction(function () {
            $id = $this->user->id;

            PerfilImages::where('client_id', $id)->forceDelete();
            ClientSystem::where('client_id', $id)->forceDelete();
            PasswordHistories::where('user_id', $id)->forceDelete();
            NicknameLog::where('client_id', $id)->delete();
            Device::where('client_id', $id)->forceDelete();
            NotificationHistories::where('client_id', $id)->forceDelete();
            
            if ($this->forceDelete) {
                $this->user->forceDelete(); // only for development
            }
            else {
                $this->user->unregisterClient()->save();
                $this->user->delete(); // soft-delete
            }
        });

        UserDeletedEvent::dispatch(
            $this->user->id,
            $this->user->client_id,
            $this->user->nickname,
            $this->user->creditcard_application_id,
        );
    }
}
