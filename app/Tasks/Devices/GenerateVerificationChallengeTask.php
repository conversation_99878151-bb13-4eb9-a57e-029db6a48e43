<?php

namespace App\Tasks\Devices;

use App\Dtos\VerificationChallenge;
use App\Exceptions\MissingPublicKeyException;
use App\Models\Device;
use App\Services\Crypt\Rsa;
use App\Tasks\Task;
use ArgumentCountError;
use Illuminate\Support\Str;

class GenerateVerificationChallengeTask extends Task
{
    /**
     * Device to be set as online.
     */
    private ?string $deviceId = null;

    /**
     * Device to be set as online.
     */
    private ?Device $device = null;

    public function __construct(private readonly Rsa $rsa)
    {
    }

    /**
     * Adds the device to modify.
     *
     * Either this or withDevice() should be called.
     *
     * @param string $deviceId
     * @return self
     */
    public function withId(string $deviceId): self
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * Adds the device to modify.
     *
     * Either this or withId() should be called.
     *
     * @param string $deviceId
     * @return self
     */
    public function withDevice(Device $device): self
    {
        $this->device = $device;

        return $this;
    }

    /**
     * Generates a new random string to be used as verification challenge on logins
     * for a device. Updates the device with it and returns the challenge.
     *
     * @return VerificationChallenge The base 64 encrypted verification challenge.
     */
    public function do(): VerificationChallenge
    {
        $this->validateArguments();

        $device = $this->device ?? Device::active()->latest()->firstWhere('device_id', $this->deviceId);
        $this->validateDevice($device);

        $challenge = Str::random(64);
        $device->verification_challenge = $challenge;
        $device->save();

        return VerificationChallenge::make(
            $this
                ->rsa
                ->encryptWith($challenge, $device->biometric_public_key)
        );
    }

    /**
     * Validates whether any of the props have been set.
     *
     * @return void
     * @throws ArgumentCountError When neither prop has been set.
     */
    private function validateArguments(): void
    {
        if (is_null($this->device) and is_null($this->deviceId)) {
            throw new ArgumentCountError('At least one of device or deviceId must be set');
        }

        if ($this->device) {
            $this->validateDevice($this->device);
        }
    }

    /**
     * Validates whether $device has a public key.
     *
     * @param Device $device
     * @return void
     * @throws MissingPublicKeyException
     */
    private function validateDevice(Device $device): void
    {
        if (empty($device->biometric_public_key)) {
            throw new MissingPublicKeyException();
        }
    }
}
