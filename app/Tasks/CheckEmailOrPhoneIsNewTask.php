<?php

namespace App\Tasks;

use App\Repos\ValidateContactRepo;

class CheckEmailOrPhoneIsNewTask extends Task
{
    /**
     * Email or phone number to check.
     */
    private ?string $emailOrPhone = '';

    public function __construct(private ValidateContactRepo $repo)
    {
    }

    /**
     * Email or phone number to check.
     *
     * @param string $emailOrPhone
     * @return self
     */
    public function withEmailOrPhone(string $emailOrPhone)
    {
        $this->emailOrPhone = $emailOrPhone;

        return $this;
    }

    /**
     * Checks whether $this->emailOrPhone is new or if it is
     * already linked to a credit card or a request for one.
     *
     * @return bool true on new.
     */
    public function do(): bool
    {
        return !$this
            ->repo
            ->contactExists($this->emailOrPhone);
    }
}
