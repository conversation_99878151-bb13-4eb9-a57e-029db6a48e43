<?php

namespace App\Tasks;

use App\Dtos\OtpValidation;
use App\Repos\ValidateOtpRepo;

class ValidateOtpTask extends Task
{
    /**
     * Data needed to validate OTP code.
     */
    private ?OtpValidation $otpData = null;

    public function __construct(private ValidateOtpRepo $repo)
    {
    }

    /**
     * Adds OTP data needed.
     *
     * @param OtpValidation $otpData
     * @return self
     */
    public function withOtpData(OtpValidation $otpData): self
    {
        $this->otpData = $otpData;

        return $this;
    }

    /**
     * Checks whether the OTP code in $otpData is valid by calling
     * an external service.
     *
     * @return bool
     */
    public function do(): bool
    {
        return $this
            ->repo
            ->isValidOtp($this->otpData);
    }
}
