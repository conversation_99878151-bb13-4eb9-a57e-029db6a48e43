<?php

namespace App\Tasks;

use App\Models\Client;
use App\Services\AuthNotifyService;
use Illuminate\Log\LogManager as Log;

class NotifyUserOfTooManyAttemptsTask extends Task
{
    /**
     * Nickname of user to notify.
     */
    private ?string $nickname = null;

    public function __construct(
        private AuthNotifyService $notifier,
        private Log $log,
    ) {
    }

    public function withNickname(string $nickname)
    {
        $this->nickname = $nickname;

        return $this;
    }

    /**
     * Notify the user, if any.
     *
     * @return void
     */
    public function do(): void
    {
        $user = Client::firstWhere('nickname', $this->nickname);

        if (is_null($user)) {
            $this
                ->log
                ->info("It's not possible to notify an inexistent user of too many login attempts", ['nickname' => $this->nickname]);
        } else {
            $this->notifier->blockTime($user);
        }
    }
}
