<?php

namespace App\Tasks;

use App\DTO\CreateClientDTO;
use App\Dtos\Customer;
use App\Models\Client;
use App\Models\Systems;
use App\Dtos\ContactChannels;
use App\Dtos\LoginCredentials;
use App\Events\UserRegisteredEvent;
use App\Services\PasswordGenerator;
use App\Services\UsernameGenerator;
use Illuminate\Support\Facades\DB;

class CreateUserTask extends Task
{
    private ?Customer $customer = null;

    private ?string $nicknameVBank = null;

    private ?string $username = null;

    private ?string $password = null;

    private ?ContactChannels $contactChannels = null;

    private int $creditcardApplicationId = 0;

    public function __construct(
        private UsernameGenerator $usernameGenerator,
        private PasswordGenerator $passwordGenerator,
    ) {}

    /**
     * Customer data of the new user.
     *
     * @param Customer $customer
     * @return self
     */
    public function withCustomer(Customer $customer)
    {
        $this->customer = $customer;

        return $this;
    }

    public function withVBankNickname(string $nickname): self
    {
        $this->nicknameVBank = $nickname;

        return $this;
    }

    /**
     * Contact channels of the new user.
     *
     * @param ContactChannels $contactChannels
     * @return self
     */
    public function withContactChannels(ContactChannels $contactChannels)
    {
        $this->contactChannels = $contactChannels;

        return $this;
    }

    /**
     * ID of the application.
     *
     * @param int $creditcardApplicationId
     * @return self
     */
    public function withCreditcardApplicationId(int $creditcardApplicationId)
    {
        $this->creditcardApplicationId = $creditcardApplicationId;

        return $this;
    }

    /**
     * Creates a new user and some of its relations.
     *
     * @return Client
     */
    public function do(): Client
    {
        DB::beginTransaction();

        $isSetNicknameVbank = isset($this->nicknameVBank);

        $this->password = $this->passwordGenerator->generate();

        if (!$isSetNicknameVbank) {
            $this->username = $this->usernameGenerator->generate(
                $this->customer->firstName,
                $this->customer->firstSurname
            );
        }

        $userData = new CreateClientDTO(
            customer: $this->customer,
            creditCardApplicationId: $this->creditcardApplicationId,
            contactChannels: $this->contactChannels,
            nickname: $this->username,
            nicknameVbank: $this->nicknameVBank,
            password: $this->password,
            homologated: $isSetNicknameVbank,
        );

        $user = Client::create($userData->toArray());
        $user->systems()->attach(Systems::default()->id);
        $user->profileImages()->insert($this->getDefaultProfileImages($user->id));

        DB::commit();

        if (!$user->homologated) {
            UserRegisteredEvent::dispatch($user, LoginCredentials::make($this->username, $this->password));
        }

        return $user;
    }

    private function getDefaultProfileImages(int $clientId): array
    {
        return [
            [
                'client_id' => $clientId,
                'image' => 'imagePerfilDefault.jpg',
                'type' => 'PerfilImage',
            ],
            [
                'client_id' => $clientId,
                'image' => 'imageBackgroundDefault.png',
                'type' => 'BackgroundImage',
            ]
        ];
    }
}
