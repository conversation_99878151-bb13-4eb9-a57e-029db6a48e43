<?php

namespace App\Tasks;

use App\Models\Client;
use App\Models\Device;
use ArgumentCountError;

class OfflineDevicesTask extends Task
{
    /**
     * Device to be set as online.
     */
    private ?string $deviceId = null;

    /**
     * Device to be set as online.
     */
    private ?Device $device = null;

    /**
     * Adds the device to modify.
     *
     * Either this or withDevice() should be called.
     *
     * @param string $deviceId
     * @return self
     */
    public function withId(string $deviceId): self
    {
        $this->deviceId = $deviceId;

        return $this;
    }

    /**
     * Adds the device to modify.
     *
     * Either this or withId() should be called.
     *
     * @param string $deviceId
     * @return self
     */
    public function withDevice(Device $device): self
    {
        $this->device = $device;

        return $this;
    }

    /**
     * Sets as offline every device of the owner except for the
     * one passed, which is set as online.
     *
     * @return \App\Models\Client
     */
    public function do(): Client
    {
        $this->validateArguments();

        $device = $this->device ?? Device::firstWhere('device_id', $this->deviceId);

        $device->update([
            'online' => Device::ONLINE,
            'active' => boolval(Device::ONLINE)
        ]);

        $this->setDevicesAsOffline($device);

        return $device->client;
    }

    /**
     * Validates whether any of the props have been set.
     *
     * @return void
     * @throws ArgumentCountError When neither prop has been set.
     */
    private function validateArguments(): void
    {
        if (is_null($this->device) && is_null($this->deviceId)) {
            throw new ArgumentCountError('At least one of device or deviceId must be set');
        }
    }

    private function setDevicesAsOffline(Device $activeDevice): void
    {
        $client = $activeDevice->client;

        $query = Device::query();

        $query
            // Disable devices where the `device_id` is equal to the `device_id` of `$activeDevice`.
            ->where(function ($query) use ($activeDevice) {
                $query
                    ->where('id', '<>', $activeDevice->id)
                    ->where('device_id', $activeDevice->device_id)
                    ->update(['online' => Device::OFFLINE, 'active' => boolval(Device::OFFLINE)]);
            })
            // Set the rest of customer devices as offline,
            ->orWhere(function ($query) use ($client, $activeDevice) {
                $query
                    ->where('client_id', $client->id)
                    ->where('device_id', '<>', $activeDevice->device_id)
                    ->update(['online' => Device::OFFLINE]);
            });

        $devices = $query->get();
    }
}
