<?php

declare(strict_types=1);

namespace App\Tasks;

use App\DTO\VBankAccountDTO;
use App\Repos\ValidateVBankAccountRepo;

class ValidateVBankAccountTask extends Task
{
    protected ?string $customerId = null;

    protected ?string $customerDui = null;

    public function __construct(private readonly ValidateVBankAccountRepo $validateVBankAccountRepo)
    {
    }

    public function withCustomerId(string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function withcustomerDui(string $customerDui): self
    {
        $this->customerDui = $customerDui;

        return $this;
    }

    public function do(): ?VBankAccountDTO
    {
        $params = [
            'CustomerId' => $this->customerId,
            'Dui' => $this->customerDui,
        ];

        $response = $this->validateVBankAccountRepo
            ->prepare($params)
            ->fetch();

        return $response;
    }
}
