<?php

declare(strict_types=1);

namespace App\Contracts;

use DomainException;
use Illuminate\Http\JsonResponse;

abstract class BaseDomainException extends DomainException
{
    protected const ID = "domain_error";

    protected $message;

    protected $exceptionCode;

    /**
     * Renders the exception into an HTTP response.
     */
    abstract public function render(): JsonResponse;

    public static function getErrorCode(): mixed
    {
        return static::ID;
    }
}
