<?php

namespace App\Contracts;

use App\Dtos\Dto;
use Illuminate\Support\Collection;

interface DtoFactory
{
    /**
     * Creates a new DTO with dummy data.
     *
     * @return Dto
     */
    public function create(): Dto;

    /**
     * Creates $n DTOs.
     *
     * @param int $n
     * @return Collection
     */
    public function createMany(int $n): Collection;

    /**
     * Creates DTOs with $values. Any missing property is filled
     * with dummy data from $this->create().
     *
     * Passing and array of arrays returns that many DTOs.
     *
     * @param array $values
     * @return Collection
     */
    public function createWith(array $values): Dto|Collection;
}
