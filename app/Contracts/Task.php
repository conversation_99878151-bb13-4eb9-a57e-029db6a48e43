<?php

namespace App\Contracts;

/**
 * Describes a simple action of the domain.
 */
interface Task
{
    /**
     * Executes the action and returns the result.
     *
     * @return mixed|void.
     */
    public function do();

    /**
     * Executes the action and feeds the result to $class.
     *
     * @param string $class
     * @return mixed An instance of $class.
     */
    public function doInto(string $class): mixed;
}
