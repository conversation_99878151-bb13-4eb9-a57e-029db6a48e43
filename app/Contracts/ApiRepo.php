<?php

namespace App\Contracts;

use App\Dtos\Dto;

interface ApiRepo
{
    /**
     * Prepares the call with $values.
     *
     * @param mixed $values DTO or array.
     * @return self
     */
    public function prepare($values): self;

    /**
     * Calls the source for the info and may return data.
     *
     * @return mixed
     */
    public function fetch();

    /**
     * Calls the source for the info and returns based on successful
     * response.
     *
     * @return bool
     */
    public function ping(): bool;
}
