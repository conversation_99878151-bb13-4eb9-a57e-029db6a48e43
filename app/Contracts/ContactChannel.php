<?php

namespace App\Contracts;

/**
 * A way to contact a customer.
 */
interface ContactChannel
{
    /**
     * Returns the value of the contact medium.
     *
     * @return string
     */
    public function get(): string;

    /**
     * Returns the type of the contact channel.
     *
     * @return string
     */
    public static function type(): string;

    /**
     * Returns whether the type of this channel matches $type.
     *
     * @return bool
     */
    public function typeIs(string $type): bool;
}
