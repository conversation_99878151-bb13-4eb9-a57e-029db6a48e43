<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property int $option_id
 * @property int $customer_id
 * @property string $other_answers
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $deleted_at
 * @property Option $option
 */
class Answer extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'answers';

    protected $fillable = [
        'option_id',
        'customer_id',
        'other_answers',
    ];

    /** @return BelongsTo<Option> */
    public function option(): BelongsTo
    {
        return $this->belongsTo(Option::class);
    }
}
