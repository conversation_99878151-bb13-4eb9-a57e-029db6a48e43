<?php

declare(strict_types=1);

namespace App\Models;

use App\Traits\LacksUpdatedTimestamp;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property ?string $endpoint_name
 * @property string $status_response
 * @property ?string $system_code
 * @property ?string $result
 * @property ?string $ip
 * @property ?int $response_time_ms
 * @property ?CarbonInterface $created_at
 */
class EndpointsLog extends Model
{
    use HasFactory;
    use LacksUpdatedTimestamp;

    /**
     * The name of the "updated at" column.
     *
     * @var string|null
     */
    public const UPDATED_AT = null;

    protected $table = "endpoints_log";
}
