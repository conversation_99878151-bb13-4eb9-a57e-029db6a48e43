<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $emitter
 * @property CarbonInterface $block_time_start
 * @property CarbonInterface $block_time_end
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 */
class OTPBlackList extends Model
{
    use HasFactory;

    protected $table = 'otp_black_list';

    protected $timestamp = false;

    protected $fillable = [
        'emitter',
        'block_time_start',
        'block_time_end',
    ];
}
