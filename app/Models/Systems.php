<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Database\Factories\SystemFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 * @property string $system_code
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Systems extends Model
{
    use HasFactory;

    use SoftDeletes;

    public const DEFAULT = 'APP';

    protected $fillable = ['name', 'system_code'];

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return SystemFactory::new();
    }

    /**
     * Returns the default system.
     *
     * @return self
     */
    public static function default(): self
    {
        return self::firstWhere('system_code', self::DEFAULT);
    }
}
