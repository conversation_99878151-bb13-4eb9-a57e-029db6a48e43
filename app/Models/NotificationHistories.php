<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $type
 * @property string $message
 * @property string $message
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class ********************* extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = "notifications_histories";

    protected $fillable = ['client_id', 'type', 'message'];
}
