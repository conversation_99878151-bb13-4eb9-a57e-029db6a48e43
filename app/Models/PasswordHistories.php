<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Database\Factories\PasswordLogFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $password
 * @property int $user_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class PasswordHistories extends Model
{
    use HasFactory;

    use SoftDeletes;

    protected $table = 'password__histories';

    protected $fillable = [
        'user_id',
        'password',
        'created_at'
    ];

    /**
     * Returns the most recent password change of $user.
     */
    public static function getLastPasswordOf(Client $user): ?self
    {
        return self::where('user_id', $user->id)
            ->latest()
            ->first();
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return PasswordLogFactory::new();
    }
}
