<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @property string $sharedkey
 * @property int $attempts
 * @property ?string $identifier
 * @property ?string $type
 * @property ?string $value
 * @property ?string $otpcode
 * @property ?int $verified
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 */
class OtpLogs extends Model
{
    use HasFactory;

    protected $table = "otp_log";

    public const VERIFIED     = 1;
    public const NOT_VERIFIED = 0;

    public const SESSION = 'SESS';
    public const APIKEY  = 'APIK';
}
