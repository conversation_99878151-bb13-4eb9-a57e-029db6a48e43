<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $name
 * @property string $url
 * @property ?bool $active
 * @property ?CarbonInterface $last_run
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class ExternalServicesMonitor extends Model
{
    use HasFactory;

    protected $casts = [
        'last_run' => 'datetime',
    ];

    protected $table = "external_services_monitor";

    protected $fillable = ['name', 'url', 'active', 'last_run'];
}
