<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $ip
 * @property string $user_agent
 * @property int $user_id
 * @property CarbonInterface $login_date_time
 * @property ?CarbonInterface $logout_date_time
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property User $user
 */
class LogAccess extends Model
{
    use HasFactory;

    protected $table = "Log_access";

    protected $fillable = ['login_date_time', 'logout_date_time', 'user_id', 'ip'];

    protected $casts = [
        'login_date_time'  => 'datetime',
        'logout_date_time' => 'datetime',
    ];

    /** @return BelongsTo<User> */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
