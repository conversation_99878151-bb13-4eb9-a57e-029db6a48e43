<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use App\Models\RegisterClientsApp;
use App\Models\NotificationHistories;
use Illuminate\Notifications\Notifiable;
use App\Traits\Client\HasClientAttributes;
use App\Traits\Client\HasClientStatusChecks;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property string $mode
 * @property string $client_id
 * @property string $dui
 * @property string $password
 * @property string $nickname
 * @property string $nickname_vbank
 * @property string $baes_token
 * @property string $baes_refresh_token
 * @property int $password_login_attempts
 * @property int $status
 * @property ?string $first_name
 * @property ?string $second_name
 * @property ?string $first_surname
 * @property ?string $second_surname
 * @property ?string $married_surname
 * @property ?string $nit
 * @property ?string $email
 * @property ?string $phone_number
 * @property ?string $group
 * @property ?string $credolab_id
 * @property ?int $send_notification
 * @property ?int $creditcard_application_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 * @property bool $homologated
 * @property bool $foreigner Determines wheter a client is a foreigner or not.
 *
 * @property Collection<Device> $devices
 * @property Collection<PasswordHistories> $passwords
 * @property Collection<PerfilImages> $profileImages
 * @property Collection<Systems> $systems
 *
 * @property-read string $full_name The client's full name, derived from the getFullNameAttribute()
 *  method inside HasClientAttributes trait.
 * @property-read string $short_name The client's short name, derived from the getShortNameAttribute()
 *  method inside HasClientAttributes trait.
 */
class Client extends Authenticatable
{
    use HasFactory;
    use Notifiable;
    use SoftDeletes;
    use HasClientAttributes;
    use HasClientStatusChecks;

    public const DELETED = -1;
    public const DISABLE = 0;
    public const ENABLE = 1;
    public const BLOCKOTP = 2;
    public const BLOCK_BY_FACE_AUTH = 3;

    protected $fillable = [
        'email',
        'password',
        'mode',
        'nickname',
        'nickname_vbank',
        'client_id',
        'first_name',
        'second_name',
        'first_surname',
        'second_surname',
        'married_surname',
        'dui',
        'nit',
        'phone_number',
        'send_notification',
        'creditcard_application_id',
        'group',
        'homologated',
        'foreigner',
    ];

    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts  = [
        'password_login_attempts' => 'integer',
        'foreigner' => 'bool'
    ];

    /**
     * @return HasMany<PerfilImages>
     */
    public function profileImages(): HasMany
    {
        return $this->hasMany(PerfilImages::class);
    }

    /**
     * @return HasMany<Device>
     */
    public function devices(): HasMany
    {
        return $this->hasMany(Device::class, 'client_id');
    }

    /**
     * @return BelongsToMany<Systems>
     */
    public function systems(): BelongsToMany
    {
        return $this->belongsToMany(Systems::class, 'clients_systems', 'client_id', 'system_id');
    }

    /** @var HasMany<RegisterClientsApp> */
    public function registerClientsApp(): HasMany
    {
        return $this->hasMany(RegisterClientsApp::class);
    }

    /**
     * Returns the past passwords of the client.
     *
     * @return HasMany<PasswordHistories>
     */
    public function passwords()
    {
        return $this->hasMany(PasswordHistories::class, 'user_id');
    }

    /** @return HasMany<NotificationHistories> */
    public function notificationHistories(): HasMany
    {
        return $this->hasMany(NotificationHistories::class);
    }

    public function unregisterClient(): self
    {
        $this->first_name = null;
        $this->second_name = null;
        $this->first_surname = null;
        $this->second_surname = null;
        $this->married_surname = null;
        $this->nit = null;
        $this->email = null;
        $this->phone_number = null;
        $this->credolab_id = null;
        $this->send_notification = null;
        $this->group = null;
        $this->creditcard_application_id = null;
        $this->baes_token = null;
        $this->baes_refresh_token = null;
        $this->password_login_attempts = 0;

        $this->status = self::DELETED;
        return $this;
    }

    /**
     * Returns a user that matches $token, if any.
     *
     * @param string $token
     * @return self|null
     */
    public static function byBaesToken(string $token): ?self
    {
        return self::firstWhere('baes_token', $token);
    }

    /**
     * Returns a user that matches $token, if any.
     *
     * @param string $token
     * @return self|null
     */
    public static function byBaesRefreshToken(string $token): ?self
    {
        return self::firstWhere('baes_refresh_token', $token);
    }

    public function disable(): self
    {
        $this->status = self::DISABLE;

        return $this;
    }
}
