<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $title
 * @property string $content
 * @property bool $status
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property Collection<NotificationVariables> $notificationVariables
 */
class NotificationsTemplates extends Model
{
    use HasFactory;

    /** @return HasMany */
    public function notificationVariables(): HasMany
    {
        return $this->hasmany(NotificationVariables::class, 'notifications_templates_id', 'id');
    }
}
