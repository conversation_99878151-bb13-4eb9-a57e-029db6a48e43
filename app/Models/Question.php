<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $type
 * @property string $question
 * @property int $survey_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Question extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'question';

    public const QUALITATIVE  = 'CTL';

    public const QUANTITATIVE = 'CNT';

    public static $types = [
        self::QUALITATIVE  => 'Cualitativas',
        self::QUANTITATIVE => 'Cuantitativas',
    ];

    protected $fillable = [
        'survey_id',
        'type',
        'question',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'survey_id' => 'integer',
    ];

    /** @return BelongsTo<Survey> */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class);
    }

    /** @return HasMany<Option> */
    public function options(): HasMany
    {
        return $this->hasMany(Option::class);
    }

    public function isQuantitative(): bool
    {
        return $this->type == Question::QUANTITATIVE;
    }

    public function isQualitative(): bool
    {
        return $this->type == Question::QUALITATIVE;
    }
}
