<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $client_id
 * @property int $system_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class ClientSystem extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'clients_systems';

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'system_id',
    ];

    /** @return HasOne<Client> */
    public function client(): HasOne
    {
        return $this->hasOne(Client::class, 'client_id');
    }

    /** @return HasOne<Systems> */
    public function system(): HasOne
    {
        return $this->hasOne(Systems::class, 'system_id');
    }
}
