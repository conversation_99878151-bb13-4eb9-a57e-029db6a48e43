<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $data
 * @property string $extension
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Storehouse extends Model
{
    protected $table = 'storehouses';

    protected $fillable = [
        'data',
        'extension',
    ];
}
