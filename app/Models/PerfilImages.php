<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $image
 * @property string $type
 * @property int $client_id
 * @property int $storehouse_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class PerfilImages extends Model
{
    use HasFactory;

    use SoftDeletes;

    protected $fillable = [
        'client_id',
        'image',
        'type',
        "storehouse_id"
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
        'storehouse_id'
    ];

    public function getImageAttribute(string $value): string
    {
        if (!app()->environment('testing')) {
            $isApi = app()->request->is('api/*');

            if (!is_null($value) && $isApi && !is_null($this->storehouse_id) && $this->storehouse_id !== 0) {
                return route('api.image', ['id' => $this->storehouse_id]);
            } elseif (!is_null($value) && $isApi) {
                return url("/storage/$value");
            }
        }

        return $value;
    }
}
