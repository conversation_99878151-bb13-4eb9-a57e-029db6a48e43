<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $value
 * @property ?string $type
 * @property int $question_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 * @property Question $question
 * @property Collection<Answer> $answers
 */
class Option extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'options';

    public const OPEN  = 'OPTION_OPEN';
    public const CLOSE = 'OPTION_CLOSED';

    public static $optionsTypes = [
        self::OPEN  => 'Abierta',
        self::CLOSE => 'Cerrada',
    ];

    protected $fillable = [
        'question_id',
        'value',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'question_id' => 'integer',
    ];

    /** @return BelongsTo<Question> */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    /** @return HasMany<Answer> */
    public function answers(): HasMany
    {
        return $this->hasMany(Answer::class, 'option_id', 'id');
    }
}
