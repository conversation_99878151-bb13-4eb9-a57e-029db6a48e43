<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $title
 * @property string $description
 * @property string $example
 * @property string $icon
 * @property string $order
 * @property string $longcode
 * @property bool $main
 * @property ?int $storehouse_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Benefit extends Model
{
    use HasFactory;

    public $guarded = ['id'];

    /** @var HasOne<Storehouse> */
    public function storehouse(): HasOne
    {
        return $this->hasOne(Storehouse::class, 'id', 'storehouse_id');
    }

    /**
     * Returns the icon as a viewable URL, if any.
     *
     * @return null|string
     */
    public function getIconAsUrl(): ?string
    {
        if (empty($this->icon) || empty($this->storehouse_id)) {
            return null;
        }

        return route('api.image', ['id' => $this->storehouse_id]);
    }
}
