<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use function strtr;

/**
 * @property string $template_code
 * @property string $subject
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 */
class SubjectEmail extends Model
{
    use HasFactory;

    protected $table = 'subject_emails';

    protected $fillable = [
        'template_code',
        'subject',
    ];

    public const FIRST_NAME     = '[PrimerNombre]';
    public const SECOND_NAME    = '[SegundoNombre]';
    public const FIRST_SURNAME  = '[PrimerApellido]';
    public const SECOND_SURNAME = '[SegundoApellido]';
    public const FULLNAME       = '[NombreCompleto]';

    /**
     * Define value and labels options to display variables
     *
     * @var array
     */
    public $options = [
        self::FIRST_NAME     => 'Primer Nombre',
        self::SECOND_NAME    => 'Segundo Nombre',
        self::FIRST_SURNAME  => 'Primer Apellido',
        self::SECOND_SURNAME => 'Segundo Apellido',
        self::FULLNAME       => 'Nombre Completo',
    ];

    /**
     * Parse the subject template with variables with the underlaying client info
     */
    public function parseSubjectTemplate(Client $client): string
    {
        $replace = [
            self::FIRST_NAME     => $client->first_name,
            self::SECOND_NAME    => $client->second_name,
            self::FIRST_SURNAME  => $client->first_surname,
            self::SECOND_SURNAME => $client->second_surname,
            self::FULLNAME       => $client->full_name,
        ];
        $str     = $this->subject ? $this->subject : "";
        return strtr($str, $replace);
    }
}
