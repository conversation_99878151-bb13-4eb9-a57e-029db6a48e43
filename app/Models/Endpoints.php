<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Database\Factories\EndpointFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $name
 * @property string $uri
 * @property string $description
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Endpoints extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['name', 'description', 'uri'];

    public function scopeOfKey(Builder $query, string $url, string $apikey): void
    {
        $query
            ->join('api_keys_endpoints', 'api_keys_endpoints.endpoints_id', '=', 'endpoints.id')
            ->join('api_keys', 'api_keys.id', '=', 'api_keys_endpoints.api_keys_id')
            ->where('endpoints.uri', '=', $url)
            ->where('api_keys.apikey', '=', $apikey);
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return EndpointFactory::new();
    }
}
