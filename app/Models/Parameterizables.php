<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $name
 * @property string $code
 * @property string $parameter_type
 * @property string $parameter
 * @property string $group
 * @property ?int $storehouse_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Parameterizables extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'parameter_type',
        'parameter',
        'group'
    ];

    public function getParameterAttribute(string $value): string
    {
        $isApi        = app()->request->is('api/*');
        $isStorehouse = !is_null($this->storehouse_id) && $this->storehouse_id !== 0;

        if (!is_null($value) && $isApi && $isStorehouse && $this->parameter_type === "1") {
            return route('api.image', ['id' => $this->storehouse_id]);
        } elseif ($this->storehouse_id === null && $this->parameter_type === "1") {
            return "http://172.27.6.49/storage/{$value}";
        }
        return $value;
    }
}
