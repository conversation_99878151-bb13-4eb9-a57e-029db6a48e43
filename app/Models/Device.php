<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $device_name
 * @property string $firebase_token
 * @property bool $active
 * @property int $client_id
 * @property ?int $device_id
 * @property ?int $online
 * @property ?string $ultimate_longitud
 * @property ?string $ultimate_latitud
 * @property ?string $biometric_public_key
 * @property ?string $verification_challenge
 * @property ?bool $huawei
 * @property ?CarbonInterface $latest_activity
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 * @property Client $client
 */
class Device extends Model
{
    use HasFactory;
    use SoftDeletes;

    public const ONLINE = 1;
    public const OFFLINE = 0;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'active',
        'device_id',
        'device_name',
        'firebase_token',
        'ultimate_longitud',
        'ultimate_latitud',
        'online',
        'biometric_public_key',
        'verification_challenge',
        'huawei',
        'latest_activity',
        'client_id',
    ];

    /**
     * Returns the owner of the device.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }
}
