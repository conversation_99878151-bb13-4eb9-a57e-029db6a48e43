<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Database\Factories\ApiKeyFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property string $name
 * @property ?string $apikey
 * @property bool $syncup
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class ApiKeys extends Model
{
    use HasFactory;
    use SoftDeletes;

    public const SYNC_UP       = 1;
    public const NO_SYNC_UP    = 0;
    public const MAINTENANCE   = 1;
    public const NO_MAINTENANCE = 0;

    protected $fillable = ['name', 'apikey'];

    protected $table = "api_keys";

    /** @return BelongsToMany<Endpoints> */
    public function endpoints(): BelongsToMany
    {
        return $this->belongsToMany(Endpoints::class);
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return ApiKeyFactory::new();
    }
}
