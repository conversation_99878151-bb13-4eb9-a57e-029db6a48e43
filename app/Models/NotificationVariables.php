<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $notifications_templates_id
 * @property string $variable
 * @property ?string $type
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class NotificationVariables extends Model
{
    use HasFactory;

    public const DEEP_LINK  = 'DeepLink';
    public const MANAGEMENT = 'MANAGEMENT';
    public const CLAIM      = 'CLAIM';
    public const CARD       = 'CARD';
    public const REQUEST    = 'REQUEST';

    /**
     * Values that are goin to be used in register clasification in any model instance
     *
     * @var array
     */
    public static $options = [self::DEEP_LINK => 'Deep Link'];

    /**
     * Values that are goin to be used to establish the deeplink parameter to send notifications
     *
     * @var array
     */
    public static $paramsDeepLink = [
        self::MANAGEMENT => 'Gestión',
        self::CLAIM      => 'Reclamo',
        self::CARD       => 'Tarjeta',
        self::REQUEST    => 'Solicitud',
    ];
}
