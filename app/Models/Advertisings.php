<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property string $name
 * @property string $body
 * @property string $url
 * @property string $image
 * @property bool $active
 * @property bool $active_url
 * @property ?string $color
 * @property ?string $group
 * @property ?int $storehouse_id
 * @property ?CarbonInterface $start_date
 * @property ?CarbonInterface $end_date
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $deleted_at
 */
class Advertisings extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['name', 'body', 'color', 'url', 'image', 'active', 'group'];

    public function getImageAttribute(string $value): string
    {
        $isApi = app()->request->is('api/*');
        if (!is_null($value) && $isApi && !is_null($this->storehouse_id) && $this->storehouse_id !== 0) {
            return route('api.image', ['id' => $this->storehouse_id]);
        }
        return $value;
    }
}
