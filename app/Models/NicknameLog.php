<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property ?string $key
 * @property ?string $faceauthentication
 * @property ?int $client_id
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class NicknameLog extends Model
{
    use HasFactory;

    protected $table = "nickname_log";

    protected $fillable = [
        'key',
        'client_id',
        'faceauthentication'
    ];
}
