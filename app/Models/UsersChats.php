<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @codeCoverageIgnore
 * @property ?string $dui
 * @property ?string $name
 * @property ?string $lastname
 * @property ?string $email
 * @property ?string $phone
 * @property ?int $client_app_id
 * @property ?int $chat_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 */
class UsersChats extends Model
{
    use HasFactory;

    protected $table = 'userschats';
}
