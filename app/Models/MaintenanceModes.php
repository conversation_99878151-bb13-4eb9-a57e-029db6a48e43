<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Database\Factories\MaintenancePeriodFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $reason
 * @property string $client_message
 * @property int $user_id
 * @property int $previous_block
 * @property CarbonInterface $date_time_maintenance_start
 * @property CarbonInterface $date_time_maintenance_end
 * @property ?int $user_approval_id
 * @property ?string $status
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class MaintenanceModes extends Model
{
    use HasFactory;
    use SoftDeletes;

    public const ACTIVE = 'Activo';

    public const INACTIVE = 'Desactivado';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'maintenance_modes';

    protected $fillable = [
        'date_time_maintenance_start',
        'date_time_maintenance_end',
        'reason',
        'cliente_message',
        'user_id',
        'user_approval_id',
        'previous_block',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts  = [
        'date_time_maintenance_start' => 'datetime',
        'date_time_maintenance_end' => 'datetime',
    ];

    /**
     * Scope a query to only include active periods.
     *
     * @param Builder $query
     * @return void
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('status', self::ACTIVE);
    }

    /**
     * Returns a period if any currently active.
     *
     * @return null|self
     */
    public static function getCurrentActivePeriod(): ?self
    {
        $now = now();

        return self::active()
            ->where('date_time_maintenance_start', '<=', $now)
            ->where('date_time_maintenance_end', '>=', $now)
            ->latest()
            ->first();
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return MaintenancePeriodFactory::new();
    }
}
