<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $service_name
 * @property string $exposed_service_url
 * @property string $third_parties_url
 * @property string $method
 * @property bool $has_token
 * @property ?string $description
 * @property ?string $group
 * @property ?string $token
 * @property ?string $security
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class ServicesRouter extends Model
{
    use HasFactory;

    protected $table = "services_router";

    protected $fillable = [
        'service_name',
        'description',
        'exposed_service_url',
        'third_parties_url',
        'has_token',
        'token',
        'group',
        'method',
        'security',
    ];
}
