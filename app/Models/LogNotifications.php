<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $token
 * @property string $huawei
 * @property string $status
 * @property int $ccapplication_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 */
class LogNotifications extends Model
{
    use HasFactory;

    protected $table = 'log_notifications';

    protected $fillable = [
        "ccapplication_id",
        "token",
        "huawei",
        "status",
    ];
}
