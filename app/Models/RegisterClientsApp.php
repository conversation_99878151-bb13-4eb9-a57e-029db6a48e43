<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\CarbonInterface;
use App\Traits\LacksUpdatedTimestamp;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $status_response
 * @property ?string $endpoint_name
 * @property ?string $result
 * @property ?string $ip
 * @property ?string $device_id
 * @property ?int $response_time_ms
 * @property ?int $client_id
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $delete_at
 */
class RegisterClientsApp extends Model
{
    use HasFactory;
    use LacksUpdatedTimestamp;

    /**
     * The name of the "updated at" column.
     *
     * @var string|null
     */
    public const UPDATED_AT = null;

    protected $table = "register_clients_app";

    /** @return BelongsTo<Client> */
    public function clients(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }
}
