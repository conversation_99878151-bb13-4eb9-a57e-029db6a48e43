<?php

namespace App\Models;

use App\Helpers\Box;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $name
 * @property string $description
 * @property ?CarbonInterface $created_at
 * @property ?CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Survey extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'survey';

    protected $fillable = [
        'name',
        'description',
    ];

    /** @var HasMany<Question> */
    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }

    /** @var HasManyThrough<Option> */
    public function options(): HasManyThrough
    {
        return $this->hasManyThrough(Option::class, Question::class);
    }

    public static function findByNameOrFail(?string $name): self
    {
        return Box::put($name)
            ->peek(fn($name) => self::firstWhere('name', $name))
            ->andOpenAndThrow(
                (new ModelNotFoundException())->setModel(self::class)
            );
    }
}
