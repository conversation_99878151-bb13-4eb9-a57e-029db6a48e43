<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Configurations extends Model
{
    protected $primaryKey = 'key';
    protected $table      = 'nova_settings';
    public $incrementing  = false;
    public $timestamps    = false;
    public $fillable      = ['key', 'value'];

    public static function getValueForKey(string $key): ?string
    {
        $setting = static::where('key', $key)->get()->first();
        return isset($setting) ? $setting->value : null;
    }
}
