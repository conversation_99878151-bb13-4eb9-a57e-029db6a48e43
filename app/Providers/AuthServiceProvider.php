<?php

namespace App\Providers;

use App\Auth\Baes\BaesGuard;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Register new guard for external auth. The JWT tokens are handled
        // by an external service.
        Auth::extend('baes', fn ($app) => $app->make(BaesGuard::class));
    }
}
