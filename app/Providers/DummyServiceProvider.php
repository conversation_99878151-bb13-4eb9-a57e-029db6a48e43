<?php

namespace App\Providers;

use App\Repos\DummyGenerateTokenRepo;
use App\Repos\DummyValidateOtpRepo;
use App\Repos\GenerateTokenRepo;
use App\Repos\ValidateOtpRepo;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

/**
 * Provides dummy implementations of classes when instructed to do so.
 * Usually, implementations that call for an external service are mocked.
 */
class DummyServiceProvider extends ServiceProvider
{
    /**
     * When this header is included in the request with any option,
     * dummy implementations are used instead of actual ones.
     */
    public const HEADER = 'Dummy';

    /**
     * Actual class or interface and their dummy implementation.
     *
     * @var array
     */
    protected array $dummyImplementations = [
        ValidateOtpRepo::class => DummyValidateOtpRepo::class,
        GenerateTokenRepo::class => DummyGenerateTokenRepo::class,
    ];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(Request $request): void
    {
        if ($this->app->environment('production') || !$request->hasHeader(self::HEADER)) {
            return;
        }

        $dummyOptions = explode(',', $request->header(self::HEADER));
        if (in_array('no_external', $dummyOptions)) {
            foreach ($this->dummyImplementations as $actual => $dummy) {
                $this->app->bind($actual, fn ($app) => $app->make($dummy));
            }
        }
    }
}
