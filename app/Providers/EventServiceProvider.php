<?php

namespace App\Providers;

use App\Events\CantDecryptCredentialsOnLoginEvent;
use App\Events\CantFindLocationEvent;
use App\Events\CantReadGeoDbEvent;
use App\Events\CheckFailedEvent;
use App\Events\ConversationCreatedEvent;
use App\Events\DeviceNotFoundOnBiometricLoginEvent;
use App\Events\EmailUpdatedEvent;
use App\Events\ExternalCallMadeEvent;
use App\Events\ExternalCallMadeLegacyEvent;
use App\Events\ExternalCallToBeMadeEvent;
use App\Events\FailedToBiometricLoginDueToLackOfChallengeEvent;
use App\Events\FailedToBiometricLoginDueToLackOfPublicKeyEvent;
use App\Events\MaxDevicesReachedEvent;
use App\Events\NewUserRegistrationRequestEvent;
use App\Events\PhoneUpdatedEvent;
use App\Events\SuccessfulBiometricLoginEvent;
use App\Events\SuccessfulLoginEvent;
use App\Events\TooManyBiometricLoginAttemptsEvent;
use App\Events\TooManyLoginAttemptsEvent;
use App\Events\UpdatedTokenForUserEvent;
use App\Events\UserCantAccessSystemEvent;
use App\Events\UserDeletedEvent;
use App\Events\UserIsDisabledEvent;
use App\Events\UserRegisteredEvent;
use App\Events\WrongBiometricCredentialsEvent;
use App\Events\WrongCredentialsEvent;
use App\Listeners\ExternalLogDisabledUser;
use App\Listeners\ExternalLogFailLoginOnSystem;
use App\Listeners\ExternalLogMaxDevicesReached;
use App\Listeners\HandleUserRegistration;
use App\Listeners\IncrementLoginAttempts;
use App\Listeners\IncrementLoginAttemptsPerUser;
use App\Listeners\LogCantFindLocation;
use App\Listeners\LogCantReadGeoDb;
use App\Listeners\LogCheckFailed;
use App\Listeners\LogConversationCreated;
use App\Listeners\LogExternalCallMade;
use App\Listeners\LogExternalCallMadeLegacy;
use App\Listeners\LogFailedToBiometricLoginDueToLackOfChallenge;
use App\Listeners\LogFailedToBiometricLoginDueToLackOfPublicKey;
use App\Listeners\Login\ExternalLogLogin;
use App\Listeners\Login\LogDeviceNotFoundOnBiometricLogin;
use App\Listeners\Login\LogSuccessfulBiometricLogin;
use App\Listeners\Login\LogSuccessfulLogin;
use App\Listeners\Login\NotifyUserOfLogin;
use App\Listeners\Login\RegisterOrUpdateDevice;
use App\Listeners\Login\SetLoginDevice;
use App\Listeners\LogUpdatedTokenForUser;
use App\Listeners\LogUserDeleted;
use App\Listeners\LogUserRegistered;
use App\Listeners\LogWrongBiometricCredentials;
use App\Listeners\NotifyUserOfEmailUpdate;
use App\Listeners\NotifyUserOfNewCredentials;
use App\Listeners\NotifyUserOfPhoneUpdate;
use App\Listeners\NotifyUserOfTooManyAttempts;
use App\Listeners\NotifyUserOfWrongCredentials;
use App\Listeners\SetNewTokenForUser;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        SuccessfulLoginEvent::class => [
            SetNewTokenForUser::class,
            RegisterOrUpdateDevice::class,
            SetLoginDevice::class,
            NotifyUserOfLogin::class,
            ExternalLogLogin::class,
            LogSuccessfulLogin::class,
        ],
        SuccessfulBiometricLoginEvent::class => [
            SetNewTokenForUser::class,
            SetLoginDevice::class,
            NotifyUserOfLogin::class,
            ExternalLogLogin::class,
            LogSuccessfulBiometricLogin::class,
        ],
        CantDecryptCredentialsOnLoginEvent::class => [
            IncrementLoginAttempts::class,
        ],
        TooManyLoginAttemptsEvent::class => [
            IncrementLoginAttempts::class,
            NotifyUserOfTooManyAttempts::class,
        ],
        WrongCredentialsEvent::class => [
            IncrementLoginAttempts::class,
            IncrementLoginAttemptsPerUser::class,
            NotifyUserOfWrongCredentials::class,
        ],
        UserCantAccessSystemEvent::class => [
            IncrementLoginAttempts::class,
            ExternalLogFailLoginOnSystem::class,
        ],
        UserIsDisabledEvent::class => [
            ExternalLogDisabledUser::class,
        ],
        MaxDevicesReachedEvent::class => [
            ExternalLogMaxDevicesReached::class,
        ],
        PhoneUpdatedEvent::class => [
            NotifyUserOfPhoneUpdate::class,
        ],
        ConversationCreatedEvent::class => [
            LogConversationCreated::class,
        ],
        EmailUpdatedEvent::class => [
            NotifyUserOfEmailUpdate::class,
        ],
        ExternalCallToBeMadeEvent::class => [],
        ExternalCallMadeEvent::class => [
            LogExternalCallMade::class,
        ],
        ExternalCallMadeLegacyEvent::class => [
            LogExternalCallMadeLegacy::class,
        ],
        UpdatedTokenForUserEvent::class => [
            SetNewTokenForUser::class,
            LogUpdatedTokenForUser::class,
        ],
        NewUserRegistrationRequestEvent::class => [
            HandleUserRegistration::class,
        ],
        UserRegisteredEvent::class => [
            NotifyUserOfNewCredentials::class,
            LogUserRegistered::class,
        ],
        CheckFailedEvent::class => [
            LogCheckFailed::class,
        ],
        DeviceNotFoundOnBiometricLoginEvent::class => [
            IncrementLoginAttempts::class,
            LogDeviceNotFoundOnBiometricLogin::class,
        ],
        TooManyBiometricLoginAttemptsEvent::class => [
            IncrementLoginAttempts::class,
        ],
        WrongBiometricCredentialsEvent::class => [
            IncrementLoginAttempts::class,
            LogWrongBiometricCredentials::class,
        ],
        FailedToBiometricLoginDueToLackOfPublicKeyEvent::class => [
            IncrementLoginAttempts::class,
            LogFailedToBiometricLoginDueToLackOfPublicKey::class,
        ],
        FailedToBiometricLoginDueToLackOfChallengeEvent::class => [
            IncrementLoginAttempts::class,
            LogFailedToBiometricLoginDueToLackOfChallenge::class,
        ],
        UserDeletedEvent::class => [
            LogUserDeleted::class,
        ],
        CantReadGeoDbEvent::class => [
            LogCantReadGeoDb::class,
        ],
        CantFindLocationEvent::class => [
            LogCantFindLocation::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(): void
    {
    }
}
