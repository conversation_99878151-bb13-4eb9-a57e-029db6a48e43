<?php

namespace App\Providers;

use App\Factory\ClientFactory;
use App\Services\Configurations as Config;
use App\Services\Crypt\Rsa;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use League\HTMLToMarkdown\HtmlConverter;
use Twilio\Rest\Client as TwilioClient;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ClientFactory::class, function () {
            $handler = new \GuzzleHttp\Handler\CurlHandler();
            $handlerStack = \GuzzleHttp\HandlerStack::create($handler);
            $config =  [
                'base_uri' => '/',
                'handler' => $handlerStack,
                'http_errors' => false,
                'verify' => false,
                'timeout' => 90,
                'connect_timeout' => 30,
            ];
            return new ClientFactory($config);
        });

        $this->app->singleton(Rsa::class, fn () => new Rsa());
        $this->app->bind(Config::class, fn () => Config::getInstance());
        $this->app->bind(TwilioClient::class, function ($app) {
            $config = $app->make(Config::class);

            return new TwilioClient(
                $config->getConfigurations('TWILIO_ACCOUNT_SID'),
                $config->getConfigurations('TWILIO_AUTH_TOKEN'),
            );
        });
        $this->app->bind(HtmlConverter::class, fn () => new HtmlConverter(['strip_tags' => true]));
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Validator::includeUnvalidatedArrayKeys();
    }

    public function provides()
    {
        return [
            ClientFactory::class,
        ];
    }
}
