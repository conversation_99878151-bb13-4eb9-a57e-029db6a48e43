<?php

namespace App\Helpers;

use App\Services\Crypt\Rsa;
use Exception;

class OTPSharedKey
{
    private Rsa $rsa;

    public function __construct(Rsa $rsa)
    {
        $this->rsa = $rsa;
    }

    /**
     * Make shared key with unique identifier and emiter.
     * You must separate data with marks like ';', '.', ':'
     *
     * @param string $data
     * @return string
     */
    public function makeSharedKey(string $data): string
    {
        return $this->rsa->encrypt($data);
    }

    /**
     * Recover data from custom shared key
     *
     * @param string $key
     * @return array
     */
    public function recoverSharedKey(string $key, $separatorChar = ';'): array
    {
        try {
            $decript = $this->rsa->decrypt($key);
            $data = explode($separatorChar, $decript);
            $dataLength = count($data);
            if ($dataLength < 2) {
                return [null, null];
            }
            return $data;
        } catch (Exception $e) {
            return [null, null];
        }
    }
}
