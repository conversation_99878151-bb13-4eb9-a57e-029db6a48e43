<?php

namespace App\Helpers;

use App\Exceptions\ApiException;
use App\Models\Configurations;
use Exception;
use GuzzleHttp\Client;

class Notify
{
    public const ES_HUAWEI = 1;
    private static $urlFcmGoogle = 'https://fcm.googleapis.com/fcm/send';
    private $header;
    private $notify = [];
    public $title;
    public $body;
    public $data; // Si queremos pasar datos a la app, no solo notificarlo
    public $color = '#22c08a';
    public $sound = 'default';
    public $icon;


    public $to = '/topics/all';


    public $registrationIds = [];
    public $huaweiTokenIds = [];

    public function __construct()
    {
        $this->header = [
          'Authorization: key= ' . Configurations::getValueForKey("FIREBASE_TOKEN"),
          'Content-Type: application/json'
        ];
        $this->notify = [
          'priority' => 'high', 'restricted_package_name' => ''
        ];
    }

    public function sendNotify()
    {
        $this->notify = [
          'notification' => [
            'title' => $this->title,
            'body' => $this->body,
            'color' => $this->color,
            'sound' => $this->sound,
            'click_action' => 'FCM_PLUGIN_ACTIVITY',
          ]
        ];
        // Check variables
        if (!empty($this->icon)) {
            $this->notify['notification']['icon'] = $this->icon;
        }
        if (is_array($this->registrationIds) && count($this->registrationIds) > 0) {
            $this->notify['registration_ids'] = $this->registrationIds;
        } else {
            $this->notify['to'] = $this->to;
        }
        if (!empty($this->data)) {
            $this->notify['data'] = $this->data;
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::$urlFcmGoogle);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($this->notify));
        $result = curl_exec($ch);
        curl_close($ch);
        error_log($result);
        return $result;
    }


    /**
     * Get the access token for sending Huawei notifications
     *
     * @return object
     */
    private function authHuawei()
    {
        try {
            $client = new Client([
              'headers' => [
                "Content-Type" => "application/x-www-form-urlencoded",
                'POST' => '/oauth2/v2/token HTTP/1.1'
              ],
            ]);

            $options = [
              'form_params' => [
                'grant_type' => 'client_credentials',
                'client_id' =>  Configurations::getValueForKey('HUAWEI_CLIENT_ID'),
                'client_secret' => Configurations::getValueForKey('HUAWEI_CLIENT_SECRET'),
              ]
            ];
            //https://oauth-login.cloud.huawei.com/oauth2/v3/token;
            $url = Configurations::getValueForKey('OAUTH_HUAWEI_URL');
            $response = $client->request('POST', $url, $options);

            return json_decode((string) $response->getBody());
        } catch (Exception $ex) {
            throw new ApiException('Error al obtener token de Huawei');
        }
    }



    /**
     * Send push notification to Huawei devices
     *
     * @return mixed
     */
    public function sendNotificationHUAWEI()
    {
        try {
            $access = self::authHuawei();
            $token = $access->access_token;
            //set headers with hueawei oauth token
            $client = new Client([
                'headers' => [
                    "Host" => 'push-api.cloud.huawei.com',
                    "Content-Type" => "application/www.form-urlencoded",
                    "Authorization" => "Bearer $token",
                    "POST" => '/oauth2/v2/token HTTP/1.1'
                ]
            ]);

            $dataSend = [
              "validate_only" => false,
              "message" => [
                "android" => [
                    "notification" => [
                      "title" => $this->title,
                      "body" => $this->body,
                      "click_action" => [
                        "type" => 3,
                        "action" => 'FCM_PLUGIN_ACTIVITY'
                      ],
                    ]
                ],
                "token" => $this->huaweiTokenIds,
              ]
            ];
            //very icon
            if (!empty($this->icon)) {
                $dataSend['notification']['message']['android']['icon'] = $this->icon;
            }
            //verify data
            if (!empty($this->data)) {
                $dataSend['data'] = $this->data;
            }
            //verify huawei tokens length
            if (is_array($this->huaweiTokenIds) && count($this->huaweiTokenIds) == 0) {
                $dataSend['result_message'] = "No device to send notifications";
                return json_encode($dataSend);
            }

            $clientId = Configurations::getValueForKey('HUAWEI_CLIENT_ID');
            //https://push-api.cloud.huawei.com/v1/[appId]/messages:send;
            $confUrl = Configurations::getValueForKey('PUSH_NOTIFICATIONS_HUAWEI_URL');
            $url = str_replace('[appId]', $clientId, $confUrl);
            $response = $client->request('POST', $url, [ 'body' => json_encode($dataSend) ]);

            return  (string) $response->getBody();
        } catch (Exception $ex) {
            throw new ApiException('Error al enviar notificacion Huawei');
        }
    }
}
