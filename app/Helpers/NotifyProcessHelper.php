<?php

namespace App\Helpers;

use App\Models\Device;
use App\Models\LogNotifications;

class NotifyProcessHelper
{
    /**
     * Get token to send notifications android, IOS and huawei devices
     *
     * @param string $process
     * @param string $id
     * @param array $tokens
     * @param array $tokensHuawei
     * @return void
     */
    public function getTokens(string $process, string $id, array &$tokens, array &$tokensHuawei): void
    {
        if ($process == 'SOLICITUD') {
            $device = LogNotifications::where('ccapplication_id', $id)->first();
            if (!is_null($device)) {
                $tokens[] = $device->token;
            }
        } else {
            $devices = Device::select('devices.firebase_token', 'devices.huawei')
                           ->join('clients', 'clients.id', '=', 'devices.client_id')
                           ->where('devices.active', true)
                           ->where('clients.send_notification', '1')
                           ->where('clients.client_id', $id)
                           ->get();

            if (!is_null($devices)) {
                foreach ($devices as $device) {
                    if ($device->huawei == 1) {
                        $tokensHuawei[] = $device->firebase_token;
                    } else {
                        $tokens[] = $device->firebase_token;
                    }
                }
            }
        }
    }
}
