<?php

namespace App\Helpers;

use Illuminate\Support\Optional;
use Throwable;

/**
 * For handling chain of calls when values can be null.
 */
class Box extends Optional
{
    /**
     * Makes a new self.
     *
     * @param mixed $value
     * @return self
     */
    public static function put($value): self
    {
        return new self($value);
    }

    /**
     * Calls $function with $this->value if not null.
     *
     * @param callable $function
     * @return self
     */
    public function peek(callable $function): self
    {
        if (is_null($this->value)) {
            return self::put(null);
        }

        return self::put($function($this->value));
    }

    /**
     * Returns $this->value. If $default is provided, it is returned
     * if $this->value is null or calls it if $default is callable.
     *
     * @param mixed $default
     * @return mixed
     */
    public function andOpen($default = null): mixed
    {
        if (!is_null($this->value)) {
            return $this->value;
        }

        if (is_callable($default)) {
            return $default();
        }

        return $default;
    }

    /**
     * Like $this->andOpen() but if the value is empty, not just null.
     *
     * @param mixed $default
     * @return mixed
     */
    public function andOpenOnEmpty($default): mixed
    {
        if (!empty($this->value)) {
            return $this->value;
        }

        if (is_callable($default)) {
            return $default($this->value);
        }

        return $default;
    }

    /**
     * Like $this->andOpen() but if the value is null, it throws
     * $exception.
     *
     * @param Throwable $exception
     * @return mixed
     * @throws Throwable
     */
    public function andOpenAndThrow(Throwable $exception): mixed
    {
        $value = $this->andOpen();
        if (!is_null($value)) {
            return $value;
        }

        throw $exception;
    }

    /**
     * Dynamically access a property on the underlying object.
     *
     * @param  string  $key
     * @return self
     */
    public function __get($key)
    {
        return self::put(parent::__get($key));
    }

    /**
     * Get an item at a given offset.
     *
     * @param  mixed  $key
     * @return self
     */
    public function offsetGet($key): mixed
    {
        return self::put(parent::offsetGet($key));
    }

    /**
     * Dynamically pass a method to the underlying object.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return self
     */
    public function __call($method, $parameters)
    {
        return self::put(parent::__call($method, $parameters));
    }
}
