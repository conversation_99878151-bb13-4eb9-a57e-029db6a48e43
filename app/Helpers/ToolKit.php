<?php

namespace App\Helpers;

use App\Enums\SecurityType;
use App\Http\Controllers\API\ServicesRouter\ServicesRouterController;
use App\Models\ServicesRouter;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Route;

class ToolKit
{
    /**
     * Mapped routes.
     *
     * @var array<string, string>
     */
    public static array $mappedRoutes = [];

    /**
     * Adds specified routes to the mapped routes array.
     */
    public static function mapRoutes(array|string $routes, SecurityType $security = SecurityType::NONE): void
    {
        $routesArray = is_string($routes) ? [$routes] : $routes;

        foreach ($routesArray as $route) {
            // Check if the route doesn't already exists to avoid duplicates on the mapped routes
            if (!isset(self::$mappedRoutes[$security->value][$route])) {
                self::$mappedRoutes[$security->value][$route] = $route;
            }
        }
    }

    /**
     * Retuns a flatten array containing all the mapped routes.
     */
    public static function getAllMappedRoutes(): ?array
    {
        $flattenedRoutes = [];

        array_walk_recursive(static::$mappedRoutes, function ($route) use (&$flattenedRoutes) {
            $flattenedRoutes[] = $route;
        });

        return $flattenedRoutes;
    }

    /**
     * Returns mapped routes by the specified security type.
     */
    public static function getMappedRoutes(SecurityType $security): array
    {
        return static::$mappedRoutes[$security->value] ?? [];
    }

    /**
     * Stablish all the routes for the service router by their security type.
     */
    public static function servicesRoutes(SecurityType $security = SecurityType::NONE, array $exclude = []): void
    {
        $routes = ServicesRouter::where('security', $security->value)->get();
        self::toRoute($routes->reject(fn($route) => in_array($route->exposed_service_url, $exclude)));
    }

    /**
     * Registers service routes as formal laravel routes only specified records by their `exposed_service_url`.
     */
    public static function only(array|string $url): void
    {
        if (is_string($url)) {
            self::toRoute(collect([ServicesRouter::firstWhere('exposed_service_url', $url)])->whereNotnull());
            return;
        }

        $routes = ServicesRouter::whereIn('exposed_service_url', $url)->get();
        self::toRoute($routes);
    }

    /**
     * Converts a services router record into a laravel route.
     *
     * @param Collection<ServicesRouter> $routes
     */
    private static function toRoute(Collection $routes): void
    {
        foreach ($routes as $route) {
            $path = '/' . $route->exposed_service_url;
            if ($route->method == 'POST') {
                Route::post($path, [ServicesRouterController::class, 'postServices']);
            } elseif ($route->method == 'PUT') {
                Route::put($path, [ServicesRouterController::class, 'putServices']);
            } elseif ($route->method == 'DEL') {
                Route::delete($path, [ServicesRouterController::class, 'delServices']);
            } else {
                Route::get($path, [ServicesRouterController::class, 'getServices']);
            }
        }
    }
}
