<?php

namespace App\Helpers;

use App\Enums\Message\Generate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

/**
 * Handle exceptions to get custom messages
 */
class CustomHandler
{
    public const DATABASE_ERROR = '42S22';
    public const NULL_VALUE_ERROR = '23000';

    /**
     * Handle exception
     *
     * @param mixed $ex
     * @param integer $code
     * @return JsonResponse
     */
    public function exceptionHandler($ex, int $code = 5000): JsonResponse
    {
        $exCode = $ex->getCode();
        return match ($exCode) {
            self::DATABASE_ERROR => $this->getResponse(
                Response::HTTP_INTERNAL_SERVER_ERROR,
                Generate::ERROR_DB->value,
                $code
            ),

            self::NULL_VALUE_ERROR => $this->getResponse(
                Response::HTTP_BAD_REQUEST,
                Generate::ERROR_DB->value,
                $code
            ),
            default => $this->getResponse(
                Response::HTTP_INTERNAL_SERVER_ERROR,
                Generate::FAIL_SERVER->value,
                $code
            )
        };
    }

    /**
     * Get response as json format
     *
     * @param integer $status
     * @param string $message
     * @param integer $code
     * @return JsonResponse
     */
    private function getResponse(int $status, string $message, int $code): JsonResponse
    {
        return response()->json([
            'status' => $status ,
            'code' => $code,
            'message' => $message,
        ], $status);
    }
}
