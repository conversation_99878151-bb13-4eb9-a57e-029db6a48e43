<?php

namespace App\Helpers;

use App\Exceptions\ApiException;
use App\Factory\ClientFactory;
use App\Models\Client;
use App\Models\SubjectEmail;
use Exception;
use GuzzleHttp\Promise\Create;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Support\Facades\App;

/**
 * Notify sender
 * Simplify the notify send process
 *
 * @method static self init()
 * @method self client(\App\Models\Client $client)
 * @method self template(string $template)
 * @method self subject(string $subject)
 * @method self subjectByTemplateCode()
 * @method self CC(string $cc)
 * @method self ccBank(string $cc)
 * @method self chanelID(int $chanel)
 * @method self alternativeChanelsIDs(string|array $chanels)
 * @method send(bool $getResponse = false)
 */
class NotifySender
{
    public const WSP   = 1;
    public const SMS   = 2;
    public const EMAIL = 3;

    private array $data;
    private Client $client;

    public function __construct(private readonly ClientFactory $clientFactory)
    {
        $this->data = [
            'TemplateCode' => "APP_OTP01",
            'ChannelId' => self::EMAIL,
            'PhoneNumber' => '',
            'PhoneCountryCode' => "503",
            'EmailFrom' => '<EMAIL>',
            'EmailFromName' => 'Banco Atlántida',
            'EmailTo' => '',
            'EmailCC' => '',
            'EmailBCC' =>  '',
            'Subject' => '',
            'Parameters' => [
                'Date' => date('d/m/Y'),
                'Hour' => date('h:i A'),
            ],
            'AlternativeChannelIds' =>  '[2]',
        ];
    }

    /**
     * Init the class
     *
     * @return self
     */
    public static function init(ClientFactory $clientFactory): self
    {
        return new static($clientFactory);
    }


    /**
     * Set the data client to the OTP
     *
     * @param Client $client
     * @return Self
     */
    public function client(Client $client): self
    {
        $this->client = $client;
        $this->data['EmailTo']      = $client->email;
        $this->data['PhoneNumber']  = $client->phone_number;
        $this->data['Parameters']['ClientName']     = $client->short_name;
        $this->data['Parameters']['CustomerName']   = $client->short_name;
        return $this;
    }

    /**
     * Set OTP template
     *
     * @param string $template
     * @return self
     */
    public function template(string $template): self
    {
        $this->data['TemplateCode'] = $template;
        return $this;
    }

    /**
     * Set subject
     *
     * @param string $subject
     * @return self
     */
    public function subject(string $subject): self
    {
        $this->data['Subject'] = $subject;
        return $this;
    }

    /**
     * Set subject
     *
     * @param string $template
     * @return self
     */
    public function subjectByTemplateCode(string $template = ''): self
    {
        if (empty($this->client)) {
            throw new ApiException('You need to set the client first');
        }
        $templateCode = !empty($template) ? $template : $this->data['TemplateCode'];
        $subjectEmail = SubjectEmail::where('template_code', $templateCode)->first();
        $subject = $subjectEmail ? $subjectEmail->parseSubjectTemplate($this->client) : "-- {$templateCode} --";
        $this->subject($subject);
        return $this;
    }

    /**
     * Set customer name if you wannna change the full_name property value in \App\Models\Client
     *
     * @param string $name
     * @return self
     */
    public function customerName(string $name): self
    {
        $this->data['CustomerName'] = $name;
        return $this;
    }

    /**
     * Set cc email
     *
     * @param string $cc
     * @return self
     */
    public function CC(string $cc): self
    {
        $this->data['EmailCC'] = $cc;
        return $this;
    }

    /**
     * Set cc bank
     *
     * @param string $cc
     * @return self
     */
    public function ccBank(string $cc): self
    {
        $this->data['EmailBCC'] = $cc;
        return $this;
    }


    /**
     * Set chanel id to send notification
     *
     * @param integer $chanel
     * @return self
     */
    public function chanelID(int $chanel): self
    {
        $this->data['ChannelId'] = $chanel;
        return $this;
    }

    /**
     * Set additional chanels IDs
     *
     * @param string|array $chanels as string or array
     * @return self
     */
    public function alternativeChanelsIDs($chanels): self
    {
        $chanels = gettype($chanels) === 'string' ? $chanels : '['.implode(',', $chanels).']';
        $this->data['AlternativeChannelIds'] = $chanels;
        return $this;
    }

    /**
     * Add new parameters to send data to de service message sender.
     * If there is new parameters in the template only add it with this function.
     *
     * @param string $key
     * @param string $value
     * @return self
     */
    public function addParameter(string $key, string $value): self
    {
        $this->data['Parameters'][$key] = $value;
        return $this;
    }

    /**
     * Send OTP by chabel ID
     *
     * @return PromiseInterface
     */
    public function send(): PromiseInterface
    {
        if (App::environment('testing')) {
            return Create::promiseFor([]);
        }

        try {
            $body = json_encode($this->data);
            return $this->clientFactory
                ->customeResponseAsync("BaMessagesSendMessage", "", "", "", $body);
        } catch (Exception $ex) {
            throw new ApiException(
                'Unexpected error using BaMessagesSendMessage service ' . $ex->getMessage(),
                previous: $ex
            );
        }
    }
}
