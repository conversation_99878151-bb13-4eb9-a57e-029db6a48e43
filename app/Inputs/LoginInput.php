<?php

namespace App\Inputs;

use App\Dtos\Coordinates;
use App\Dtos\DeviceLogin;
use App\Dtos\Login;
use App\Dtos\LoginCredentials;
use App\Http\Requests\Auth\LoginRequest;
use App\Services\GeoIpService;

class LoginInput
{
    public function __construct(private GeoIpService $geoIp)
    {
    }

    public function toLogin(LoginRequest $request): Login
    {
        return Login::make(
            LoginCredentials::make($request->nickname, $request->password),
            $request,
            $this->asDeviceLogin($request),
        );
    }

    protected function asDeviceLogin(LoginRequest $request): DeviceLogin
    {
        return DeviceLogin::make(
            $request->device_id,
            $request->device_name,
            $request->firebase_token,
            $this->asCoordinates(
                $request->longitude,
                $request->latitude,
                $request->ip(),
            ),
            $request->is_huawei,
        );
    }

    protected function asCoordinates(?string $longitude, ?string $latitude, string $ip): Coordinates
    {
        if ($this->isEmptyOrZeroFloat($longitude) || $this->isEmptyOrZeroFloat($latitude)) {
            return $this->geoIp->getCoordinates($ip);
        }

        return Coordinates::make($longitude, $latitude);
    }

    private function isEmptyOrZeroFloat(?string $value): string
    {
        return empty((string) (float) $value);
    }
}
