<?php

namespace App\Inputs;

use App\Helpers\Box;
use App\Models\Client;
use Illuminate\Support\Collection;

class DeleteUserInput
{
    public function toUsers(Box $idsBox, Box $customerIdsBox, Box $duisBox): Collection
    {
        return $users = collect([
            $idsBox
                ->peek(collect(...))
                ->map(fn ($id) => (int) $id)
                ->peek(fn ($ids) => Client::whereIn('id', $ids)->get())
                ->andOpen(),
            $customerIdsBox
                ->peek(collect(...))
                ->peek(fn ($ids) => Client::whereIn('client_id', $ids)->get())
                ->andOpen(),
            $duisBox
                ->peek(collect(...))
                ->peek(fn ($duis) => Client::whereIn('dui', $duis)->get())
                ->andOpen(),
        ])
            ->flatten()
            ->whereNotNull()
            ->unique('id');
    }
}
