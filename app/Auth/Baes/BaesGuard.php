<?php

namespace App\Auth\Baes;

use App\Dtos\ExternalToken;
use App\Dtos\LoginForToken;
use App\Models\Client;
use App\Tasks\Auth\GenerateTokenForUserTask;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Hashing\Hasher;

/**
 * This auth is two-fold: The credentials are validated against
 * the DB as per usual, but the auth token is handled by an external
 * service, so after the validation the service is called.
 */
class BaesGuard implements Guard
{
    use GuardHelpers;

    public function __construct(
        private Client $userRepo,
        private Hasher $hasher,
        private GenerateTokenForUserTask $task,
    ) {}

    /**
     * Get the currently authenticated user.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function user()
    {
        return $this->user;
    }

    /**
     * Validate a user's credentials.
     *
     * @param  array  $credentials
     * @return bool
     */
    public function validate(array $credentials = [])
    {
        if (empty($credentials)) {
            return false;
        }

        $user = $this
            ->userRepo
            ->newQuery()
            ->firstWhere('nickname', $credentials['nickname']);
        if (is_null($user)) {
            return false;
        }

        return $this
            ->hasher
            ->check($credentials['password'], $user->password);
    }

    /**
     * Validate credentials and return an auth token.
     *
     * @param LoginForToken $loginData
     * @return ExternalToken
     * @throws AuthenticationException When credentials are invalid.
     */
    public function login(LoginForToken $loginData): ?ExternalToken
    {
        return $this
            ->task
            ->withGenerateExternalToken($loginData->toGenerateExternalToken())
            ->do();
    }
}
