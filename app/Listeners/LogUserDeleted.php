<?php

namespace App\Listeners;

use App\Events\UserDeletedEvent;
use Illuminate\Log\LogManager as Log;

class LogUserDeleted
{
    public function __construct(protected Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\UserDeletedEvent $event
     * @return void
     */
    public function handle(UserDeletedEvent $event)
    {
        $this
            ->log
            ->debug('User deleted', [
                'id' =>  $event->id,
                'customer_id' =>  $event->customerId,
                'username' =>  $event->username,
                'creditcard_application_id' =>  $event->creditcardApplicationId,
            ]);
    }
}
