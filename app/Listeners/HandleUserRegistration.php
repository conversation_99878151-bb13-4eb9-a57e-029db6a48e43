<?php

namespace App\Listeners;

use App\Jobs\ProcessUserRegistrationAsynchronously;

class HandleUserRegistration
{
    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        ProcessUserRegistrationAsynchronously::dispatch(
            creditcardApplicationId: $event->creditcardApplicationId,
            customer: $event->customer,
            contactChannels: $event->contactChannels,
        );
    }
}
