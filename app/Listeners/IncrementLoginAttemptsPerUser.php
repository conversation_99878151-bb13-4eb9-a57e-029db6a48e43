<?php

namespace App\Listeners;

use App\Events\WrongCredentialsEvent;
use App\Models\Client;
use App\Services\Configurations as Config;
use Illuminate\Log\LogManager as Log;

class IncrementLoginAttemptsPerUser
{
    public function __construct(private Log $log, private Config $config)
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\WrongCredentialsEvent  $event
     * @return void
     */
    public function handle(WrongCredentialsEvent $event)
    {
        $user = Client::firstWhere('nickname', $event->nickname);
        if (is_null($user)) {
            return $this
                ->log
                ->info("No actual user to which increment attempts", ['nickname' => $event->nickname]);
        }

        $user->password_login_attempts += 1;
        if ($user->password_login_attempts >= $this->config->maxAttempts()) {
            $user->disable();
        }

        $user->save();
    }
}
