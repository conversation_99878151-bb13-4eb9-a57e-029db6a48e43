<?php

namespace App\Listeners;

use App\Events\CantFindLocationEvent;
use Illuminate\Log\LogManager as Log;

class LogCantFindLocation
{
    public function __construct(protected Log $log)
    {
    }

    public function handle(CantFindLocationEvent $event): void
    {
        $this
            ->log
            ->info('Cannot find geo location for IP', [
                'ip' => $event->ip,
            ]);
    }
}
