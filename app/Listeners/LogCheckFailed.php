<?php

namespace App\Listeners;

use App\Events\CheckFailedEvent;
use App\Services\Log;
use Illuminate\Http\Request;

class LogCheckFailed
{
    public function __construct(
        private Log $log,
        private Request $request,
    ) {
    }

    /**
     * Handles the event.
     *
     * @return void
     */
    public function handle(CheckFailedEvent $event)
    {
        $this
            ->log
            ->warning(
                'Check failed for request',
                [
                    'path' => $this->request->path(),
                    'message' => $event->checkFailedMessage,
                ],
            );
    }
}
