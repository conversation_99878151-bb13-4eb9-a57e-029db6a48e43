<?php

namespace App\Listeners;

use App\Events\TooManyLoginAttemptsEvent;
use App\Tasks\NotifyUserOfTooManyAttemptsTask;

class NotifyUserOfTooManyAttempts
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(private NotifyUserOfTooManyAttemptsTask $task)
    {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\TooManyLoginAttemptsEvent $event
     * @return void
     */
    public function handle(TooManyLoginAttemptsEvent $event)
    {
        $this
            ->task
            ->withNickname($event->nickname)
            ->do();
    }
}
