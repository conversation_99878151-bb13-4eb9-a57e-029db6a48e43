<?php

namespace App\Listeners;

use App\Events\FailedToBiometricLoginDueToLackOfChallengeEvent;
use App\Services\Log;

class LogFailedToBiometricLoginDueToLackOfChallenge
{
    public function __construct(
        private Log $log,
    ) {
    }

    /**
     * Handles the event.
     *
     * @return void
     */
    public function handle(FailedToBiometricLoginDueToLackOfChallengeEvent $event): void
    {
        $this
            ->log
            ->warning(
                'Failed biometric login due to lack of challenge on device',
                [
                    'ip' => $event->request->ip(),
                    'device_id' => $event->deviceId,
                    'device_name' => $event->deviceName,
                    'signed_challenge' => $event->signedChallenge,
                ],
            );
    }
}
