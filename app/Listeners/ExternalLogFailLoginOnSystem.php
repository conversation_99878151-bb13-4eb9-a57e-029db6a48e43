<?php

namespace App\Listeners;

use App\Enums\LogApplicationCode;
use App\Enums\Message\AuthMessage;
use App\Events\UserCantAccessSystemEvent;
use App\Traits\ActionLogger;

class ExternalLogFailLoginOnSystem
{
    use ActionLogger;

    /**
     * Handle the event.
     *
     * @param \App\Events\UserCantAccessSystemEvent $event
     * @return void
     */
    public function handle(UserCantAccessSystemEvent $event)
    {
        $this->logBank(
            $event->user->client_id,
            LogApplicationCode::NO_SYSTEM_ACCESS,
            AuthMessage::NOT_PERMISSIONS_SYSTEM->value,
        );
    }
}
