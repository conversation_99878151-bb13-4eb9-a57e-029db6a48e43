<?php

namespace App\Listeners;

use App\Events\WrongBiometricCredentialsEvent;
use App\Services\Log;

class LogWrongBiometricCredentials
{
    public function __construct(
        private Log $log,
    ) {
    }

    /**
     * Handles the event.
     *
     * @return void
     */
    public function handle(WrongBiometricCredentialsEvent $event): void
    {
        $this
            ->log
            ->warning(
                'Failed biometric login with wrong credentials',
                [
                    'ip' => $event->request->ip(),
                    'device_id' => $event->deviceId,
                    'device_name' => $event->deviceName,
                    'signed_challenge' => $event->signedChallenge,
                ],
            );
    }
}
