<?php

namespace App\Listeners;

use App\Events\UserRegisteredEvent;
use App\Factory\ClientFactory;
use App\Helpers\NotifySender;
use Illuminate\Log\LogManager as Log;

class NotifyUserOfNewCredentials
{
    public function __construct(
        private ClientFactory $http,
        private Log $log,
    ) {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\UserRegisteredEvent  $event
     * @return void
     */
    public function handle(UserRegisteredEvent $event)
    {
        $this
            ->log
            ->info('Sending request for email of new credentials to external services', [
                'client_id' => $event->user->id,
            ]);

        $promise = NotifySender::init($this->http)
            ->client($event->user)
            ->subject('Envío de credenciales App One')
            ->template('APP_NOTCRE')
            ->chanelID(NotifySender::EMAIL)
            ->alternativeChanelsIDs('[2]')
            ->addParameter('User', $event->credentials->nickname)
            ->addParameter('Password', $event->credentials->password)
            ->send();

        $promise->then(
            fn ($response) => $this
                ->log
                ->info('Request for email of new credentials to external services sent successfully', [
                    'client_id' => $event->user->id,
                    'response_status' => $response->getStatusCode(),
                    'response_body' => (string) $response->getBody(),
                ]),
            fn ($exception) => $this
                ->log
                ->error('Failed to send email of new credentials to external services', [
                    'client_id' => $event->user->id,
                    'exception_type' => get_class($exception),
                    'message' => $exception->getMessage(),
                    'exception' => $exception,
                ]),
        );
    }
}
