<?php

namespace App\Listeners;

use App\Events\ExternalCallMadeEvent;
use App\Services\Log;
use App\Traits\HandlesExternalCallLogging;

class LogExternalCallMade
{
    use HandlesExternalCallLogging;

    public function __construct(private Log $log)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(ExternalCallMadeEvent $event): void
    {
        $logMessage = "External call made to {$event->baseUrl}/{$event->path}";

        $isUnsuccessful = $this->isUnsuccessfulResponse(
            $event->path,
            $event->status,
            $event->responseBody
        );

        $baseContext = [
            'base_url' => $event->baseUrl,
            'path' => $event->path,
            'response_status' => $event->status,
        ];

        $additionalContext = [
            'class' => $event->repo,
            'request_headers' => $event->requestHeaders,
            'request_body' => $event->requestBody,
            'request_query_params' => $event->requestQueryParams,
            'response_headers' => $event->responseHeaders
        ];

        $context = $this->buildContext($baseContext, $additionalContext, $isUnsuccessful);

        $context['response_body'] = $event->responseBody;

        if ($isUnsuccessful) {
            $this->log->warning($logMessage, $context);
            return;
        }

        $this->log->info($logMessage, $context);
    }
}
