<?php

namespace App\Listeners;

use App\Contracts\TokenEvent;
use App\Contracts\UserEvent;
use App\Tasks\Clients\UpdateBaesTokenTask;

class SetNewTokenForUser
{
    public function __construct(private UpdateBaesTokenTask $task)
    {
    }

    /**
     * Handle the event.
     *
     * @param TokenEvent&UserEvent $event
     * @return void
     */
    public function handle(TokenEvent&UserEvent $event)
    {
        $this
            ->task
            ->withUser($event->getUser())
            ->withToken($event->getToken())
            ->do();
    }
}
