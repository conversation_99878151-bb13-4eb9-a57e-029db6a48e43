<?php

namespace App\Listeners;

use App\Events\UserRegisteredEvent;
use Illuminate\Log\LogManager as Log;

class LogUserRegistered
{
    public function __construct(private Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\UserRegisteredEvent  $event
     * @return void
     */
    public function handle(UserRegisteredEvent $event)
    {
        $this
            ->log
            ->info('New user registered', [
                'user_id' => $event->user->id,
                'customer_id' => $event->user->client_id,
                'creditcard_application_id' => $event->user->creditcard_application_id,
            ]);
    }
}
