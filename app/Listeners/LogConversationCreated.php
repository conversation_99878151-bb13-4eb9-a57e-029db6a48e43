<?php

namespace App\Listeners;

use App\Events\ConversationCreatedEvent;
use Illuminate\Log\LogManager as Log;

class LogConversationCreated
{
    public function __construct(protected Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\ConversationCreatedEvent $event
     * @return void
     */
    public function handle(ConversationCreatedEvent $event)
    {
        $this
            ->log
            ->info('Created Twilio conversation for web chat participant', [
                'conversation_sid' => $event->conversation->sid,
                'participant_identity' => $event->identity,
            ]);
    }
}
