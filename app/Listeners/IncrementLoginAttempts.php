<?php

namespace App\Listeners;

use App\Contracts\RequestEvent;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class IncrementLoginAttempts
{
    use AuthenticatesUsers;

    /**
     * Increments login attempts.
     *
     * @param RequestEvent $event
     * @return void
     */
    public function handle(RequestEvent $event)
    {
        $this->incrementLoginAttempts($event->getRequest());
    }
}
