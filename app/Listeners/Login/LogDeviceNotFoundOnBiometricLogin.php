<?php

namespace App\Listeners\Login;

use App\Events\DeviceNotFoundOnBiometricLoginEvent;
use Illuminate\Log\LogManager as Log;

class LogDeviceNotFoundOnBiometricLogin
{
    public function __construct(protected Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\DeviceNotFoundOnBiometricLoginEvent  $event
     * @return void
     */
    public function handle(DeviceNotFoundOnBiometricLoginEvent $event)
    {
        $this
            ->log
            ->info('Device not found on biometric login', [
                'device_id' => $event->deviceId,
                'device_name' => $event->deviceName,
            ]);
    }
}
