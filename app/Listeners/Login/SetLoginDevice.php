<?php

namespace App\Listeners\Login;

use App\Contracts\DeviceEvent;
use App\Tasks\ClearChallengeOfDeviceTask;
use App\Tasks\OfflineDevicesTask;

class SetLoginDevice
{
    public function __construct(
        private OfflineDevicesTask $offlineDevicesTask,
        private ClearChallengeOfDeviceTask $clearChallengeTask,
    ) {
    }

    /**
     * Marks the device inside $event as online and all others
     * as offline.
     *
     * @param DeviceEvent $event
     * @return void
     */
    public function handle(DeviceEvent $event)
    {
        $this
            ->offlineDevicesTask
            ->withDevice($event->getDevice())
            ->do();

        $this
            ->clearChallengeTask
            ->withDevice($event->getDevice())
            ->do();
    }
}
