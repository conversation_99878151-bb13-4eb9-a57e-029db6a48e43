<?php

namespace App\Listeners\Login;

use App\Contracts\UserEvent;
use App\Tasks\NotifyUserOfSuccesfulLoginTask;

class NotifyUserOfLogin
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(private NotifyUserOfSuccesfulLoginTask $task)
    {
    }

    /**
     * Handle the event.
     *
     * @param  UserEvent $event
     * @return void
     */
    public function handle(UserEvent $event)
    {
        $client = $event->getUser();

        if ($client->homologated) {
            return;
        }

        $this
            ->task
            ->withUser($client)
            ->do();
    }
}
