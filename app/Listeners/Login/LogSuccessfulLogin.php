<?php

namespace App\Listeners\Login;

use App\Events\SuccessfulLoginEvent;
use Illuminate\Log\LogManager as Log;

class LogSuccessfulLogin
{
    public function __construct(protected Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\SuccessfulLoginEvent  $event
     * @return void
     */
    public function handle(SuccessfulLoginEvent $event)
    {
        $this
            ->log
            ->info('Successful login', [
                'user_id' => $event->user->id,
                'device_id' => $event->login->device->id,
            ]);
    }
}
