<?php

namespace App\Listeners\Login;

use App\Events\SuccessfulBiometricLoginEvent;
use Illuminate\Http\Request;
use Illuminate\Log\LogManager as Log;

class LogSuccessfulBiometricLogin
{
    public function __construct(
        protected Log $log,
        protected Request $request,
    ) {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\SuccessfulBiometricLoginEvent  $event
     * @return void
     */
    public function handle(SuccessfulBiometricLoginEvent $event)
    {
        $this
            ->log
            ->info('Successful biometric login', [
                'ip' => $this->request->ip(),
                'user_id' => $event->user->id,
                'device_id' => $event->device->id,
            ]);
    }
}
