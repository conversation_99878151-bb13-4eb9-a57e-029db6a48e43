<?php

namespace App\Listeners\Login;

use App\Events\SuccessfulLoginEvent;
use App\Models\Device;

class RegisterOrUpdateDevice
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\SuccessfulLoginEvent $event
     * @return void
     */
    public function handle(SuccessfulLoginEvent $event)
    {
        $device = $event->login->device;
        Device::updateOrCreate(
            [
                'device_id' => $device->id,
                'client_id' => $event->user->id,
            ],
            [
                'active' => true,
                'device_name' => $device->name,
                'firebase_token' => $device->firebaseToken,
                'ultimate_longitud' => $device->coordinates->longitude,
                'ultimate_latitud' => $device->coordinates->latitude,
                'online' => Device::ONLINE,
                'huawei' => $device->isHuawei,
                'latest_activity' => now(),
            ],
        );
    }
}
