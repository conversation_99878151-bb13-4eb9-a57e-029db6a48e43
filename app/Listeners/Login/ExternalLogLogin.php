<?php

namespace App\Listeners\Login;

use App\Contracts\UserEvent;
use App\Enums\LogApplicationCode;
use App\Traits\ActionLogger;

class ExternalLogLogin
{
    use ActionLogger;

    /**
     * Handle the event.
     *
     * @param  UserEvent $event
     * @return void
     */
    public function handle(UserEvent $event)
    {
        $this->logBank(
            $event->getUser()->client_id,
            LogApplicationCode::LOGIN
        );
    }
}
