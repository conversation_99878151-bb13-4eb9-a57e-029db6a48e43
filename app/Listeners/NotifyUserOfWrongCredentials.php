<?php

namespace App\Listeners;

use App\Events\WrongCredentialsEvent;
use App\Tasks\NotifyUserOfWrongCredentialsTask;

class NotifyUserOfWrongCredentials
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(private NotifyUserOfWrongCredentialsTask $task)
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\WrongCredentialsEvent  $event
     * @return void
     */
    public function handle(WrongCredentialsEvent $event)
    {
        $this
            ->task
            ->withNickname($event->nickname)
            ->do();
    }
}
