<?php

namespace App\Listeners;

use App\Events\ExternalCallToBeMadeEvent;
use Illuminate\Log\LogManager as Log;

class LogExternalCallToBeMade
{
    public function __construct(private Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\ExternalCallToBeMadeEvent $event
     * @return void
     */
    public function handle(ExternalCallToBeMadeEvent $event)
    {
        $this
            ->log
            ->debug('Making external call', [
                'base_url' => $event->baseUrl,
                'path' => $event->path,
                'class' => $event->repo,
                'call_id' => $event->id,
            ]);
    }
}
