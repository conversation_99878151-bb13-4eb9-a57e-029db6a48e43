<?php

namespace App\Listeners;

use App\Enums\LogApplicationCode;
use App\Events\UserIsDisabledEvent;
use App\Services\Configurations as Config;
use App\Traits\ActionLogger;

class ExternalLogDisabledUser
{
    use ActionLogger;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(private Config $config)
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\UserIsDisabledEvent  $event
     * @return void
     */
    public function handle(UserIsDisabledEvent $event)
    {
        $this->logBank(
            $event->user->client_id,
            LogApplicationCode::USER_DISABLED,
            $this->config->getConfigurations('MENSAJE_USUARIO_BLOQUEADO_ADMINISTRACION'),
        );
    }
}
