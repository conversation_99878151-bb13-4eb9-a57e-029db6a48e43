<?php

namespace App\Listeners;

use App\Events\CantReadGeoDbEvent;
use Illuminate\Log\LogManager as Log;

class LogCantReadGeoDb
{
    public function __construct(protected Log $log)
    {
    }

    public function handle(CantReadGeoDbEvent $event): void
    {
        $this
            ->log
            ->emergency("The Geo DB can't be read, is not found, or corrupted", [
                'geo_db_path' => $event->path,
            ]);
    }
}
