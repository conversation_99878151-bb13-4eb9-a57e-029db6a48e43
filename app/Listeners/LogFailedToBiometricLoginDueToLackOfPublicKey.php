<?php

namespace App\Listeners;

use App\Events\FailedToBiometricLoginDueToLackOfPublicKeyEvent;
use App\Services\Log;

class LogFailedToBiometricLoginDueToLackOfPublicKey
{
    public function __construct(
        private Log $log,
    ) {}

    /**
     * Handles the event.
     *
     * @return void
     */
    public function handle(FailedToBiometricLoginDueToLackOfPublicKeyEvent $event): void
    {
        $this
            ->log
            ->warning(
                'Failed biometric login due to lack of public key on device',
                [
                    'ip' => $event->request->ip(),
                    'device_id' => $event->deviceId,
                    'device_name' => $event->deviceName,
                    'signed_challenge' => $event->signedChallenge,
                ],
            );
    }
}
