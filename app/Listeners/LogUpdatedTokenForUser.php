<?php

namespace App\Listeners;

use App\Events\UpdatedTokenForUserEvent;
use Illuminate\Log\LogManager as Log;

class LogUpdatedTokenForUser
{
    public function __construct(private Log $log)
    {
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\UpdatedTokenForUserEvent $event
     * @return void
     */
    public function handle(UpdatedTokenForUserEvent $event)
    {
        $this
            ->log
            ->info('External token updated for user', [
                'user_id' => $event->user->id,
            ]);
    }
}
