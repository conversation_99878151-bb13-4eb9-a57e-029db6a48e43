<?php

namespace App\Listeners;

use App\Events\ExternalCallMadeLegacyEvent;
use App\Services\Log;
use App\Traits\HandlesExternalCallLogging;

class LogExternalCallMadeLegacy
{
    use HandlesExternalCallLogging;

    public function __construct(private Log $log)
    {
    }

    public function handle(ExternalCallMadeLegacyEvent $event): void
    {
        $logMessage = "[LEGACY] External call made to {$event->baseUrl}";

        $isUnsuccessful = $this->isUnsuccessfulResponse(
            $event->gatewayService,
            (int) $event->responseStatus,
            $event->responseBody
        );

        $baseContext = [
            'base_url' => $event->baseUrl,
            'gateway_service' => $event->gatewayService,
            'response_status' => $event->responseStatus
        ];

        $additionalContext = [
            'request' => [
                'method' => $event->requestMethod,
                'headers' => $event->requestHeaders,
                'parameters' => $event->requestParameters,
            ],
        ];

        $context = $this->buildContext($baseContext, $additionalContext, $isUnsuccessful);

        $context['response_body'] = $event->responseBody;

        if ($isUnsuccessful) {
            $this->log->warning($logMessage, $context);
            return;
        }

        $this->log->info($logMessage, $context);
    }
}
