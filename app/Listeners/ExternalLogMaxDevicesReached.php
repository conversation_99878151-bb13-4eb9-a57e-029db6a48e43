<?php

namespace App\Listeners;

use App\Enums\LogApplicationCode;
use App\Enums\Message\AuthMessage;
use App\Events\MaxDevicesReachedEvent;
use App\Traits\ActionLogger;

class ExternalLogMaxDevicesReached
{
    use ActionLogger;

    /**
     * Handle the event.
     *
     * @param  \App\Events\MaxDevicesReachedEvent  $event
     * @return void
     */
    public function handle(MaxDevicesReachedEvent $event)
    {
        $this->logBank(
            $event->user->client_id,
            LogApplicationCode::MAX_DEVICES_REACHED,
            AuthMessage::MAXIMUM_REGISTERED_DEVICES->value,
        );
    }
}
