<?php

namespace App\Enums\Message;

/**
 * Generation response codes for Message
 * @codeCoverageIgnore
 */
enum Generate: string
{
    case CLIENT_NOT_FOUND                 = "Cliente no encontrado.";
    case FAIL_SERVER                      = "Error interno de servidor.";
    case MORE_REQUIRED_FIELD              = "Uno o más campos son requeridos.";
    case INCOMPLETED_FIELD_REQUIRED       = "Campos incompletos, uno o más campos son requeridos.";
    case NOT_MORE_DATA                    = "No se encuentran datos de contacto";
    case NUMBER_INCORRECT_TRY_AGAIN       = "El número ingresado no es correcto.\nIntenta nuevamente.";
    case DUI_FAIL                         = "El número de documento ingresado no es correcto.";
    case RESULT_OK                        = 'OK';
    case NICK_NAME_SEND_EMAIL             = 'Nickname enviado por correo';
    case POSITIVE_FACE_BASE               = 'POSITVE';
    case NEGATIVE_FACE_BASE               = 'NOT_POSITVE';
    case PENDING_FACE_BASE                = 'PENDING';
    case CREDENTIAL_FAIL                  = 'Credenciales bloqueadas por fallo de autenticación.';
    case CLIENT_DELETE_SYSTEM             = 'Cliente eliminado del sistema.';
    case CLIENT_ADD_SYSTEM                = 'Cliente agregado al sistema.';
    case succes                           = 'SUCCESS';
    case INVALED_KEY                      = 'Key inválida';
    case DATE_FORMAT                      = "d/m/Y";
    case PHONE_REGISTER                   = 'El número de teléfono ya está registrado';
    case COD_OTP_INVALID                  = 'Código OTP inválido';
    case FAIL_EMAIL_HAVE_REGISTER         = 'El correo ya está registrado';
    case EMAIL_REGISTER_SUCCES            = 'Su correo ha sido cambiado satisfactoriamente.';
    case CLIENT_NOT_FOUND_SEARCH_NICKNAME = 'Cliente no encontrado, revisa si esta correcto el nickname';
    case DELETE_CLIENT                    = 'Cliente eliminado';
    case CLIENT_NOT_FOUND_SEARCH_CUSTOMER = 'Cliente no encontrado, revisa si esta correcto el customer id';
    case ADDADRESSS                       = "Dirección agregada";
    case CLIENT_NOT_REGISTER              = "El :info :valor no se encuentra registrado.";
    case CLIENT_REGISTER                  = "El  :info :valor  se encuentra registrado.";
    case FIELD_NOT_FOUND                  = "El campo :info no existe.";
    case FIELD_SYSTEM_CODE_INVALID        = "System_code inválido.";
    case TEMPLATE_NOT_FOUND               = "Plantilla no encontrada";
    case NOTIFICATE_SEND                  = "Notificación enviada";
    case ERROR_DB                         = "Error de base de datos.";
    case VALUE_NULL                       = "Valor nulo.";
    case NOT_AUTORIZATE                   = 'No autorizado';
    case NOT_DESCRIPT                     = 'Uno de los datos solicitados no se ha podido desencriptar.';
    case NOT_CLIENT_FIELD_REQUERD         = 'Error al crear el cliente, uno o más campos no son valídos.';
    case CREDENTIAL_INVALID               = 'Credenciales inválidas.';
    case SECCION_EXPIRE                   = 'Su sesión ha expirado.';
    case DEVICE_NOT_FOUND                 = 'No se encontró el dispositivo.';
    case COULD_NOT_REFRESH_TOKEN          = 'No se pudo refrescar el token.';
    case NOT_CONFIGURATION                = 'No se encontro ninguna';
    case NOT_SURVEY_FOUND                 = 'No se encontró la encuesta solicitada.';
    case TOKEN_REFRESH                    = 'Token refrescado correctamente.';
    case DEVICE_NOT_LOGIN_BIOMETRIC       = 'Este dispotivo no tiene habilitado login biometrico.';
    case UNVERIFIED_DEVICE                = 'Dispositivo no verificado.';
    case REGISTERED_DEVICE                = 'Dispositivo registrado.';
    case MAXIMUM_REGISTERED_DEVICES       = 'Ha alcanzado el maximo de dispositivos registrados.';
    case DEVICE_REMOVED                   = 'Dispositivo eliminado.';
    case DEVICE_NOT_REMOVED               = 'No se pudo eliminar el dispositivo.';
    case CHANGED_PASSWORD                 = 'Su clave se modificó satisfactoriamente.';
    case ERROR_CHANGE_PASSWORD            = 'Error al establecer primer contraseña.';
    case ERROR_VERIFY_PASSWORD            = 'Error al verificar contraseña.';
    case NOT_CUSTOMER_FOUND_DUI           = 'No se encontró cliente con DUI ';
    case PASSWORD_USED                    = 'Contraseña usada con anterioridad.';
    case INVALID_PASSWORD_FORMAT          = 'El formato de contraseña no es válido.';
    case VALID_PASSWORD_FORMAT            = 'El formato de contraseña es válido.';
    case NOT_CLIENT_WHIT_DUI              = 'No se encontró cliente con DUI :valor';
    case INVALID_IDENTIFIER               = 'El campo \'IDENTIFIER\' debe ser CLIENT_ID o DUI';
}
