<?php

namespace App\Enums\Message;

/**
 * Response OTP messages
 * @codeCoverageIgnore
 */
enum OTPMessage: string
{
    case VALID              = 'OTP válido';
    case INVALID            = 'Código OTP inválido';
    case EXPIRED            = 'El tiempo de vida del código ha expirado, debes solicitar uno nuevo.';
    case INVALID_SHARED_KEY = 'Código inválido.';
    case MAX_GENERATES_CODE = 'Has excedido el máximo de códigos generados. Por tu seguridad intenta de nuevo mas tarde.';
    case WAIT_OTP_TIME      = 'Por el momento no puede generar otro código. Por favor espere.';
    case MAX_ATTEMPTS       = 'Has excedido el máximo de intentos. Por tu seguridad intenta de nuevo en 5 minutos.';
    case NOT_GENERATE_CODES = 'No puede generar más códigos, ya que ha excedido los intentos de validación.';
    case SUCCESSED_ATTEMPTS = 'Ha superado los intentos de validar el código. Espere :varlor minutos.';
}
