<?php

namespace App\Enums\Message;

/**
 * Display messages to collector responses
 * @codeCoverageIgnore
 */
enum AuthMessage: string
{
    case NOT_CLOSE_SESSION = 'No se pudo cerrar sesión.';
    case CLOSE_SESSION = 'Cierre de sesión exitoso.';

    case LAST_ENTRY_APP = 'Último ingreso a la APP.';

    case FAIL_CLOSE_SESSION = 'Falló al cerrar sesión.';

    case MAXIMUM_REGISTERED_DEVICES = 'Ha alcanzado el maximo de dispositivos registrados.';

    case INCORRECT_FINGERPRINT = '<PERSON><PERSON> incorrecta, te queda ATTEMPTS intento antes de bloquear el acceso';

    case SESSION_STARTED = 'Sesión iniciada correctamente.';

    case ENTER_PASSWORD = 'Debe ingresar nueva clave.';

    case NOT_PERMISSIONS_SYSTEM = 'Este usuario no tiene permisos de ingreso al sistema.';

    case EXTERNAL_TOKEN_SERVICE_FAILURE = 'No se pudo autenticar al usuario, intentar nuevamente en unos momentos.';
}
