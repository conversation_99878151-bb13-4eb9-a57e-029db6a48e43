<?php

declare(strict_types=1);

namespace App\Enums;

use App\Exceptions\InvalidLogApplicationCode;

enum LogApplicationCode: string
{
    case LOGIN = 'LOG_CUSTOMER_LOGIN';
    case USER_DISABLED = 'LOG_CUSTOMER_LOGIN_USER_DISABLED';
    case NO_SYSTEM_ACCESS = 'LOG_CUSTOMER_LOGIN_NO_SYSTEM_ACCESS';
    case MAX_DEVICES_REACHED = 'LOG_CUSTOMER_LOGIN_MAX_DEVICES_REACHED';
    case LOGOUT = 'LOG_CUSTOMER_LOGOUT';
    case RECOVER_USER = 'LOG_CUSTOMER_RECOVER_USER';
    case RESET_PASSWORD = 'LOG_CUSTOMER_RESET_PASSWORD';

    public function getValue(): string
    {
        return match ($this) {
            self::USER_DISABLED,
            self::NO_SYSTEM_ACCESS,
            self::MAX_DEVICES_REACHED => self::LOGIN->value,
            default => $this->value
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::LOGIN => 'Accediste a ONE con éxito',
            self::LOGOUT => 'Finalizaste sesión en ONE',
            self::MAX_DEVICES_REACHED => 'Alcanzaste el máximo de dispositivos registrado',
            self::RECOVER_USER => 'Solicitaste la recuperación de usuario',
            self::RESET_PASSWORD => 'Realizaste cambio de contraseña',
            self::NO_SYSTEM_ACCESS, self::USER_DISABLED => 'Hubo un problema al intentar acceder a ONE',
            default => throw new InvalidLogApplicationCode()
        };
    }
}
