<?php

namespace App\Enums\ValidateData;

/**
 * Fields inside collectors forms
 * @codeCoverageIgnore
 */
enum CollectorFields: string
{
    case VALUE           = 'value';
    case TYPE            = 'type';
    case VALIDATIONS     = 'validations';
    case QUERY_FORM      = 'Data.dataSets.data.queryForm';
    case FIELDS          = 'Data.dataSets.data.queryForm.fields';


    case NOT_LOGIN_FILED = 'Error al hacer login, uno o más campos no son valídos.';
    case PASSWORD_REQUIRED = 'La contraseña es requerida.';
    case PASSWORD_CONFIRM_REQUIRED = 'La confirmación de contraseña es requerida.';
    case PASSWORD_CONFIRM_SAME = 'Las contraseñas no coinciden.';
}
