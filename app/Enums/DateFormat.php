<?php

declare(strict_types=1);

namespace App\Enums;

enum DateFormat: string
{
    
    // Format: Day-Month-Year (e.g., 31-12-2025)
    case DAY_MONTH_YEAR_FULL = 'd-m-Y';
    
    // Format: Month-Day-Year (e.g., 12-31-2025)
    case MONTH_DAY_YEAR_FULL = 'm-d-Y';
    
    // Format: Year-Month-Day (e.g., 2025-12-31)
    case YEAR_MONTH_DAY_FULL = 'Y-m-d';
    
    // Format: Day-Month-Year (short year) (e.g., 31-12-25)
    case DAY_MONTH_YEAR_SHORT = 'd-m-y';
    
    // Format: Month-Day-Year (short year) (e.g., 12-31-25)
    case MONTH_DAY_YEAR_SHORT = 'm-d-y';
    
    // Format: Year-Month-Day (short year) (e.g., 25-12-31)
    case YEAR_MONTH_DAY_SHORT = 'y-m-d';
    
    // Format: Day-Month-Year (e.g., 31-December-2025)
    case DAY_MONTH_YEAR_FULL_TEXTUAL = 'd-F-Y';
    
    // Format: Month-Day-Year (e.g., December-31-2025)
    case MONTH_DAY_YEAR_FULL_TEXTUAL = 'F-d-Y';
    
    // Format: Year-Month-Day (e.g., 2025-December-31)
    case YEAR_MONTH_DAY_FULL_TEXTUAL = 'Y-F-d';
    
    // Format: Day-Month-Year (short textual month) (e.g., 31-Dec-25)
    case DAY_MONTH_YEAR_SHORT_TEXTUAL = 'd-M-y';
    
    // Format: Month-Day-Year (short textual month) (e.g., Dec-31-25)
    case MONTH_DAY_YEAR_SHORT_TEXTUAL = 'M-d-y';
    
    // Format: Year-Month-Day (short textual month) (e.g., 25-Dec-31)
    case YEAR_MONTH_DAY_SHORT_TEXTUAL = 'y-M-d';
    
    // Format: Month-Day (no year) (e.g., 12-31)
    case MONTH_DAY_NO_YEAR = 'm-d';
    
    // Format: Day-Month (no year) (e.g., 31-12)
    case DAY_MONTH_NO_YEAR = 'd-m';
    
    // Textual format: Month-Day (e.g., Dec-31)
    case MONTH_DAY_TEXTUAL = 'M-d';
    
    // Textual format: Day-Month (e.g., 31-Dec)
    case DAY_MONTH_TEXTUAL = 'd-M';
    
    // Format: Month-Year (e.g., 12-2025)
    case MONTH_YEAR = 'm-Y';
    
    // Format: Month-Year (short year) (e.g., 12-25)
    case MONTH_YEAR_SHORT = 'm-y';
    
    // Format: Month-Year (textual month) (e.g., Dec-2025)
    case MONTH_TEXTUAL_YEAR = 'M-Y';
    
    // Format: Month-Year (short year, textual month) (e.g., Dec-25)
    case MONTH_TEXTUAL_YEAR_SHORT = 'M-y';
    
    // Format: Month-Year (full textual month) (e.g., December-2025)
    case MONTH_FULL_TEXTUAL_YEAR = 'F-Y';
    
    // Format: Month-Year (short year, full textual month) (e.g., December-25)
    case MONTH_FULL_TEXTUAL_YEAR_SHORT = 'F-y';
    
    // Format: Year-Month (e.g., 2025-12)
    case YEAR_MONTH = 'Y-m';

    // Format: Year-Month (short year) (e.g., 25-12)
    case YEARH_SHORT_MONTH = 'y-m';
    
    // Format: Year-Month (textual month) (e.g., 2025-Dec)
    case YEAR_MONTH_TEXTUAL = 'Y-M';

    // Format: Year-Month (short year, textual month) (e.g., 25-Dec)
    case YEAR_SHORT_MONTH_TEXTUAL = 'y-M';

    // Format: Year-Month (full textual month) (e.g., 2025-December)
    case YEAR_MONTH_FULL_TEXTUAL = 'Y-F';

    // Format: Year-Month (short year, full textual month) (e.g., 25-December)
    case YEAR_SHORT_MONTH_FULL_TEXTUAL = 'y-F';

    /**
     * Returns the respective pattern for the textual date format to be able to translate them.
     *
     * @throws \InvalidArgumentException
     */
    public function translationPattern(): string
    {
        return match ($this) {
            self::DAY_MONTH_YEAR_FULL_TEXTUAL => 'dd-MMMM-yyyy',
            self::MONTH_DAY_YEAR_FULL_TEXTUAL => 'MMMM-dd-yyyy',
            self::YEAR_MONTH_DAY_FULL_TEXTUAL => 'yyyy-MMMM-dd',
            self::DAY_MONTH_YEAR_SHORT_TEXTUAL => 'dd-MMM-yy',
            self::MONTH_DAY_YEAR_SHORT_TEXTUAL => 'MMM-dd-yy',
            self::YEAR_MONTH_DAY_SHORT_TEXTUAL => 'yy-MMM-dd',
            self::MONTH_DAY_TEXTUAL => 'MMM-dd',
            self::DAY_MONTH_TEXTUAL => 'dd-MMM',
            self::MONTH_TEXTUAL_YEAR => 'MMM-yyyy',
            self::MONTH_TEXTUAL_YEAR_SHORT => 'MMM-yy',
            self::MONTH_FULL_TEXTUAL_YEAR => 'MMMM-yyyy',
            self::MONTH_FULL_TEXTUAL_YEAR_SHORT => 'MMMM-yy',
            self::YEAR_MONTH_TEXTUAL => 'yyyy-MMM',
            self::YEAR_SHORT_MONTH_TEXTUAL => 'yy-MMM',
            self::YEAR_MONTH_FULL_TEXTUAL => 'yyyy-MMMM',
            self::YEAR_SHORT_MONTH_FULL_TEXTUAL => 'yy-MMMM',
            default => throw new \InvalidArgumentException('No translation pattern for this format'),
        };
    }
}
