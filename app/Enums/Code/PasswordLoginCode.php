<?php

declare(strict_types=1);

namespace App\Enums\Code;

use App\Enums\Code\HomologatedLoginCode;

enum PasswordLoginCode: int
{
    case SUCCESSFUL_LOGIN = 1;
    case DEVICE_NOT_FOUND = 2;
    case REQUIRES_PASSWORD_RESET = 3;
    case EXPIRE_PASSWORD = 4;
    case MAXIMUM_DEVICES_REACHED = 5;
    case TOO_MANY_LOGIN_ATTEMPTS = 6;
    case WRONG_CREDENTIALS = 7;
    case INVALID_SYSTEM_PERMISSIONS = 8;
    case WRONG_BIOMETRIC_CREDENTIALS = 9;
    case DISABLED_USER = 10;
    case DEVICE_MISSING_PUBLIC_KEY = 11;
    case  DEVICE_MISSING_CHALLENGE = 12;
    case UNEXPECTED_ERROR = 500;

    public function getCodeForHomologatedUsers(): ?HomologatedLoginCode
    {
        return match ($this) {
            self::SUCCESSFUL_LOGIN => HomologatedLoginCode::SUCCESSFUL_LOGIN,
            self::REQUIRES_PASSWORD_RESET => HomologatedLoginCode::REQUIRES_PASSWORD_RESET,
            self::MAXIMUM_DEVICES_REACHED => HomologatedLoginCode::MAXIMUM_DEVICES_REACHED,
            self::WRONG_CREDENTIALS => HomologatedLoginCode::WRONG_CREDENTIALS,
            self::WRONG_BIOMETRIC_CREDENTIALS => HomologatedLoginCode::WRONG_BIOMETRIC_CREDENTIALS,
            self::INVALID_SYSTEM_PERMISSIONS => HomologatedLoginCode::INVALID_SYSTEM_PERMISSIONS,
            self::DISABLED_USER => HomologatedLoginCode::DISABLED_USER,
            self::DEVICE_NOT_FOUND => HomologatedLoginCode::DEVICE_NOT_FOUND,
            self::DEVICE_MISSING_PUBLIC_KEY => HomologatedLoginCode::DEVICE_MISSING_PUBLIC_KEY,
            self::DEVICE_MISSING_CHALLENGE => HomologatedLoginCode::DEVICE_NOT_FOUND,
            self::UNEXPECTED_ERROR => HomologatedLoginCode::UNEXPECTED_ERROR,
            default => null,
        };
    }
}
