<?php

declare(strict_types=1);

namespace App\Enums\Code;

enum HomologatedLoginCode: string
{
    case SUCCESSFUL_LOGIN = "success";
    case REQUIRES_PASSWORD_RESET = "requires_password_reset";
    case MAXIMUM_DEVICES_REACHED = "max_devices_reached";
    case WRONG_CREDENTIALS = "wrong_credentials";
    case WRONG_BIOMETRIC_CREDENTIALS = "wrong_biometric_credentials";
    case INVALID_SYSTEM_PERMISSIONS = "invalid_system_permissions";
    case DISABLED_USER = "disabled_user";
    case DEVICE_NOT_FOUND = "device_not_found";
    case DEVICE_MISSING_PUBLIC_KEY = "device_missing_public_key";
    case DEVICE_MISSING_CHALLENGE = "device_missing_challenge";
    case NON_HOMOLOGATED_USER_NO_VBANK = "non_homologated_no_vbank";
    case NON_HOMOLOGATED_USER_VBANK = "non_homologated_has_vbank";
    case TOO_MANY_LOGIN_ATTEMPTS = "too_many_login_attempts";
    case UNEXPECTED_ERROR = "unexpected_error";
}
