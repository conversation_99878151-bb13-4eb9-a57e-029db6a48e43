<?php

namespace App\Enums\Code;

/**
 * Password validations code
 * @codeCoverageIgnore
 */
enum PasswordCode: int
{
    case FAIL             = 0;
    case SUCCES           = 1;
    case DUI_ERROR        = 2;
    case CLIENT_404       = 3;
    case PASSCONF_ERROR   = 4;
    case PASS_MIN_ERROR   = 5;
    case PASS_MAX_ERROR   = 6;
    case PASS_ERROR       = 7;
    case PASS_USED        = 8;
    case SERVER_ERROR     = 9;
    case DECRYPT_ERROR    = 10;
}
