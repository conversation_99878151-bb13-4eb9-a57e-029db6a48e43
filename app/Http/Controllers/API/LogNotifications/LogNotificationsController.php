<?php

namespace App\Http\Controllers\API\LogNotifications;

use App\Enums\Message\Generate;
use App\Events\CheckFailedEvent;
use App\Helpers\NotifyProcessHelper;
use App\Http\Controllers\Controller;
use App\Http\Responses\NotificationResponse;
use App\Models\LogNotifications;
use App\Models\NotificationsTemplates;
use App\Models\NotificationVariables;
use App\Services\NotifyService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LogNotificationsController extends Controller
{
    public function __construct(
        private NotifyProcessHelper $notifyProcessHelper,
        private NotifyService $notifyService,
        private NotificationResponse $response
    ) {}

    /**
     * Save firebase token
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function saveFirebaseToken(Request $request): JsonResponse
    {
        try {
            LogNotifications::create([
                "ccapplication_id" => $request->ccapplication_id,
                "token" => $request->token,
                "huawei" => $request->huawei,
                "status" => 'ACT',
            ]);

            return  $this->response->firebaseTokenSaved();
        } catch (Exception $e) {
            return $this->response->serverError();
        }
    }

    /**
     * Send proces notifications.
     */
    public function sendNotification(Request $request): JsonResponse
    {
        try {
            $tokens = [];
            $tokensHuawei = [];

            $template = NotificationsTemplates::find($request->template_id);

            if (empty($template)) {
                CheckFailedEvent::dispatch(Generate::TEMPLATE_NOT_FOUND->value, get_class($this));
                return $this->response->templateNotFound();
            }

            $this->notifyProcessHelper->getTokens($request->type, $request->id, $tokens, $tokensHuawei);

            foreach ($request->variables as $var) {
                $search = '[' . $var["Key"] . ']';
                $template->content = str_replace($search, $var['Value'], $template->content);
            }

            $variablesDeepLink = NotificationVariables::where('notifications_templates_id', $template->id)
                ->pluck('variable')
                ->toArray();

            $paramsDeepLinks = [];

            if ((count($variablesDeepLink) > 0) && $request->keys_deeplink && !empty($request->keys_deeplink)) {
                foreach ($request->keys_deeplink as $key => $value) {
                    if (in_array($key, $variablesDeepLink)) {
                        $paramsDeepLinks[] = [$key => $value];
                    }
                }
            }

            $notificate = $this->notifyService->sendNotificationCustom(
                paramsDeepLinks: $paramsDeepLinks,
                tokens: $tokens,
                tokensHuawei: $tokensHuawei,
                template: $template
            );

            return $this->response->notificationSent(
                firebaseResponse: $notificate[0],
                huwaeiResponse: $notificate[1]
            );
        } catch (Exception $e) {
            return $this->response->serverError();
        }
    }
}
