<?php

namespace App\Http\Controllers\API\Parameterizables;

use Exception;
use App\DTO\ResultData;
use Illuminate\Http\Request;
use App\Helpers\CustomHandler;
use App\Enums\Message\Generate;
use App\Models\Parameterizables;
use App\Services\Configurations;
use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\Parameterizables as ParameterizablesResource;

class ParameterizablesController extends Controller
{
    public function __construct(
        private CustomHandler $exceptionHandler,
        private Configurations $configurations,
        private ApiResponse $response
    ) {}

    /**
     * Get parametrizables resource
     *
     * @param Request $request
     * @return ParameterizablesResource|JsonResponse
     */
    public function parameterizables(Request $request): ParameterizablesResource | JsonResponse
    {
        try {
            $parameterizables = Parameterizables::select(
                'code',
                'parameter_type',
                'parameter',
                'group',
                'storehouse_id'
            )
                ->where('group', $request->group)
                ->get();

            return new ParameterizablesResource($parameterizables);
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }

    /**
     * Get configuration by given key
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function configuration(Request $request): JsonResponse
    {
        $data = new ResultData([
            'result'  => null,
            'status'  => Response::HTTP_OK,
        ]);

        $config = $this->configurations->isAllowedToRequest($request->code)
            ? $this->configurations->getConfigurations($request->code)
            : null;

        $data->result = $config ? $config : Generate::NOT_CONFIGURATION->value;

        return $this->response->success([
            'result' => $data->result,
            'status' => $data->status
        ], $data->status);
    }
}
