<?php

namespace App\Http\Controllers\API\ClientDevice;

use App\Helpers\CustomHandler;
use App\Http\Controllers\Controller;
use App\Http\Resources\ClientDeviceResource;
use App\Models\Client;
use Exception;
use Illuminate\Http\JsonResponse;

class ClientDeviceController extends Controller
{
    public function __construct(private readonly CustomHandler $exceptionHandler)
    {
    }

    /**
     * Get device list.
     */
    public function devices(): ClientDeviceResource | JsonResponse
    {
        try {
            /** @var Client */
            $client = auth('api')->user();

            $clientDevices = $client->devices()->select(
                'device_id',
                'device_name',
                'ultimate_latitud',
                'ultimate_longitud',
                'created_at as ultimate_conexion',
                'online'
            )->get();

            return new ClientDeviceResource($clientDevices);
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
