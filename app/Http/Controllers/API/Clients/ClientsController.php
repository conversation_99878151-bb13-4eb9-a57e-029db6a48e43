<?php

namespace App\Http\Controllers\API\Clients;

use App\DTO\ExternalRequestResponse;
use Exception;
use App\Models\Client;
use App\Dtos\ContactChannels;
use App\Factory\ClientFactory;
use App\Helpers\CustomHandler;
use App\Services\ClientService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClientValidateRequest;
use App\Http\Responses\Auth\ClientResponse;

class ClientsController extends Controller
{
    public function __construct(
        private readonly ClientFactory $clientFactory,
        private ClientService $clientService,
        private CustomHandler $exceptionHandler,
        private ClientResponse $response,
    ) {}

    /**
     * It validates the user before the reset password process.
     * It gets the customer DUI and phone number and verify if the customer exists in the database,
     * if exist is going to get the customer contact information from BAES service and verify the phone number.
     *
     * @param ClientValidateRequest $request
     * @return mixed
     */
    public function validateUser(ClientValidateRequest $request): JsonResponse
    {
        try {
            $client = auth('api')->user();

            if (!$client || $client->dui !== $request->dui) {
                return $this->response->clientNotFound();
            }

            $response = ExternalRequestResponse::fromLegacyExternalResponse(
                data: $this->clientFactory->customeResponse(
                    'CustomerContacts',
                    $client->client_id,
                )
            );

            $externalDatabasePhone = ContactChannels::makeFromLegacyExternalResponse($response)->getFirstPhone();

            return match (true) {
                empty($externalDatabasePhone) => $this->response->missingContactInformation(),
                $externalDatabasePhone !== $request->phone_number => $this->response->invalidPhoneNumber(),
                default => $this->response->validUser($client->client_id, $client->creditcard_application_id)
            };
        } catch (Exception $e) {
            return $this->response->validateUserServerError($e->getMessage());
        }
    }

    /**
     * Remember client nickname
     *
     * @param ClientValidateRequest $request
     * @return JsonResponse
     */
    public function rememberNickname(ClientValidateRequest $request): JsonResponse
    {
        try {
            $client = Client::where('dui', $request->dui)->where('phone_number', $request->phone_number)->first();

            if (empty($client)) {
                return $this->response->rememberNickNameInvalidPhoneNumber();
            }

            $nicknameLog = $this->clientService->saveNicknameLog($client);

            return $this->response->rememberNicknameSuccessfully(
                key: $nicknameLog->key,
                ccApplicationId: $client->creditcard_application_id
            );
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
