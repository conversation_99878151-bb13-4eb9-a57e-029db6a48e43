<?php

namespace App\Http\Controllers\API\Clients;

use App\DTO\FullSessionDTO;
use App\Models\Client;
use App\Dtos\BiometricLogin;
use App\Services\ClientService;
use App\Http\Controllers\Controller;
use App\Http\Resources\SessionResource;
use App\Services\Auth\BiometricLoginService;
use App\Http\Requests\Auth\BiometricLoginRequest;

class BiometricLoginController extends Controller
{
    public function __construct(
        private readonly BiometricLoginService $biometricLoginService,
        private readonly ClientService $clientService,
    ) {
    }

    /**
     * Tries to login a user.
     *
     * @return SessionResource
     */
    public function __invoke(BiometricLoginRequest $request): SessionResource
    {
        $biometricLoginData = BiometricLogin::make(
            $request->signed_data,
            $request->device_id,
            $request->device_name,
            $request,
        );

        $sessionData = $this->biometricLoginService->login($biometricLoginData);

        /** @var Client */
        $client = auth('api')->user();

        $latestLogin = $this->clientService->getLatestLogin($client);

        $fullSession = new FullSessionDTO($sessionData, $client, $latestLogin);

        return SessionResource::make($fullSession);
    }
}
