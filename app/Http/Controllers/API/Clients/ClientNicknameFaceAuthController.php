<?php

namespace App\Http\Controllers\API\Clients;

use App\Models\Client;
use App\Enums\Code\Status;
use App\Models\NicknameLog;
use App\Traits\ActionLogger;
use Illuminate\Http\Request;
use App\Factory\ClientFactory;
use App\Enums\Message\Generate;
use App\Services\ClientService;
use App\Http\Responses\ApiResponse;
use App\DTO\ExternalRequestResponse;
use App\Enums\LogApplicationCode;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;

class ClientNicknameFaceAuthController extends Controller
{
    use ActionLogger;

    public function __construct(
        private readonly ClientFactory $clientFactory,
        private readonly ClientService $clientService,
        private readonly ApiResponse $response
    ) {}

    public function __invoke(Request $request)
    {
        $nicknameLog = NicknameLog::where('key', $request->input('Key'))
            ->where('faceauthentication', '!=', Generate::POSITIVE_FACE_BASE->value)->first();

        if (!$nicknameLog) {
            $unathorizedResponse = new ExternalRequestResponse(
                requestStatusCode: Response::HTTP_OK,
                requestStatusMessage: Generate::succes->value,
                responseStatusCode: Response::HTTP_UNAUTHORIZED,
                responseStatusMessage: Generate::INVALED_KEY->value
            );

            return $this->response->success($unathorizedResponse->toResponseArray());
        }

        $client = Client::find($nicknameLog->client_id);

        $response = ExternalRequestResponse::fromLegacyExternalResponse(
            $this->clientFactory->customeResponse(
                url: 'faceauthentication',
                content: $request->getContent(),
                headers: array_merge($request->headers->all(), ['IpAddressAuth' => $request->ip()])
            )
        );

        $faceAuthenticationStatus = Generate::NEGATIVE_FACE_BASE->value;

        if (
            $response->requestStatusCode == Status::codeExternalOK->value
            && $response->responseStatusCode == Status::codeExternalFind->value
        ) {
            $this->clientService->sendNickname($client);
            $faceAuthenticationStatus = Generate::POSITIVE_FACE_BASE->value;
        }

        $this->clientService->updateNicknameLogFace($nicknameLog->id, $faceAuthenticationStatus);

        $this->logBank($client->client_id, LogApplicationCode::RECOVER_USER);

        return $this->response->success($response->toResponseArray());
    }
}
