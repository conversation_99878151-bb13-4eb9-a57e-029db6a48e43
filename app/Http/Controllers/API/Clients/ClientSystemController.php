<?php

namespace App\Http\Controllers\API\Clients;

use Exception;
use Illuminate\Http\Request;
use App\Helpers\CustomHandler;
use App\Services\ClientService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Responses\Auth\ClientResponse;

class ClientSystemController extends Controller
{
    public function __construct(
        private readonly ClientService $clientService,
        private readonly CustomHandler $exceptionHandler,
        private readonly ClientResponse $response,
    ) {}

    public function store(Request $request): JsonResponse
    {
        try {
            $client = $this->clientService->clientBySystemId($request->ClientId);

            if (!$client) {
                return $this->response->clientSystemNotFound();
            }

            $this->clientService->addClientToSystem($request->SystemCode, $client->id);

            return $this->response->successfullyAddedToSystem();
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }

    public function destroy(Request $request): JsonResponse
    {
        try {
            $client = $this->clientService->clientBySystemId($request->ClientId);

            if (!$client) {
                return $this->response->clientSystemNotFound();
            }

            $this->clientService->removeClientFromSystem($request->SystemCode, $client);

            return $this->response->successfullyRemovedFromSystem();
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
