<?php

namespace App\Http\Controllers\API\Clients;

use App\Helpers\Box;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserRequest;
use App\Http\Resources\UserDeletedResource;
use App\Http\Responses\ApiResponse;
use App\Inputs\DeleteUserInput;
use App\Models\Client;
use App\Services\RegisterNewUserService;
use App\Tasks\DeleteUserTask;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UsersController extends Controller
{
    /**
     * Auth manager.
     */
    private Guard $auth;

    public function __construct(
        private readonly AuthManager $authManager,
        private readonly ApiResponse $apiResponse
    ) {
        $this->auth = $authManager->guard('api');
    }

    /**
     * Registers a new user.
     */
    public function store(RegisterNewUserService $registerNewUserService, StoreUserRequest $request): JsonResponse
    {
        $registerNewUserService->register($request->customer_id, $request->creditcard_application_id);

        return $this->apiResponse->success(status: Response::HTTP_CREATED);
    }

    /**
     * Deregister a user.
     *
     * @param DeleteUserTask $deleteUserTask
     * @return \Illuminate\Http\JsonResponse
     */
    public function remove(DeleteUserTask $deleteUserTask)
    {
        /** @var Client */
        $user = $this->auth->user();

        // TODO: Validate if the user does not have a credit card assigned
        // or they have an approved ticekt to delete the account.

        $deleteUserTask
            ->withUser($user)
            ->do();

        return $this->apiResponse->success(status: Response::HTTP_NO_CONTENT);
    }

    /**
     * Deletes a user.
     *
     * Only available in non-production envs.
     *
     * @param Application $app
     * @param DeleteUserTask $task
     * @param int $id
     */
    public function destroy(
        Application $app,
        DeleteUserTask $task,
        int $id,
    ) {
        if ($app->environment('production')) {
            return $this->apiResponse->success(status: Response::HTTP_NOT_FOUND);
        }

        $task
            ->withUser(Client::findOrFail($id))
            ->forceDelete()
            ->do();

        return $this->apiResponse->success(status: Response::HTTP_NO_CONTENT);
    }

    /**
     * Deletes one or more users.
     *
     * Only available in non-production envs.
     *
     * @param Application $app
     * @param DeleteUserTask $task
     * @param Request $request
     */
    public function destroyQuery(
        Application $app,
        DeleteUserTask $task,
        DeleteUserInput $input,
        Request $request,
    ) {
        if ($app->environment('production')) {
            return $this->apiResponse->notFound();
        }

        $users = $input->toUsers(
            Box::put($request->query('id')),
            Box::put($request->query('customer_id')),
            Box::put($request->query('dui')),
        );
        foreach ($users as $user) {
            $task
                ->withUser($user)
                ->forceDelete()
                ->do();
        }

        return UserDeletedResource::collection($users);
    }
}
