<?php

namespace App\Http\Controllers\API\Clients;

use Exception;
use App\Models\Client;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Helpers\CustomHandler;
use App\Enums\Message\Generate;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\Client\ClientFullProfileResource;
use App\Http\Responses\ApiResponse;

class ClientProfileController extends Controller
{
    public function __construct(
        private readonly  CustomHandler $exceptionHandler,
        private readonly ApiResponse $response
    ) {}

    public function getProfile(): Responsable|JsonResponse
    {
        try {
            $client = Client::where('id', auth('api')->user()->id)->firstOrFail();

            $client->load('profileImages');

            return $this->response->success(
                data: new ClientFullProfileResource($client),
                status: Response::HTTP_OK
            );
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
