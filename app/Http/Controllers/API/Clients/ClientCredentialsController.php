<?php

namespace App\Http\Controllers\API\Clients;

use Exception;
use App\Models\Client;
use App\Services\ClientService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LockUnlockClientCredentialsRequest;
use App\Http\Responses\Auth\ClientCredentialsResponse;

class ClientCredentialsController extends Controller
{
    public function __construct(
        private readonly ClientService $clientService,
        private readonly ClientCredentialsResponse $response
    ) {}

    public function lock(LockUnlockClientCredentialsRequest $request): JsonResponse
    {
        try {
            $client = $this->clientService->getClientFromRequest($request->IDENTIFIER, $request->VALUE);

            if (empty($client)) {
                return $this->clientNotFoundByIdentifier($request->IDENTIFIER, $request->VALUE);
            }

            $this->clientService->updateStatus($client, Client::DISABLE);

            return $this->response->lockClientSuccessful();
        } catch (Exception $e) {
            return $this->response->credentialsInternalServerError($e->getMessage());
        }
    }

    public function unlock(LockUnlockClientCredentialsRequest $request): JsonResponse
    {
        try {
            $client = $this->clientService->getClientFromRequest($request->IDENTIFIER, $request->VALUE);

            if (empty($client)) {
                return $this->clientNotFoundByIdentifier($request->IDENTIFIER, $request->VALUE);
            }

            $this->clientService->updateStatus($client, Client::ENABLE);

            return $this->response->unlockClientSuccessful();
        } catch (Exception $e) {
            return $this->response->credentialsInternalServerError($e->getMessage());
        }
    }

    /**
     * Lock client by face auth.
     */
    public function lockClientByFaceAuth(): JsonResponse
    {
        try {
            $client = Client::find(auth('api')->user()->id);

            $this->clientService->lockClientByFaceAuth($client);

            return $this->response->lockFaceAuthSuccessful();
        } catch (Exception $e) {
            return $this->response->lockFaceAuthInternalServerError();
        }
    }

    private function clientNotFoundByIdentifier(string $identifier, string $value): JsonResponse
    {
        return match ($identifier) {
            'CLIENT_ID' => $this->response->clientNotFoundByID($value),
            'DUI' => $this->response->clientNotFoundByDUI($value)
        };
    }
}
