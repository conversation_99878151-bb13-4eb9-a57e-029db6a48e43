<?php

namespace App\Http\Controllers\API\Clients;

use App\Http\Controllers\Controller;
use App\Http\Requests\Clients\RefreshTokenRequest;
use App\Http\Resources\ExternalTokenResource;
use App\Models\Client;
use App\Tasks\Clients\RefreshTokenForUserTask;

class RefreshTokenController extends Controller
{
    /**
     * Refreshes an external token.
     *
     * @return ExternalTokenResource
     */
    public function __invoke(RefreshTokenForUserTask $task, RefreshTokenRequest $request)
    {
        return $task
            ->withUser(Client::byBaesRefreshToken($request->refresh_token))
            ->withDeviceId($request->device_id)
            ->doInto(ExternalTokenResource::class);
    }
}
