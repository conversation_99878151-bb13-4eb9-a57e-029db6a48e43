<?php

namespace App\Http\Controllers\API\Clients;

use App\DTO\FullSessionDTO;
use App\Models\Client;
use App\Inputs\LoginInput;
use App\Services\Crypt\Rsa;
use App\Http\Controllers\Controller;
use App\Events\WrongCredentialsEvent;
use App\Http\Resources\SessionResource;
use App\Http\Requests\Auth\LoginRequest;
use App\Services\Auth\VBankLoginService;
use App\Exceptions\Auth\WrongCredentials;
use App\Services\Auth\PasswordLoginService;
use App\Exceptions\Auth\CantDecryptException;
use App\Events\CantDecryptCredentialsOnLoginEvent;
use App\Services\ClientService;

class LoginController extends Controller
{
    public function __construct(
        private readonly Rsa $rsa,
        private readonly PasswordLoginService $loginService,
        private readonly VBankLoginService $vBankLoginService,
        private readonly ClientService $clientService,
    ) {
    }

    public function __invoke(LoginInput $input, LoginRequest $request): SessionResource
    {
        $this->validateDecryption($request);

        $request->merge([
            'nickname' => $this->rsa->decrypt($request->nickname),
            'password' => $this->rsa->decrypt($request->password),
        ]);

        $loginData = $input->toLogin($request);

        $client = $this->findClient($request->nickname);

        if (empty($client)) {
            $this->handleWrongCredentials($loginData);
        }

        $sessionData = $this->createSession($loginData, $client);

        return SessionResource::make($sessionData);
    }

    private function validateDecryption(LoginRequest $request): void
    {
        if (
            !$this->rsa->canDecrypt($request->nickname) ||
            !$this->rsa->canDecrypt($request->password)
        ) {
            CantDecryptCredentialsOnLoginEvent::dispatch($request);
            throw new CantDecryptException();
        }
    }

    private function findClient(string $nickname): ?Client
    {
        return Client::where('nickname', $nickname)
            ->orWhere('nickname_vbank', $nickname)
            ->first();
    }

    private function handleWrongCredentials($loginData): void
    {
        WrongCredentialsEvent::dispatch($loginData->request, $loginData->credentials->nickname);

        throw new WrongCredentials(
            $loginData->request->ip(),
            $loginData->credentials->nickname,
        );
    }

    private function createSession($loginData, Client $client): FullSessionDTO
    {
        $session = match (true) {
            (bool) $client->homologated => $this->vBankLoginService->login($loginData, $client),
            default => $this->loginService->login($loginData, $client)
        };

        $latestLogin = $this->clientService->getLatestLogin($client);

        return new FullSessionDTO($session, $client, $latestLogin);
    }
}
