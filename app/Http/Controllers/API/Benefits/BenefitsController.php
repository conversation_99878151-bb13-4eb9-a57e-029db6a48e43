<?php

namespace App\Http\Controllers\API\Benefits;

use App\Http\Controllers\Controller;
use App\Http\Resources\BenefitResource;
use App\Models\Benefit;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\Request;
use League\HTMLToMarkdown\HtmlConverter;

class BenefitsController extends Controller
{
    /**
     * Returns a list of benefits.
     *
     * @param HtmlConverter $markdownConverter
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(HtmlConverter $markdownConverter, Request $request): Responsable
    {
        $benefits = Benefit::all();
        if ($request->query('format') === 'markdown') {
            $benefits->map(function ($benefit) use ($markdownConverter) {
                $benefit->description = $markdownConverter->convert($benefit->description);

                return $benefit;
            });
        }

        return BenefitResource::collection($benefits);
    }
}
