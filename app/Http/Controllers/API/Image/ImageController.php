<?php

namespace App\Http\Controllers\API\Image;

use App\Http\Controllers\Controller;
use App\Models\Storehouse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\Response;

class ImageController extends Controller
{
    /**
     * Decode images from the database and return response as file
     * 404 if the image does not exist
     * @param Request $request
     * @param int $id
     * @return HttpResponse
     */
    public function images(Request $request, $id): HttpResponse
    {
        try {
            $image = Storehouse::find($id);

            $ctype = match ($image->extension) {
                "git" => "image/git",
                "png" => "image/png",
                "jpeg","jpg" => "image/jpeg",
                "svg" => "image/svg+xml",
                default => "image/png"
            };

            $decoded = base64_decode($image->data);
            $response = Response::make($decoded, HttpResponse::HTTP_OK);
            $response->header("Content-Type", $ctype);
            return $response;
        } catch (\Exception $th) {
            abort(HttpResponse::HTTP_NOT_FOUND);
        }
    }
}
