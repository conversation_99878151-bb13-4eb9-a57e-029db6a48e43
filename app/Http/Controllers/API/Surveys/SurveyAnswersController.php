<?php

namespace App\Http\Controllers\API\Surveys;

use App\Dtos\Answer;
use App\Http\Responses\ApiResponse;
use App\Http\Controllers\Controller;
use App\Tasks\Surveys\CreateAnswersTask;
use App\Http\Requests\SurveyAnswersRequest;
use App\Tasks\Auth\GetCurrentCustomerIdTask;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class SurveyAnswersController extends Controller
{

    public function __construct(private readonly ApiResponse $response)
    {
    }

    /**
     * Stores the answers for a survey
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(
        GetCurrentCustomerIdTask $getCurrentCustomerIdTask,
        CreateAnswersTask $createAnswersTask,
        SurveyAnswersRequest $request,
        int $id,
    ): JsonResponse {
        $customerId = $getCurrentCustomerIdTask
            ->withDui($request->header('DuiAuth'))
            ->do();

        $createAnswersTask
            ->withAnswers(Answer::manyFromRequest($request))
            ->withCustomerId($customerId)
            ->do();

        return $this->response->success(status: Response::HTTP_CREATED);
    }
}
