<?php

namespace App\Http\Controllers\API\Surveys;

use App\Http\Controllers\Controller;
use App\Http\Resources\SurveyResource;
use App\Models\Survey;
use Illuminate\Http\Request;

class SurveysController extends Controller
{
    public function show($id)
    {
        return new SurveyResource(Survey::findOrFail($id)->load('questions.options'));
    }

    public function index(Request $request)
    {
        return new SurveyResource(
            Survey::findByNameOrFail($request->query('name'))->load('questions.options')
        );
    }
}
