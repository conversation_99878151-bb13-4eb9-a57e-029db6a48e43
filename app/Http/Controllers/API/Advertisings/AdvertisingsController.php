<?php

namespace App\Http\Controllers\API\Advertisings;

use App\Helpers\CustomHandler;
use App\Http\Controllers\Controller;
use App\Http\Resources\Advertisings as AdvertisingsResource;
use App\Models\Advertisings;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdvertisingsController extends Controller
{
    private CustomHandler $exceptionHandler;

    public function __construct(CustomHandler $customHandler)
    {
        $this->exceptionHandler = $customHandler;
    }

    /**
     * Get Advertising resource collection
     *
     * @param Request $request
     * @return AdvertisingsResource|JsonResponse
     */
    public function advertisings(Request  $request): AdvertisingsResource | JsonResponse
    {
        try {
            $day = now();
            return new AdvertisingsResource(
                Advertisings::select(
                    'name',
                    'body',
                    DB::raw("CONCAT( '".(url('storage'))."/', image) as image"),
                    'color',
                    'url',
                    'storehouse_id',
                    'active_url'
                )
                  ->where('group', $request->group)
                  ->where('start_date', '<=', $day)
                  ->where('finish_date', '>', $day)
                  ->get()
            );
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
