<?php

namespace App\Http\Controllers\API\ClientPassword;

use Exception;
use App\Enums\Message\Generate;
use App\Events\CheckFailedEvent;
use App\Factory\ClientFactory;
use App\Http\Controllers\Controller;
use App\Http\Requests\Password\PassworRequest;
use App\Http\Responses\Auth\PasswordResponse;
use App\Models\Client;
use App\Services\ClientPasswordService;
use App\Services\ClientService;
use App\Services\Configurations;
use App\Services\Crypt\Rsa;
use App\Services\PasswordValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Crypto\Rsa\Exceptions\CouldNotDecryptData;

class ClientPasswordController extends Controller
{
    public function __construct(
        private readonly Rsa $rsa,
        private readonly PasswordValidationService $passwordValidation,
        private readonly ClientPasswordService $passwordService,
        private readonly ClientService $clientService,
        private readonly ClientFactory $clientFactory,
        private readonly PasswordResponse $passwordResponse,
    ) {
        $this->passwordValidation->setClientFactory($this->clientFactory);
    }

    /**
     * Reset the first client password
     */
    public function resetFirstPassword(Request $request): JsonResponse
    {
        try {
            /** @var Client */
            $client = auth('api')->user();

            $password = $this->rsa->decrypt($request->password);

            if (Hash::check($password, $client->password)) {
                $errorMessage = (string) Configurations::getInstance()->getConfigurations('MENSAJE_PASSWORD_USADA');
                $response =  $this->passwordResponse->firstCurrentPasswordUsed($errorMessage);
            } elseif (!$this->passwordValidation->validatePasswordRules($request, $client->client_id)) {
                $errorMessage = $this->passwordValidation->validationError();
                $response = $this->passwordResponse->passwordValidationFailed($errorMessage);
            } else {
                $this->passwordService->updatePassword($client, $password);
                $response = $this->passwordResponse->firstPasswordUpdatedSuccessfully();
            }

            return $response;
        } catch (Exception $e) {
            return $this->passwordResponse->firstPasswordInternalServerError();
        }
    }

    /**
     * Verifies the valid password format for the SSF requirement.
     *
     * @param PassworRequest $request
     * @return JsonResponse
     */
    public function validatePassword(PassworRequest $request): JsonResponse
    {
        try {
            $client = Client::where('dui', $request->dui)->latest()->first();
            $password = $this->rsa->decrypt($request->password);

            if (empty($client)) {
                $errorMessage = Generate::NOT_CUSTOMER_FOUND_DUI->value;
                CheckFailedEvent::dispatch($errorMessage, -1);
                $response =  $this->passwordResponse->validatePasswordCustomerNotFound($errorMessage);
            } elseif (
                $this->passwordService->verifyExistingPassword($client, $password) ||
                Hash::check($password, $client->password)
            ) {
                $errorMessage = Configurations::getInstance()->getConfigurations('MENSAJE_PASSWORD_USADA');
                CheckFailedEvent::dispatch($errorMessage, -1);
                $response = $this->passwordResponse->currentPasswordUsed(errorMessage: $errorMessage);
            } elseif (!$this->passwordValidation->validatePasswordRules($request, $client->client_id)) {
                $result = Generate::INVALID_PASSWORD_FORMAT->value;
                CheckFailedEvent::dispatch($result, get_class($this));
                $response = $this->passwordResponse->validatePasswordInvalidFormat(
                    code: $this->passwordValidation->validationCode(),
                    errorMessage: $this->passwordValidation->validationError()
                );
            } else {
                $response =  $this->passwordResponse->validPassword();
            }

            return $response;
        } catch (Exception $e) {
            return $this->passwordResponse->validatePasswordInternalServerError();
        }
    }

    /**
     * Reset the user password after the valid period of time
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function resetPassword(Request $request): JsonResponse
    {
        try {
            $attrs = [
                'dui' => $request->dui,
            ];

            $rules = [
                'dui' => 'required',
            ];

            $password = $this->rsa->decrypt($request->password);

            $client = Client::where('dui', $request->dui)->latest()->first();
            if (empty($client) && !empty($request->dui)) {
                $errorMessage = Str::of(Generate::NOT_CLIENT_WHIT_DUI->value)
                    ->swap([":valor" => $request->dui]) . '.';
                CheckFailedEvent::dispatch($errorMessage, get_class($this));
                $response = $this->passwordResponse->customerNotFound($errorMessage);
            } elseif (!$this->passwordValidation->validateAditionalData($attrs, $rules)) {
                $errorMessage = $this->passwordValidation->validationError();
                CheckFailedEvent::dispatch($errorMessage, get_class($this));
                $response = $this->passwordResponse->missingFields(
                    code: $this->passwordValidation->validationCode(),
                    errorMessage: $errorMessage,
                );
            } elseif (!$this->passwordValidation->validatePasswordRules($request, $client->client_id)) {
                $errorMessage = Generate::INVALID_PASSWORD_FORMAT->value;
                CheckFailedEvent::dispatch($errorMessage, get_class($this));
                $response = $this->passwordResponse->invalidPasswordFormat(
                    $this->passwordValidation->validationCode(),
                    $this->passwordValidation->validationError()
                );
            } else {
                $this->passwordService->updatePassword($client, $password);
                $response = $this->passwordResponse->passwordUpdatedSuccessfully();
            }

            return $response;
        } catch (CouldNotDecryptData $e) {
            return $this->passwordResponse->cannotDecryptData();
        } catch (Exception $e) {
            return $this->passwordResponse->resetPasswordInternalServerError($e->getMessage());
        }
    }
}
