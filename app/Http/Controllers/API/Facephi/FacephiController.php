<?php

namespace App\Http\Controllers\API\Facephi;

use App\Services\Configurations;
use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;

class FacephiController extends Controller
{
    public function __construct(private readonly ApiResponse $response)
    {
    }

    /**
     * Get match score facephi paramater
     */
    public function score(): JsonResponse
    {
        $score =  Configurations::getInstance()->getConfigurations('SCORE_FACEPHI');

        return $this->response->success([
            'score' => $score,
            'status' => Response::HTTP_OK
        ]);
    }
}
