<?php

namespace App\Http\Controllers\API\ServicesRouter;

use Exception;
use App\Services\Log;
use Illuminate\Http\Request;
use App\Factory\ClientFactory;
use App\Helpers\CustomHandler;
use App\Http\Controllers\Controller;

class ServicesRouterController extends Controller
{
    private CustomHandler $exceptionHandler;

    public function __construct(
        private readonly ClientFactory $clientFactory,
        private readonly Log $log,
        CustomHandler $exceptionHandler
    ) {
        $this->exceptionHandler = $exceptionHandler;
    }

    public function postServices(Request $request)
    {
        return $this->responseService($request->server->get('REQUEST_URI'), $request->parameters, $request);
    }

    public function getServices(Request $request)
    {
        return $this->responseService($request->server->get('REQUEST_URI'), $request->parameters, $request);
    }

    public function putServices(Request $request)
    {
        return $this->responseService($request->server->get('REQUEST_URI'), $request->parameters, $request);
    }

    public function delServices(Request $request)
    {
        return $this->responseService($request->server->get('REQUEST_URI'), $request->parameters, $request);
    }


    public function responseService(string $url, mixed $parameters, Request $request): mixed
    {
        try {
            $response = $this->clientFactory->customeResponse(
                $url,
                $parameters,
                $request->url(),
                $request->fullUrl(),
                $request->getContent(),
                array_merge(
                    $request->headers->all(),
                    ['IpAddressAuth' => $request->ip()],
                )
            );

            $httpStatus = $response["status"];
            if ($httpStatus == 200 && array_key_exists("Status", $response["content"])) {
                $baesHttpCode = $response["content"]["Status"]["RequestStatus"]["Code"];

                // Required when the token was valid but at the time of requesting a BAES service it's already invalid
                if ($baesHttpCode == 401) {
                    $this->log->debug("HTTP Status Code changed", [
                        'HTTP Status' => $httpStatus,
                        'BAES HTTP Status' => $baesHttpCode,
                    ]);
                    $httpStatus = 401;
                }
            }

            return Response($response["content"], $httpStatus);
        } catch (Exception $ex) {
            $this->log->error("Unexpected problem on external call made " . $ex->getMessage());
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
