<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Repos\RefreshTokenRepo;
use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Application;
use App\Repos\GetCustomerReferencesRepo;
use App\Repos\CreateCustomerAddressesRepo;
use App\Repos\DeleteCustomerAddressesRepo;
use App\Repos\UpdateCustomerAddressesRepo;
use App\Repos\CreateCustomerReferencesRepo;
use App\Repos\DeleteCustomerReferencesRepo;
use App\Repos\UpdateCustomerReferencesRepo;

class ExternalRoutesController extends Controller
{
    protected array $repos = [
        'refreshToken' => RefreshTokenRepo::class,
        'updateCustomerAddresses' => UpdateCustomerAddressesRepo::class,
        'createCustomerAddresses' => CreateCustomerAddressesRepo::class,
        'deleteCustomerAddresses' => DeleteCustomerAddressesRepo::class,
        'createCustomerReferences' => CreateCustomerReferencesRepo::class,
        'deleteCustomerReferences' => GetCustomerReferencesRepo::class,
        'updateCustomerReferences' => UpdateCustomerReferencesRepo::class,
        'getCustomerReferences' => DeleteCustomerReferencesRepo::class,
    ];

    public function __construct(protected Application $app, private ApiResponse $response)
    {
    }

    public function __call($calledMethod, $arguments): JsonResponse
    {
        if (!array_key_exists($calledMethod, $this->repos)) {
            return $this->response->notFound();
        }

        return $this->call($this->app->make($this->repos[$calledMethod]));
    }

    private function call($repo)
    {
        // Ensure that the request object is new for the cycle even if the controller
        // was cobbled together at an earlier time, cycle, or outside.
        $request = $this->app->make(Request::class);

        $result = $repo
            ->prepare($request->all())
            ->addHeaders(
                collect($request->headers->all())
                    ->map(fn($arr) => $arr[0])
                    ->all()
            )
            ->fetchRaw();

        return $this->response->response($result, $repo->getStatus());
    }
}
