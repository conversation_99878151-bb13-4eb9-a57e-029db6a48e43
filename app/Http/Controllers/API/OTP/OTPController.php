<?php

namespace App\Http\Controllers\API\OTP;

use App\DTO\ExternalRequestResponse;
use App\Enums\Message\OTPMessage;
use App\Enums\OTP\ExternalValidate;
use App\Events\CheckFailedEvent;
use App\Factory\ClientFactory;
use App\Helpers\CustomHandler;
use App\Helpers\NotifySender;
use App\Helpers\OTP;
use App\Helpers\OTPSharedKey;
use App\Http\Controllers\Controller;
use App\Http\Requests\OTP\SendOtpRequest;
use App\Http\Requests\OTP\ValidateOtpRequest;
use App\Http\Responses\Auth\OTPResponse;
use App\Models\Client;
use App\Models\NicknameLog;
use App\Models\OtpLogs;
use App\Services\ClientService;
use App\Services\OTPService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class OTPController extends Controller
{
    public function __construct(
        private readonly OTPSharedKey $otpSharedKey,
        private readonly CustomHandler $exceptionHandler,
        private readonly OTPService $otpService,
        private readonly ClientService $clientService,
        private readonly ClientFactory $clientFactory,
        private readonly OTPResponse $otpResponse,
    ) {}

    /**
     * Send OTP code by given phone number or email and verify availability
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendOTPCode(SendOtpRequest $request): JsonResponse
    {
        try {
            $userID = 0;
            $authorization = OtpLogs::APIKEY;

            if (auth('api')->check()) {
                $userID = auth('api')->user()->id;
                $authorization = OtpLogs::SESSION;
            }

            $type   = $request->type;
            $value  = $request->value;
            $chanel = $this->otpService->getChanel($request->chanel);

            if (
                $this->otpService->generationHasFailedByEmitter($authorization, $value, $userID) ||
                $this->otpService->generationHasFailedByTime($value)
            ) {
                return $this->otpResponse->otpGenerationFailed(
                    message: $this->otpService->getResult(),
                    code: $this->otpService->getCode(),
                    status: $this->otpService->getStatus()
                );
            }

            $dataOtpCode  = [
                'SystemCode' => 'SYS_APP01',
                'Mode' => 'OTP_GEN_SEND',
                'ContactType' => $type,
                'ContactValue' => $value,
                'Chanel' => $chanel
            ];

            $body = OTP::getOtpCode($dataOtpCode);

            $externalResponse = $this->clientFactory->customeResponse(
                url: "sendotpcode2",
                content: $body,
                headers: array_merge($request->headers->all(), ['IpAddressAuth' => $request->ip()]),
            );

            $response = ExternalRequestResponse::fromLegacyExternalResponse($externalResponse);

            $sharedKey = data_get($response->data, 'SharedKey');
            if (empty($sharedKey)) {
                $message =  $response->responseStatusMessage ?: OTPMessage::WAIT_OTP_TIME->value;
                $result = $this->otpResponse->emptySharedKey($message);
            } else {
                $otpLog = new OtpLogs();
                $otpLog->otpcode = 1;
                $otpLog->sharedkey = $sharedKey;
                $otpLog->identifier = Str::uuid()->toString();
                $otpLog->type = $type;
                $otpLog->value = $value;
                $otpLog->save();

                $customSharedKey = $this->otpSharedKey->makeSharedKey("{$otpLog->identifier};$value;$authorization;$userID");
                $result = $this->otpResponse->otpGeneratedSuccessfully(sharedKey: $customSharedKey);
            }

            return $result;
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }

    /**
     * Validate OTP code by given shared key
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function otpValidateCode(ValidateOtpRequest $request): JsonResponse
    {
        try {
            return $this->validateAndUpdateOtp($request);
        } catch (Exception $e) {
            return $this->otpResponse->validateOTPServerError();
        }
    }

    /**
     * Validate OTP for nickname recover and send nickname by email if success.
     *
     * @param Request $request
     * @return void
     */
    public function otpValidateCodeForNickname(ValidateOtpRequest $request): JsonResponse
    {
        try {
            $nicknameLog = NicknameLog::where('key', $request->Key)
                ->where('faceauthentication', '!=', 'POSITIVE')
                ->first();

            if (empty($nicknameLog)) {
                CheckFailedEvent::dispatch(OTPMessage::INVALID_SHARED_KEY->value, get_class($this));
                return $this->otpResponse->validateOTPEmptyNicknameLog();
            }

            return $this->validateAndUpdateOtp($request, $nicknameLog);
        } catch (Exception $e) {
            return $this->otpResponse->validateOTPServerError();
        }
    }

    /**
     * Validate given otp code and custom shared key.
     */
    // FIX: Too many returns
    private function validateAndUpdateOtp(Request $request, ?NicknameLog $nicknameLog = null): JsonResponse
    {
        [$identifier, $emitter, $authorization, $userID] = $this->otpSharedKey->recoverSharedKey($request->SharedKey, ';');

        $otp = OtpLogs::where([
            'identifier' => $identifier,
            'value' => $emitter,
            'verified' => OtpLogs::NOT_VERIFIED
        ])->first();

        if (empty($otp) || (empty($identifier) && empty($emitter))) {
            CheckFailedEvent::dispatch(OTPMessage::INVALID_SHARED_KEY->value, -1);
            $response = $this->otpResponse->validateOTPCodeMissingParts();
        } elseif (!$this->otpService->otpStillValid(createdAt: $otp->created_at)) {
            CheckFailedEvent::dispatch(OTPMessage::EXPIRED->value, -1);
            $response = $this->otpResponse->validateOTPExpiredCode();
        } elseif ($this->otpService->exceededAttempts($otp->attempts)) {
            $this->otpService->blockEmitter($authorization, $emitter, $userID);
            $message = $this->otpService->getBlockMessage($authorization);
            CheckFailedEvent::dispatch($message, -1);
            $response = $this->otpResponse->validateOTPExceededAttemps(
                message: $message,
                code: $this->otpService->codeByAuthorization($authorization),
            );
        }

        if (isset($response)) {
            return $response;
        }

        $dataOtpCodeParameter  = $this->clientFactory->convertParameterToString([
            'SystemCode' => 'SYS_APP01',
            'OTPCode' => $request->OTPCode,
            'SharedKey' =>  $otp->sharedkey
        ]);

        $dataOtpCode = $this->clientFactory->customeResponse(
            url: "verifyOtpCode",
            parameters: $dataOtpCodeParameter,
            headers: array_merge($request->headers->all(), ['IpAddressAuth' => $request->ip()])
        );

        $externalResponse = ExternalRequestResponse::fromLegacyExternalResponse($dataOtpCode);

        if ($externalResponse->responseStatusCode !== ExternalValidate::SUCCESS->value) {
            $otp->attempts  = $otp->attempts + 1;
            $otp->update();

            return $this->otpResponse->validateOTPInvalidCode();
        }

        $otp->verified  = OtpLogs::VERIFIED;
        $otp->update();

        if (isset($nicknameLog)) {
            $client = Client::find($nicknameLog->client_id);
            self::sendNickname($client);
        }

        return $this->otpResponse->validateOTPSuccessful();
    }


    /**
     * Send Email whith client nickname
     *
     * @param Client $client
     * @return void
     */
    private function sendNickname(Client $client): void
    {
        NotifySender::init($this->clientFactory)
            ->client($client)
            ->template('APP_NOTUSU')
            ->chanelID(NotifySender::EMAIL)
            ->subjectByTemplateCode()
            ->alternativeChanelsIDs('[1,2]')
            ->addParameter('User', $client->nickname)
            ->send();

        $this->clientService->addClientHistory(
            client: $client,
            type: 'MAIL',
            message: 'Su usuario es: ' . $client->nickname
        );
    }
}
