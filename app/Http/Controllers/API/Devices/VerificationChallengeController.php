<?php

namespace App\Http\Controllers\API\Devices;

use App\Http\Controllers\Controller;
use App\Http\Requests\VerificationChallengeRequest;
use App\Http\Resources\VerificationChallengeResource;
use App\Models\Device;
use App\Tasks\Devices\GenerateVerificationChallengeTask;

class VerificationChallengeController extends Controller
{
    /**
     * Generates a new verification challenge for a device and returns
     * it encrypted.
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(
        GenerateVerificationChallengeTask $task,
        VerificationChallengeRequest $request,
        string $id,
    ) {
        $device = Device::active()
            ->where('device_id', $request->id)
            ->latest()
            ->firstOrFail();

        return $task
            ->withDevice($device)
            ->doInto(VerificationChallengeResource::class);
    }
}
