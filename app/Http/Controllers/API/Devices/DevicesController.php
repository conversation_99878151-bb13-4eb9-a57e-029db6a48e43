<?php

namespace App\Http\Controllers\API\Devices;

use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\RemoveDeviceRequest;
use App\Models\Client;
use Symfony\Component\HttpFoundation\Response;

class DevicesController extends Controller
{
    public function __construct(private readonly ApiResponse $repsonse)
    {
    }

    /**
     * Deletes a device.
     */
    public function destroy(RemoveDeviceRequest $request, string $id): JsonResponse
    {
        /** @var Client */
        $client = auth('api')->user();

        $client->devices()->where('device_id', $id)->first()->delete();

        return $this->repsonse->success(status: Response::HTTP_NO_CONTENT);
    }
}
