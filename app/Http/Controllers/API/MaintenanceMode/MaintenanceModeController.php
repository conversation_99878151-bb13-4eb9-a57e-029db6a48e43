<?php

namespace App\Http\Controllers\API\MaintenanceMode;

use App\Helpers\CustomHandler;
use App\Http\Controllers\Controller;
use App\Http\Responses\MaintenanceResponse;
use App\Models\ApiKeys;
use App\Services\MaintenanceService;
use Exception;
use Illuminate\Http\JsonResponse;

class MaintenanceModeController extends Controller
{
    public function __construct(
        private CustomHandler $exceptionHandler,
        private MaintenanceService $maintenanceService,
        private MaintenanceResponse $response,
    ) {}

    /**
     * Maintenance mode
     *
     * The following function verifies if the APP is about to be in maintenance or if it is already in maintenance.
     * A message will be returned to the client if it is in maintenance.
     * @return JsonResponse
     */
    public function maintenanceMode(): JsonResponse
    {
        try {
            $apikey = ApiKeys::where('syncup', ApiKeys::SYNC_UP)->first();

            $apikeyCode = bin2hex(bin2hex(base64_encode(base64_encode($apikey->apikey))));

            if ($maintenance = $this->maintenanceService->getMaintenanceModeIfExists()) {
                return $this->response->isUnderMaintenance(
                    apiKeyCode: $apikeyCode,
                    message: $maintenance->client_message
                );
            }

            return $this->response->isAvailable($apikeyCode);
        } catch (Exception $ex) {
            return $this->exceptionHandler->exceptionHandler($ex);
        }
    }
}
