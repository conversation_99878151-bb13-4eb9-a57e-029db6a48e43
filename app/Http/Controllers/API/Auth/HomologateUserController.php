<?php

namespace App\Http\Controllers\API\Auth;

use Exception;
use App\Models\Client;
use Illuminate\Http\Request;
use App\Events\CheckFailedEvent;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Tasks\Auth\CreateVBankUserTask;
use App\Tasks\ValidateVBankAccountTask;
use App\Tasks\Auth\SendVBankUsernameEmailTask;
use App\Exceptions\Auth\ClientNotFoundException;
use App\Exceptions\Auth\CouldNotCreateVBankUser;
use App\Http\Responses\Auth\HomologateUserResponse;

class HomologateUserController extends Controller
{
    public function __construct(
        private readonly HomologateUserResponse $response,
        private readonly ValidateVBankAccountTask $validateVBankAccountTask,
        private readonly SendVBankUsernameEmailTask $sendVBankUsernameEmailTask,
        private readonly CreateVBankUserTask $createVBankUserTask,
    ) {
        //
    }

    public function __invoke(Request $request): JsonResponse
    {
        try {
            /** @var Client $client */
            $client = $request->user('api');

            if ($client->homologated) {
                return $this->response->clientAlreadyHomologated();
            }

            $deviceId = $request->headers->get('DeviceAuth');

            $this->handleHomologation($client, $deviceId);

            return $this->response->clientHomologatedSuccessfully();
        } catch (Exception $exception) {
            return $this->handleExceptions($exception);
        }
    }

    private function handleHomologation(Client $client, string $deviceId): void
    {
        $customerId = $client->client_id;

        $vBankAccount = $this->validateVBankAccountTask
            ->withCustomerId($customerId)
            ->do();

        if ($vBankAccount) {
            $this->sendVBankUsernameEmailTask
                ->withCustomerId($customerId)
                ->withDeviceId($deviceId)
                ->do();

            $client->nickname_vbank = $vBankAccount->username;
        } else {
            $vBankAccountUsername = $this->createVBankUserTask
                ->withCustomerId($customerId)
                ->withDeviceId($deviceId)
                ->do();

            $client->nickname_vbank = $vBankAccountUsername;
        }

        $client->homologated = true;
        $client->save();
    }

    private function handleExceptions(Exception $exception): JsonResponse
    {
        CheckFailedEvent::dispatch($exception->getMessage(), get_class($this));

        return match (get_class($exception)) {
            CouldNotCreateVBankUser::class => $this->response->couldNotCreateVBankUser(),
            ClientNotFoundException::class => $this->response->clientNotFound(),
            default => $this->response->unexpectedError()
        };
    }
}
