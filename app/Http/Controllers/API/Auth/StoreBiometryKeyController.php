<?php

namespace App\Http\Controllers\API\Auth;

use App\Http\Controllers\Controller;
use App\Http\Responses\Auth\BiometricResponse;
use App\Models\Client;
use App\Services\DeviceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class StoreBiometryKeyController extends Controller
{
    public function __construct(
        private readonly BiometricResponse $biometricResponse,
        private readonly DeviceService $deviceService
    ) {}


    /**
     * Save the public key generated by the mobile app to verificate the biometric session.
     */
    public function __invoke(Request $request): JsonResponse
    {
        try {
            /** @var Client */
            $client = auth('api')->user();

            $this->deviceService->updateBiometricToDevice($request, $client);

            return $this->biometricResponse->biometricPublicKeySavedSuccessfully();
        } catch (Throwable $e) {
            return $this->biometricResponse->biometricPublicKeyInternalServerError($e->getMessage());
        }
    }
}
