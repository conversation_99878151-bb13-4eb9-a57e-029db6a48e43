<?php

declare(strict_types=1);

namespace App\Http\Controllers\API\Auth;

use App\DTO\FaceAuthRequestDTO;
use App\Models\Client;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Tasks\Auth\CallFaceAuthenticationTask;
use App\Http\Requests\Auth\ValidateFaceAuthenticationRequest;
use App\Http\Responses\ApiResponse;
use App\Tasks\Auth\CallFaceAuthenticationExtTask;

class FaceAuthenticationExtController extends Controller
{
    public function __construct(
        private readonly ApiResponse $response,
        private readonly CallFaceAuthenticationTask $callFaceAuthenticationTask,
        private readonly CallFaceAuthenticationExtTask $callFaceAuthenticationExtTask
    ) {}

    public function __invoke(ValidateFaceAuthenticationRequest $request): JsonResponse
    {
        $faceAuthData = FaceAuthRequestDTO::make($request->validated());

        $client = Client::where('dui', $faceAuthData->query)->first();

        /** @var CallFaceAuthenticationTask|CallFaceAuthenticationExtTask */
        $task = match ($client->foreigner) {
            true => $this->callFaceAuthenticationExtTask,
            default => $this->callFaceAuthenticationTask
        };

        $response = $task
            ->withFaceAuthData($faceAuthData)
            ->do();

        return $this->response->success($response);
    }
}
