<?php

namespace App\Http\Controllers\API\Auth;

use App\Enums\LogApplicationCode;
use App\Http\Responses\ApiResponse;
use App\Models\Client;
use App\Traits\ActionLogger;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class LogoutController extends Controller
{
    use ActionLogger;

    public function __construct(private readonly ApiResponse $response)
    {
    }

    public function __invoke(Request $request)
    {
        /** @var Client */
        $client = $request->user('api');

        $this->logBank($client->client_id, code: LogApplicationCode::LOGOUT);

        return $this->response->success();
    }
}
