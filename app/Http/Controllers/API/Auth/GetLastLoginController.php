<?php

namespace  App\Http\Controllers\API\Auth;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Responses\Auth\LastLoginResponse;
use App\Models\Client;
use App\Services\ClientService;
use Symfony\Component\HttpFoundation\Response;

class GetLastLoginController extends Controller
{
    public function __construct(
        private readonly LastLoginResponse $lastLoginResponse,
        private readonly ClientService $clientService,
    ) {
    }

    /**
     * Get last login info
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(): JsonResponse
    {
        try {
            /** @var Client */
            $client = auth('api')->user();

            $latestLogin = $this->clientService->getLatestLogin($client);

            if (empty($latestLogin)) {
                return $this->lastLoginResponse->emptyLoginActivity();
            }

            return $this->lastLoginResponse->successful($latestLogin);
        } catch (Exception $e) {
            return $this->lastLoginResponse->internalServerError();
        }
    }
}
