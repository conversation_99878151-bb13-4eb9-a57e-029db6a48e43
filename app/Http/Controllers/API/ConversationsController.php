<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateConversationRequest;
use App\Http\Resources\ConversationResource;
use App\Services\Twilio\ConversationCreatorService;

class ConversationsController extends Controller
{
    /**
     * Creates a Twilio conversation, adds a participant and
     * an access token for such.
     *
     * @param ConversationCreatorService $service
     * @param CreateConversationRequest $request
     * @return ConversationResource
     */
    public function __invoke(
        ConversationCreatorService $service,
        CreateConversationRequest $request,
    ) {
        return ConversationResource::make($service->createConversationWithWebParticipant($request->identity));
    }
}
