<?php

namespace App\Http\Controllers\API;

use App\Helpers\Box;
use App\Http\Controllers\Controller;
use App\Http\Resources\MaintenancePeriodResource;
use App\Http\Responses\ApiResponse;
use App\Models\MaintenanceModes as MaintenancePeriod;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class MaintenancePeriodsController extends Controller
{
    public function __construct(private ApiResponse $response)
    {
    }

    /**
     * Returns a maintenance period, if any.
     */
    public function __invoke(): Response|JsonResponse|Responsable
    {
        return Box::put(MaintenancePeriod::getCurrentActivePeriod())
            ->peek(fn($period) => MaintenancePeriodResource::make($period))
            ->andOpen($this->response->noContent());
    }
}
