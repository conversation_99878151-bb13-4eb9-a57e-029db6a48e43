<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use App\Enums\Message\AuthMessage;
use App\Http\Responses\ApiResponse;
use Illuminate\Http\JsonResponse;

class LoginResponse extends ApiResponse
{
    public function externalTokenServiceFailure(string $code): JsonResponse
    {
        return $this->serviceUnavailable([
            'message' => AuthMessage::EXTERNAL_TOKEN_SERVICE_FAILURE,
            'error_code' => $code,
        ]);
    }
}
