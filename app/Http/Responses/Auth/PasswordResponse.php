<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use App\Enums\Message\Generate;
use App\Enums\Code\PasswordCode;
use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use Symfony\Component\HttpFoundation\Response;

class PasswordResponse extends ApiResponse
{
    public function passwordUpdatedSuccessfully(): JsonResponse
    {
        return $this->success([
            'status' => $status = Response::HTTP_OK,
            'result' => Generate::CHANGED_PASSWORD->value,
            'code' => PasswordCode::SUCCES->value,
            'message' => '',
        ], $status);
    }

    public function firstPasswordUpdatedSuccessfully(): JsonResponse
    {
        return $this->success([
            'status' => $status = Response::HTTP_OK,
            'result' => 'Contraseña restablecida',
            'code' => PasswordCode::SUCCES->value,
            'message' => '',
        ], $status);
    }

    public function firstPasswordInternalServerError(): JsonResponse
    {
        return $this->error([
            'result' => Generate::ERROR_CHANGE_PASSWORD->value,
            'status' => $status = Response::HTTP_INTERNAL_SERVER_ERROR,
            'code' => PasswordCode::SERVER_ERROR->value,
            'messages' => '',
        ], $status);
    }

    public function customerNotFound(string $errorMessage): JsonResponse
    {
        $status = Response::HTTP_BAD_REQUEST;

        return $this->error(
            data: [
                'status' => $status,
                'result' => $errorMessage,
                'code' => PasswordCode::CLIENT_404,
                'messages' => '',
            ],
            status: $status
        );
    }

    public function validatePasswordCustomerNotFound(string $errorMessage): JsonResponse
    {
        $response = $this->customerNotFound($errorMessage);

        $response->setData([
            ...$response->getData(true),
            'valid_password' => false
        ]);

        return $response;
    }

    public function validPassword(): JsonResponse
    {
        return $this->success([
            'status' => $status = Response::HTTP_OK,
            'result' => Generate::VALID_PASSWORD_FORMAT->value,
            'valid_password' => true,
            'code' => PasswordCode::SUCCES->value,
            'messages' => ''
        ], $status);
    }

    public function invalidPasswordFormat(int $code, string $errorMessage): JsonResponse
    {
        $status = Response::HTTP_BAD_REQUEST;

        return $this->error(
            data: [
                'status' => $status,
                'result' => Generate::INVALID_PASSWORD_FORMAT->value,
                'code' => $code,
                'messages' => $errorMessage
            ],
            status: $status
        );
    }

    public function validatePasswordInvalidFormat(int $code, string $errorMessage): JsonResponse
    {
        $response = $this->invalidPasswordFormat($code, $errorMessage);

        $response->setData([
            ...$response->getData(true),
            'valid_password' => false
        ]);

        return $response;
    }

    public function missingFields(int $code, string $errorMessage): JsonResponse
    {
        return $this->error([
            'status' => $status = Response::HTTP_BAD_REQUEST,
            'result' => Generate::MORE_REQUIRED_FIELD->value,
            'code' => $code,
            'messages' => $errorMessage
        ], $status);
    }
    public function cannotDecryptData(): JsonResponse
    {
        return $this->error([
            'status' => $status = Response::HTTP_BAD_REQUEST,
            'result' => Generate::NOT_DESCRIPT->value,
            'code' => PasswordCode::DECRYPT_ERROR->value,
            'messages' => ''
        ], $status);
    }

    public function firstCurrentPasswordUsed(string $errorMessage): JsonResponse
    {
        $status = Response::HTTP_BAD_REQUEST;

        return $this->error([
            'status' => $status,
            'result' => $errorMessage,
            'code' => PasswordCode::PASS_USED->value,
            'messages' => $errorMessage,
            'valid_password' => false,
        ], $status);
    }

    public function currentPasswordUsed(string $errorMessage): JsonResponse
    {
        $status = Response::HTTP_BAD_REQUEST;

        return $this->error(
            data: [
                'status' => $status,
                'result' => Generate::PASSWORD_USED->value,
                'code' => PasswordCode::PASS_USED->value,
                'messages' => $errorMessage,
                'valid_password' => false,
            ],
            status: $status
        );
    }

    public function passwordValidationFailed(string $errorMessage): JsonResponse
    {
        return $this->error([
            'status' => $status = Response::HTTP_BAD_REQUEST,
            'result' => $errorMessage,
            'code' => PasswordCode::FAIL->value,
            'messages' => $errorMessage
        ], $status);
    }

    public function resetPasswordInternalServerError(string $errorMessage = ''): JsonResponse
    {
        return $this->internalServerError([
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
            'result' => Generate::FAIL_SERVER->value,
            'code' => 5000,
            'messages' => $errorMessage,
        ]);
    }

    public function validatePasswordInternalServerError(): JsonResponse
    {
        return $this->internalServerError([
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
            'result' => Generate::ERROR_VERIFY_PASSWORD->value,
            'valid_password' => false,
            'code' => PasswordCode::SERVER_ERROR->value,
            'messages' => '',
        ]);
    }
}
