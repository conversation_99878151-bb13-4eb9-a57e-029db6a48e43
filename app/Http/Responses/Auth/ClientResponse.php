<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use App\Enums\Message\Generate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use App\Http\Responses\ApiResponse;
use App\Enums\Code\ClientPhoneValidationCode;
use Symfony\Component\HttpFoundation\Response;

class ClientResponse extends ApiResponse
{
    public function clientNotFound(): JsonResponse
    {
        return  $this->notFound([
            'result' => Generate::DUI_FAIL->value,
            'status' => Response::HTTP_NOT_FOUND,
            'code' => ClientPhoneValidationCode::DUI_NOT_FOUND,
        ]);
    }

    public function missingContactInformation(): JsonResponse
    {
        return $this->success([
            'result' =>  Generate::NOT_MORE_DATA->value,
            'status' => Response::HTTP_OK,
            'code' => ClientPhoneValidationCode::SUCCESS->value,
            'customer_id' => "",
            'creditcard_application_id' => ""
        ]);
    }

    public function invalidPhoneNumber(): JsonResponse
    {
        return $this->error([
            'result' =>  Generate::NUMBER_INCORRECT_TRY_AGAIN->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => ClientPhoneValidationCode::INVALID_PHONE_NUMBER->value,
            'customer_id' => "",
            'creditcard_application_id' => ""
        ]);
    }

    public function validUser(string $clientId, string $ccApplicationId): JsonResponse
    {
        return $this->success([
            'result' => Generate::RESULT_OK->value,
            'status' => Response::HTTP_OK,
            'code' => ClientPhoneValidationCode::SUCCESS->value,
            'customer_id' => $clientId,
            'creditcard_application_id' => $ccApplicationId,
        ]);
    }

    public function validateUserServerError(string $errorMessage): JsonResponse
    {
        return $this->internalServerError([
            'result' => Generate::FAIL_SERVER->value,
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
            'code' => ClientPhoneValidationCode::INTERNAL_SERVER_FAILURE->value, // Server error
            'message' => $errorMessage,
        ]);
    }

    public function rememberNickNameInvalidPhoneNumber(): JsonResponse
    {
        return $this->success([
            'result' => 'El número ingresado no es correcto. Intenta nuevamente.',
            'status' => Response::HTTP_OK,
            'code' => 0,
            'key' =>  '',
            'CCApplicationId' => null,
        ]);
    }

    public function rememberNicknameSuccessfully(string $key, string $ccApplicationId): JsonResponse
    {
        return $this->success([
            'result' => Generate::RESULT_OK,
            'status' => Response::HTTP_OK,
            'code' => 1,
            'key' =>  $key,
            'CCApplicationId' => $ccApplicationId,
        ]);
    }

    public function successfullyAddedToSystem(): JsonResponse
    {
        $status = Response::HTTP_CREATED;

        return $this->success([
            'status' => $status,
            'result' => Generate::CLIENT_ADD_SYSTEM->value
        ], $status);
    }

    public function successfullyRemovedFromSystem(): JsonResponse
    {
        $status = Response::HTTP_ACCEPTED;

        return $this->success([
            'status' => $status,
            'result' => Generate::CLIENT_DELETE_SYSTEM->value
        ], $status);
    }

    public function clientSystemNotFound(): JsonResponse
    {
        return $this->error([
            'status' => Response::HTTP_BAD_REQUEST,
            'result' => Generate::CLIENT_NOT_FOUND->value
        ]);
    }
}
