<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;

class HomologateUserResponse extends ApiResponse
{
    public function clientHomologatedSuccessfully(): JsonResponse
    {
        return $this->success([
            'message' => 'El cliente ha sido homologado satisfactoriamente.',
            'code' => 'client_homologated_successfully'
        ]);
    }

    public function clientAlreadyHomologated(): JsonResponse
    {
        return $this->error([
            'message' => 'Este cliente ya se encuentra homologado.',
            'code' => 'client_already_homologated',
        ]);
    }

    public function clientNotFound(?string $message = null, ?string $code = null): JsonResponse
    {
        return $this->notFound(data: [
            'message' => $message ?: "Client not found",
            'code' => $code ?: "client_not_found"
        ]);
    }

    public function couldNotCreateVBankUser(): JsonResponse
    {
        return $this->internalServerError([
            'message' => 'Hubo un error al crear usuario VBank.',
            'code' => 'create_vbank_user_failure'
        ]);
    }

    public function unexpectedError(): JsonResponse
    {
        return $this->internalServerError([
            'message' => 'Hubo un error inesperado durante el proceso de homologación.',
            'code'  => 'unexpected_homologation_error'
        ]);
    }
}
