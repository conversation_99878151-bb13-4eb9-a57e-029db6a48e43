<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use Symfony\Component\HttpFoundation\Response;

class BiometricResponse  extends ApiResponse
{
    public function biometricPublicKeySavedSuccessfully(): JsonResponse
    {
        return $this->success([
            'result' => 'Llave pública guardada correctamente.',
            'status' =>  Response::HTTP_OK,
        ]);
    }

    public function biometricPublicKeyInternalServerError(string $errorMessage): JsonResponse
    {
        $status = Response::HTTP_INTERNAL_SERVER_ERROR;

        return $this->error([
            'result' => 'No se pudo guardar la llave pública del dispositivo.',
            'status' => $status,
            'message' => $errorMessage,
        ], $status);
    }
}
