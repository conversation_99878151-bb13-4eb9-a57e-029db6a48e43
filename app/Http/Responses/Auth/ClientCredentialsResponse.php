<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use App\Enums\Message\Generate;
use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use App\Enums\Code\ClientCredentialCode;
use Symfony\Component\HttpFoundation\Response;

class ClientCredentialsResponse extends ApiResponse
{
    public function validationError(string $errorMessage, int $code): JsonResponse
    {
        return $this->error([
            'result' => 'Uno o más campos son requeridos',
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => $code,
            'message' => $errorMessage
        ]);
    }

    public function lockClientUnknownIdentifier(): JsonResponse
    {
        return $this->error([
            'result' => Generate::INVALID_IDENTIFIER->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => ClientCredentialCode::UNKNOWN_IDENTIFIER->value,
            'message' => ''
        ]);
    }

    public function clientNotFoundByDUI(string $dui): JsonResponse
    {
        return $this->notFound([
            'result' => "No se encontró cliente con DUI:{$dui}.",
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => ClientCredentialCode::CLIENT_DUI_NOT_FOUND->value,
            'message' => ''
        ]);
    }

    public function clientNotFoundByID(string $id): JsonResponse
    {
        return $this->notFound([
            'result' => "No se encontró cliente con CLIENT_ID:{$id}.",
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => ClientCredentialCode::CLIENT_ID_NOT_FOUND->value,
            'message' => ''
        ]);
    }

    public function lockClientSuccessful(): JsonResponse
    {
        return $this->success([
            'result' => "Cliente inhabilitado correctamente.",
            'status' => Response::HTTP_OK,
            'code' => ClientCredentialCode::SUCCESS->value,
            'message' => ''
        ]);
    }

    public function unlockClientSuccessful(): JsonResponse
    {
        return $this->success([
            'result' => "Cliente habilitado correctamente.",
            'status' => Response::HTTP_OK,
            'code' => ClientCredentialCode::SUCCESS->value,
            'message' => ''
        ]);
    }

    public function credentialsInternalServerError(string $errorMessage): JsonResponse
    {
        return $this->internalServerError([
            'result'  => Generate::FAIL_SERVER->value,
            'status'  => Response::HTTP_INTERNAL_SERVER_ERROR,
            'code'    => ClientCredentialCode::INTERNAL_SERVER_FAILURE->value,
            'message' => $errorMessage
        ]);
    }

    public function lockFaceAuthSuccessful(): JsonResponse
    {
        return $this->success([
            'result' => Generate::CREDENTIAL_FAIL->value,
            'status' => Response::HTTP_OK,
            'code' => ClientCredentialCode::SUCCESS->value,
            'message' => ''
        ]);
    }

    public function lockFaceAuthInternalServerError(): JsonResponse
    {
        return $this->internalServerError([
            'result'  => Generate::FAIL_SERVER->value,
            'status'  => Response::HTTP_INTERNAL_SERVER_ERROR,
            'code'    => ClientCredentialCode::FAIL->value,
            'message' => ''
        ]);
    }
}
