<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use App\Enums\OTP\Generate;
use App\Enums\OTP\Validate;
use App\Enums\Message\OTPMessage;
use Illuminate\Http\JsonResponse;
use App\Http\Responses\ApiResponse;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\Message\Generate as MessageGenerate;

class OTPResponse extends ApiResponse
{
    public function otpGenerationFailed(string $message, int $code, int $status): JsonResponse
    {
        return $this->error([
            'result' => $message,
            'status' => $status,
            'code' => $code,
            'SharedKey' => '',
        ], $status);
    }

    public function emptySharedKey(string $message): JsonResponse
    {
        return $this->error([
            'status' => Response::HTTP_BAD_REQUEST,
            'result' => $message,
            'code' => Generate::WAIT_TIME->value,
        ]);
    }

    public function otpGeneratedSuccessfully(string $sharedKey): JsonResponse
    {
        return $this->success([
            'result' => MessageGenerate::RESULT_OK->value,
            'status' => Response::HTTP_OK,
            'code' => Generate::SUCCESS->value,
            'SharedKey' => $sharedKey,
        ]);
    }

    public function validateOTPSuccessful(): JsonResponse
    {
        return $this->success([
            'result' => OTPMessage::VALID->value,
            'status' => Response::HTTP_OK,
            'code' => Validate::SUCCESS->value,
        ]);
    }

    public function validateOTPServerError(): JsonResponse
    {
        return $this->internalServerError([
            'result' => MessageGenerate::FAIL_SERVER->value,
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
        ]);
    }

    public function validateOTPEmptyNicknameLog(): JsonResponse
    {
        return $this->error([
            'result' => OTPMessage::INVALID_SHARED_KEY->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => Validate::SHAREDKEY_ERR->value,
        ]);
    }

    public function validateOTPInvalidCode(): JsonResponse
    {
        return $this->error([
            'result' => OTPMessage::INVALID->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => Validate::INVALID->value,
        ]);
    }

    public function validateOTPCodeMissingParts(): JsonResponse
    {
        return $this->error([
            'result' => OTPMessage::INVALID_SHARED_KEY->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => Validate::INVALID_REQUEST->value,
        ]);
    }

    public function validateOTPExpiredCode(): JsonResponse
    {
        return $this->error([
            'result' => OTPMessage::EXPIRED->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => Validate::TIME_LIFE->value,
        ]);
    }

    public function validateOTPExceededAttemps(string $message, int $code): JsonResponse
    {
        return $this->error([
            'result' => $message,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => $code,
        ]);
    }
}
