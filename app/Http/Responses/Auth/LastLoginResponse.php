<?php

declare(strict_types=1);

namespace App\Http\Responses\Auth;

use App\Enums\Message\Generate;
use Illuminate\Http\JsonResponse;
use App\Enums\Message\AuthMessage;
use App\Http\Responses\ApiResponse;
use Symfony\Component\HttpFoundation\Response;

class LastLoginResponse extends ApiResponse
{
    public function successful(string $lastLogin): JsonResponse
    {
        return $this->success([
            'result' => AuthMessage::LAST_ENTRY_APP->value,
            'status' => Response::HTTP_OK,
            'last_login' => $lastLogin,
            'code' => 1,
            'message' => ''
        ]);
    }


    public function emptyLoginActivity(): JsonResponse
    {
        return $this->success([
            'result' => AuthMessage::LAST_ENTRY_APP->value,
            'status' => Response::HTTP_OK,
            'last_login' => '',
            'code' => 2,
            'message' => ''
        ]);
    }

    public function lastLoginInternalServerError(): JsonResponse
    {
        return $this->internalServerError([
            'result' => Generate::FAIL_SERVER->value,
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
            'last_login' => '',
            'code' => 3,
            'message' => ''
        ]);
    }
}
