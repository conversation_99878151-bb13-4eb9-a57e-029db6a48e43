<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class MaintenanceResponse extends ApiResponse
{
    public function isAvailable(string $apiKeyCode): JsonResponse
    {
        return $this->success([
            'result' => "Ok",
            'apikey' => $apiKeyCode,
            'status' => Response::HTTP_OK,
            'code' => 0
        ]);
    }

    public function isUnderMaintenance(string $apiKeyCode, string $message): JsonResponse
    {
        $status = Response::HTTP_SERVICE_UNAVAILABLE;

        return $this->error([
            'result' => $message,
            'apikey' => $apiKeyCode,
            'status' => $status,
            'code' => 1
        ], $status);
    }
}
