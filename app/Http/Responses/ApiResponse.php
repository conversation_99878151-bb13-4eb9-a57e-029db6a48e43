<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ApiResponse
{
    public function response(
        array|Responsable $data = [],
        int $status = 200,
        array $headers = [],
        $options = 0
    ): JsonResponse {
        return response()->json($data, $status, $headers, $options);
    }

    public function success(array|Responsable $data = [], int $status = 200, array $headers = []): JsonResponse
    {
        return $this->response($data, $status, $headers);
    }

    public function noContent(): JsonResponse
    {
        return $this->response(status: Response::HTTP_NO_CONTENT);
    }

    public function error(array $data = [], int $status = 400, array $headers = []): JsonResponse
    {
        return $this->response($data, $status, $headers);
    }

    public function unauthorized(array $data = [], int $status = 401, array $headers = []): JsonResponse
    {
        return $this->response($data, $status, $headers);
    }

    public function notFound(array $data = [], int $status = 404, array $headers = []): JsonResponse
    {
        return $this->response($data, $status, $headers);
    }

    public function internalServerError(array $data = [], int $status = 500, array $headers = []): JsonResponse
    {
        return $this->response($data, $status, $headers);
    }

    public function serviceUnavailable(array $data = [], int $status = 503, array $headers = []): JsonResponse
    {
        return $this->response($data, $status, $headers);
    }
}
