<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\Enums\Message\Generate;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class NotificationResponse extends ApiResponse
{
    public function firebaseTokenSaved(): JsonResponse
    {
        return $this->success([
            'result' => Generate::RESULT_OK->value,
            'status' => Response::HTTP_OK
        ]);
    }

    public function templateNotFound(): JsonResponse
    {
        return $this->error([
            'result' => Generate::TEMPLATE_NOT_FOUND->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'Firebase_response' => "",
            'Huawei_response' => "",
        ]);
    }

    public function notificationSent($firebaseResponse, $huwaeiResponse): JsonResponse
    {
        return $this->success([
            'result' => Generate::NOTIFICATE_SEND->value,
            'Firebase_response' => $firebaseResponse,
            'Huawei_response' => $huwaeiResponse,
            'status' => Response::HTTP_OK
        ]);
    }


    public function serverError(): JsonResponse
    {
        return $this->internalServerError([
            'result' => Generate::FAIL_SERVER->value,
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR
        ]);
    }
}
