<?php

namespace App\Http\Requests\OTP;

use App\Enums\Message\Generate;
use App\Enums\OTP\Validate;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;

class ValidateOtpRequest extends FormRequest
{
    public const REQUIRED_STRING = 'required|string';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'OTPCode' => self::REQUIRED_STRING,
            'SharedKey' => self::REQUIRED_STRING,
            'Key' => $this->Key ? self::REQUIRED_STRING : 'nullable'
        ];
    }

    /**
     * Customer validation message exception
     *
     * @param Validator $validator
     *
     * @return void
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(response()->json([
            'result' => Generate::INCOMPLETED_FIELD_REQUIRED->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'code' => Validate::INVALID_REQUEST->value,
            'SharedKey' => ''
        ], Response::HTTP_BAD_REQUEST));
    }
}
