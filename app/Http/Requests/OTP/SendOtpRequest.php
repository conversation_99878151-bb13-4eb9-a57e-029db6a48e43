<?php

namespace App\Http\Requests\OTP;

use App\Enums\Message\Generate;
use App\Enums\OTP\Generate as OTPGenerate;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;

class SendOtpRequest extends FormRequest
{
    public const REQUIRED_STRING = 'required|string';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type'    => self::REQUIRED_STRING,
            'value'   => self::REQUIRED_STRING,
            'chanel'  => self::REQUIRED_STRING,
        ];
    }

    /**
     * Custome validation message exception
     *
     * @param Validator $validator
     * @return void
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(
            response()->json([
                'result' => Generate::MORE_REQUIRED_FIELD->value,
                'status' => Response::HTTP_BAD_REQUEST,
                'code' => OTPGenerate::INVALID_REQUEST->value,
                'SharedKey' => ''
            ], Response::HTTP_BAD_REQUEST)
        );
    }
}
