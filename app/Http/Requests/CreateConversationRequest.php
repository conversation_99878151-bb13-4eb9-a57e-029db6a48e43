<?php

namespace App\Http\Requests;

use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

class CreateConversationRequest extends FormRequest
{
    use AuthorizesRequests;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'identity' => [
                'required',
                'string',
                'alpha_dash',
                'min:2',
                'max:30',
            ],
        ];
    }
}
