<?php

namespace App\Http\Requests;

use App\Enums\Message\Generate;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;

class ClientValidateRequest extends FormRequest
{
    public const REQUIRED = 'required';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phone_number' => self::REQUIRED,
            'dui' => self::REQUIRED
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
         'result' => Generate::MORE_REQUIRED_FIELD->value,
         'status' => Response::HTTP_BAD_REQUEST,
         "validated" => $validator->errors()->first(),
    ], Response::HTTP_BAD_REQUEST));
    }
}
