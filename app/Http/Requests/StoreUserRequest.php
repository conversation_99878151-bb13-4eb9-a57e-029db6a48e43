<?php

namespace App\Http\Requests;

use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    use AuthorizesRequests;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'customer_id' => [
                'required',
                'integer',
            ],
            'creditcard_application_id' => [
                'required',
                'integer',
            ],
        ];
    }
}
