<?php

namespace App\Http\Requests\Clients;

use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

class RefreshTokenRequest extends FormRequest
{
    use AuthorizesRequests;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'device_id' => [
                'required',
                'string',
            ],
            'refresh_token' => [
                'required',
                'string',
                'exists:clients,baes_refresh_token',
            ],
        ];
    }
}
