<?php

namespace App\Http\Requests;

use App\Rules\IsOptionOfSurveyRule;
use App\Tasks\Surveys\CheckSurveyAndOptionRelationTask;
use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

class SurveyAnswersRequest extends FormRequest
{
    use AuthorizesRequests;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(CheckSurveyAndOptionRelationTask $task)
    {
        return [
            'survey_id' => [
                'bail',
                'required',
                'integer',
                'exists:survey,id',
            ],
            'answers' => [
                'bail',
                'required',
                'array',
            ],
            'answers.*.option_id' => [
                'bail',
                'required',
                'integer',
                'exists:options,id',
                new IsOptionOfSurveyRule($task, $this->route('id')),
            ],
            'answers.*.other' => [
                'present',
                'nullable',
                'string',
                'max:255',
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'survey_id' => $this->route('id'),
        ]);
    }
}
