<?php

namespace App\Http\Requests\Password;

use App\Enums\Code\PasswordCode;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;

class PassworRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dui' => 'required'
        ];
    }

    public function failedValidation(Validator $validator)
    {
        $status = Response::HTTP_NOT_FOUND;
        throw new HttpResponseException(response()->json([
            'result' => 'Uno o más campos son requeridos.',
            'status' => $status,
            'valid_password' => false,
            'code' => PasswordCode::DUI_ERROR->value,
            'messages' => $validator->errors()->first(),
     ], 400));
    }
}
