<?php

namespace App\Http\Requests;

use App\Models\Client;
use App\Rules\IsDeviceOfflineRule;
use App\Rules\IsDeviceOfUserRule;
use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

class RemoveDeviceRequest extends FormRequest
{
    use AuthorizesRequests;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        /** @var Client|null */
        $user = auth('api')->user();

        if (!$user) {
            abort(403, 'Unauthorized');
        }

        return [
            'id' => [
                'bail',
                'required',
                new IsDeviceOfUserRule($user),
                new IsDeviceOfflineRule($user),
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);
    }
}
