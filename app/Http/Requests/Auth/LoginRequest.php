<?php

namespace App\Http\Requests\Auth;

use App\Rules\IsLatitudeRule;
use App\Rules\IsLongitudeRule;
use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

/**
 * @property string $nickname RSA encrypted nickname.
 * @property string $password RSA encrypted password.
 * @property string $device_id
 * @property string $device_name
 * @property string $firebase_token.
 * @property string $longitude
 * @property string $latitude
 * @property bool $is_huawei
 */
class LoginRequest extends FormRequest
{
    use AuthorizesRequests;

    public const REQUIRED_STRING = 'required|string';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'nickname' => self::REQUIRED_STRING,
            'password' => self::REQUIRED_STRING,
            'device_id' => self::REQUIRED_STRING,
            'device_name' => self::REQUIRED_STRING,
            'firebase_token' => self::REQUIRED_STRING,
            'is_huawei' => [
                'sometimes',
                'boolean',
            ],
            'longitude' => [
                'present',
                'nullable',
                'string',
                new IsLongitudeRule(true),
            ],
            'latitude' => [
                'present',
                'nullable',
                'string',
                new IsLatitudeRule(true),
            ],
        ];
    }
}
