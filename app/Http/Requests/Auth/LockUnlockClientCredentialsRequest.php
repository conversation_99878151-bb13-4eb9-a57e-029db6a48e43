<?php

namespace App\Http\Requests\Auth;

use App\Enums\Code\ClientCredentialCode;
use App\Http\Responses\Auth\ClientCredentialsResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class LockUnlockClientCredentialsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'IDENTIFIER'  => 'required|string|in:CLIENT_ID,DUI',
            'VALUE' => 'required|string|alpha_dash|min:1|max:60',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->failed();

        $errorMessage = $validator->errors()->first();

        $errorCode = array_key_first($errors) === 'IDENTIFIER'
            ? ClientCredentialCode::INVALID_IDENTFIER->value
            : ClientCredentialCode::INVALID_IDENTFIIER_VALUE->value;

        $response = app(ClientCredentialsResponse::class)->validationError(
            errorMessage: $errorMessage,
            code: $errorCode,
        );

        throw new HttpResponseException($response);
    }
}
