<?php

namespace App\Http\Requests\Auth;

use App\DTO\ResultData;
use App\Enums\Message\Generate;
use App\Traits\ActionLogger;
use App\Traits\HeaderIpTrait;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;

class LogoutRequest extends FormRequest
{
    use HeaderIpTrait;
    use ActionLogger;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'device_id' => 'required|string'
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(Validator $validator): void
    {
        $ip =  $this->getIpHeader(request());
        $data = new ResultData([
            'endpointName' => 'LOGOUT',
            'ip' => $ip,
            'result' => Generate::MORE_REQUIRED_FIELD->value,
            'status' => Response::HTTP_BAD_REQUEST,
            'clientId' => auth('api')->id(),
            'deviceId' => ""
        ]);

        $this->endpointLog($data);
        throw new HttpResponseException(
            response()->json([
                'result' => $data->result,
                'status' => $data->status
            ], $data->status)
        );
    }
}
