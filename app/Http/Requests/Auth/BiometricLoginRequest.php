<?php

namespace App\Http\Requests\Auth;

use App\Traits\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;

class BiometricLoginRequest extends FormRequest
{
    use AuthorizesRequests;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'signed_data' => [
                'bail',
                'required',
                'string',
            ],
            'device_name' => [
                'bail',
                'required',
                'string',
            ],
            'device_id' => [
                'bail',
                'required',
                'string',
            ],
        ];
    }
}
