<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @property-read int $CCApplicationId
 * @property-read string $Method
 * @property-read string $Querry
 * @property-read string $ImageBuffer
 */
class ValidateFaceAuthenticationRequest extends FormRequest
{
    private const REQUIRED_STRING = 'required|string';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'CCApplicationId' => 'required|numeric',
            'Method' => self::REQUIRED_STRING,
            'Querry' => self::REQUIRED_STRING,
            'ImageBuffer' => self::REQUIRED_STRING,
        ];
    }

    public function validated($key = null, $default = null): mixed
    {
        $headers = [
            'CustomerId' => $this->headers->get('UserAuth'),
            'DeviceId' => $this->headers->get('DeviceAuth')
        ];

        return array_merge(parent::validated($key, $default), $headers);
    }

    /**
     * Maps the attibutes being validated to the given attribute name on validation error messages.
     */
    public function attributes(): array
    {
        return [
            'CCApplicationId' => 'credit card application id'
        ];
    }
}
