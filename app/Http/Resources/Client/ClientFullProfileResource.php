<?php

namespace App\Http\Resources\Client;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Symfony\Component\HttpFoundation\Response;

class ClientFullProfileResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            "customer_id" => strval($this->client_id),
            "creditcard_application_id" =>  $this->creditcard_application_id
                ? (int) $this->creditcard_application_id
                : null,
            "nickname" => $this->nickname,
            "first_name" => $this->first_name,
            "second_name" => $this->second_name,
            "first_surname" => $this->first_surname,
            "second_surname" => $this->second_surname,
            "married_surname" => $this->married_surname,
            "dui" => $this->dui,
            "nit" => $this->nit,
            "email" => $this->email,
            "phone_number" => $this->phone_number,
            "status" => Response::HTTP_OK,
            "last_login" => $this->whenLoaded($this->last_login)
        ];
    }

    private function getClientImage(string $imageType): string
    {
        $backgroundImage = $this->profileImages
            ->where('type', $imageType)
            ->first();

        return $backgroundImage?->image ?? '';
    }
}
