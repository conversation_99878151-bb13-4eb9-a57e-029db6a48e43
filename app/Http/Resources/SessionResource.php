<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use App\Enums\Code\PasswordLoginCode;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Client\ClientFullProfileResource;

class SessionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $response = [
            'token' => $this->loginSession->token->token,
            'refresh_token' => $this->loginSession->token->refreshToken,
            'message' => $this->loginSession->message,
            'code' => $this->loginSession->code,
        ];

        $homologationCode = $this->loginSession->homologationCode
            ?? PasswordLoginCode::tryFrom($this->loginSession->code)?->getCodeForHomologatedUsers()->value;

        if (isset($homologationCode)) {
            $response['result'] = $homologationCode;
        }

        if ($client = $this->client) {
            $user = (new ClientFullProfileResource($client))->toArray($request);
            $user['last_login'] = $this->latestLogin;
            $response['user'] = $user;
        }

        return $response;
    }
}
