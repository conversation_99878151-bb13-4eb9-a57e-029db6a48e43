<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BenefitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($_)
    {
        return [
            'title' => $this->title,
            'description' => $this->description,
            'example' => $this->example,
            'main' => $this->main,
            'order' => $this->order,
            'longcode' => $this->longcode,
            'icon' => $this->getIconAsUrl(),
            'storehouse_id' => $this->storehouse_id,
        ];
    }
}
