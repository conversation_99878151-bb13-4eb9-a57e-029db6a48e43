<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ConversationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $_
     * @return array
     */
    public function toArray($_)
    {
        return [
            'access_token' => $this->accessToken,
            'conversation_sid' => $this->conversationSid,
        ];
    }
}
