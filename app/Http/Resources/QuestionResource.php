<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($_)
    {
        return [
            'id' => $this->id,
            'survey_id' => $this->survey_id,
            'type' => $this->type,
            'question' => $this->question,
            'options' => OptionResource::collection($this->options),
        ];
    }
}
