<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($_)
    {
        return [
            'id' => $this->id,
            'question_id' => $this->question_id,
            'value' => $this->value,
            'type' => $this->type,
        ];
    }
}
