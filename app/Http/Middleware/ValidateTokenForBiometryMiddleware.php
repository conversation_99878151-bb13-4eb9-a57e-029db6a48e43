<?php

namespace App\Http\Middleware;

use App\Dtos\ExternalAuth;
use App\Dtos\ExternalAuthBiometry;
use Illuminate\Http\Request;

class ValidateTokenForBiometryMiddleware extends ValidateTokenMiddleware
{
    /**
     * Headers the request should have.
     *
     * Authorization is already included.
     *
     * @var array
     */
    public const REQUIRED_HEADERS = [
        ExternalAuth::DEVICE_HEADER,
        ExternalAuthBiometry::DUI_HEADER,
        ExternalAuthBiometry::BIOMETRY_HEADER,
    ];

    /**
     * Extracts data needed for the validation from the request.
     *
     * @param Request $request
     * @return ExternalAuthBiometry
     */
    protected function getDataFromRequest(Request $request): ExternalAuthBiometry
    {
        return ExternalAuthBiometry::fromRequest($request);
    }

    /**
     * Returns check failed message for debugging.
     *
     * @return string
     */
    protected static function getCheckFailedMessage(): string
    {
        return 'Not a biometry user';
    }
}
