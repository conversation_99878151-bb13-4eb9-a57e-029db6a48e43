<?php

namespace App\Http\Middleware;

use Closure;
use App\Helpers\Box;
use Illuminate\Http\Request;
use App\Http\Responses\ApiResponse;
use App\Models\MaintenanceModes as MaintenancePeriod;

class MaintenanceModeMiddleware
{
    public function __construct(private readonly ApiResponse $repsonse)
    {
    }

    /**
     * Checks if the system is under a maintenance period.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $isNotMaintenanceRoute = !Box::put($request->route())
            ->named('maintenance-periods')
            ->andOpen(false);

        if ($isNotMaintenanceRoute && MaintenancePeriod::getCurrentActivePeriod()) {
            return $this->repsonse->serviceUnavailable();
        }

        return $next($request);
    }
}
