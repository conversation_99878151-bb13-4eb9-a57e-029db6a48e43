<?php

namespace App\Http\Middleware;

use App\Models\Client;
use App\Repos\GetCustomerContactChannelsRepo;
use App\Tasks\Clients\UpdatePhoneTask;
use Illuminate\Http\Request;
use App\DTO\ExternalRequestResponse;
use App\Services\Log;
use App\Tasks\Clients\UpdateEmailTask;
use Symfony\Component\HttpFoundation\Response;

class InterceptUpdateCustomerContactMiddleware
{
    public function __construct(private readonly Log $log)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(Request): (Response)  $next
     */
    public function handle(Request $request, \Closure $next): Response
    {
        if ($request->is('api/CustomerContactsp')) {
            $response = $next($request);
            $responseDTO = $this->parseResponse($response);

            /** @var Client|null */
            $user = $request->user('api');

            if ($user && $this->successfulResponse($responseDTO)) {
                $this->updateCustomerContact($user, $request->all());
            }

            return $response;
        }

        return $next($request);
    }

    private function parseResponse(Response $response): ExternalRequestResponse
    {
        $responseData = json_decode($response->getContent(), true);
        return ExternalRequestResponse::fromExternalResponse($responseData);
    }

    private function successfulResponse(ExternalRequestResponse $response): bool
    {
        $expectedResponseStatusCode = 0;

        return $response->requestStatusCode === Response::HTTP_OK
            && $response->responseStatusCode === $expectedResponseStatusCode;
    }

    private function updateCustomerContact(Client $user, array $requestBody): void
    {
        $contactType = data_get($requestBody, 'CustomerContacts.ContactTypeCode');
        $contactValue = data_get($requestBody, 'CustomerContacts.ConctactValue');

        if (empty($contactType) || empty($contactValue)) {
            $this->log->error('Could not update customer contact: Missing contact type or value.', $requestBody);
            return;
        }

        $updateMethods = [
            GetCustomerContactChannelsRepo::PHONE_TYPE_CODE => function () use ($user, $contactValue) {
                $this->updatePhone($user, $contactValue);
            },
            GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE => function () use ($user, $contactValue) {
                $this->updateEmail($user, $contactValue);
            },
        ];

        $updateMethod = $updateMethods[$contactType] ?? null;

        if ($updateMethod) {
            $updateMethod();
        } else {
            $this->log->error('Could not update customer contact: Unsupported contact type.', $requestBody);
        }
    }

    private function updatePhone(Client $user, string $phoneNumber): void
    {
        (new UpdatePhoneTask)->withUser($user)->withPhoneNumber($phoneNumber)->do();

        $this->log->info("Phone updated for customer_id {$user->client_id}");
    }

    private function updateEmail(Client $user, string $email): void
    {
        (new UpdateEmailTask)->withUser($user)->withEmail($email)->do();

        $this->log->info("Email updated for customer_id {$user->client_id}");
    }
}
