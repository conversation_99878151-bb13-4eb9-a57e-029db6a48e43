<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\DTO\ResultData;
use App\Traits\ActionLogger;
use App\Traits\HeaderIpTrait;
use Closure;

use function error_log;

use Exception;

use Illuminate\Http\Request;

use function str_replace;
use function strtoupper;

class LogApiMiddleware
{
    use HeaderIpTrait;
    use ActionLogger;

    protected $statusRedirect = 302;
    protected $statusNotFount = 401;
    protected $except = [
        'LOGOUTAPP',
    ];

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        return $next($request);
    }

    public function terminate(mixed $request, mixed $response)
    {
        $isException  = in_array(strtoupper(str_replace("api/", "", $request->path())), $this->except);
        if (!$isException) {
            try {
                if (get_class($response)  === "Illuminate\Http\Response") {
                    $data = $response->content();
                    if (is_string($data)) {
                        $data = json_decode($data);
                    }
                } else {
                    $data = $response->getData();
                }
            } catch (Exception $th) {
                error_log($th->getMessage());
            }

            $ip = $this->getIpHeader($request);

            $result = $data->result ?? "";
            if (empty($result)) {
                $result = $data->message ?? "";
            }
            $systeCode = $request->get("SystemCode");
            $status = $response->status() !== "$this->statusRedirect" ? $response->status() : $this->statusNotFount;

            $this->endpointLog(new ResultData(
                [
                    "endpointName"  => strtoupper(str_replace("api/", "", $request->path())),
                    "status"        => $status,
                    "system_code"   => $systeCode,
                    "deviceId"      => $request->get("device_id"),
                    "ip"            => $ip,
                    "result"        => $result,
                ]
            ));
        }
    }
}
