<?php

namespace App\Http\Middleware;

use App\Events\CheckFailedEvent;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateTokenForUserOrBiometryMiddleware
{
    public function __construct(
        protected ValidateTokenForUserMiddleware $userMiddleware,
        protected ValidateTokenForBiometryMiddleware $biometryMiddleware
    ) {
    }

    /**
     * Checks whether this request has valid credentials by leveraging
     * other middlewares.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $isUserTokenValid = $this->userMiddleware->isValid($request);

        if ($isUserTokenValid) {
            return $next($request);
        }

        $isBiometryTokenValid = $this->biometryMiddleware->isValid($request);

        if ($isBiometryTokenValid) {
            return $next($request);
        }

        CheckFailedEvent::dispatch('Unexpected error while validating token', get_class($this));

        return match (true) {
            is_null($isUserTokenValid) && is_null($isBiometryTokenValid) => response()->json(
                status: Response::HTTP_FORBIDDEN
            ),
            empty($isUserTokenValid) && empty($isBiometryTokenValid) => response()->json(
                status: Response::HTTP_UNAUTHORIZED
            ),
        };
    }
}
