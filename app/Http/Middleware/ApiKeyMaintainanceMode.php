<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\ApiKeys;
use App\Services\Crypt\Rsa;
use Illuminate\Http\Request;
use App\Enums\Message\Generate;
use App\Http\Responses\ApiResponse;
use Symfony\Component\HttpFoundation\Response;

class ApiKeyMaintainanceMode
{
    public function __construct(private Rsa $rsa, private ApiResponse $response)
    {
    }

    /**
     * Verify api key maintainance mode.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $apiKey = $request->header('APIKey');

        if (is_null($request->header('APIKey'))) {
            $apiKey = $request->apikey;
        }

        if ($apiKey = $this->rsa->decrypt($apiKey)) {
            $apiKeyMaintainanceMode = ApiKeys::where('maintenance_mode', 1)->first();

            if (empty($apiKeyMaintainanceMode) || $apiKeyMaintainanceMode->apikey !== $apiKey) {
                return $this->response->unauthorized([
                    'result' => Generate::NOT_AUTORIZATE->value,
                    'status' => Response::HTTP_UNAUTHORIZED
                ]);
            }

            return $next($request);
        }

        return $this->response->error([
            'result' => Generate::NOT_DESCRIPT->value,
            'status' => Response::HTTP_BAD_REQUEST
        ]);
    }
}
