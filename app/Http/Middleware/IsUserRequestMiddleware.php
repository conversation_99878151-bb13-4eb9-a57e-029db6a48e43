<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Factory\ClientFactory;
use App\Models\Client;

use function array_key_exists;

use Closure;

use function count;

use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Http\Request;

use function in_array;
use function is_array;
use function is_null;
use function strlen;

class IsUserRequestMiddleware
{
    /**
     * The authentication factory instance.
     *
     * @var Auth
     */
    protected $auth;
    protected $messageNotAutorizated = "No autorizado.";
    protected $statusNotAutorizate = 401;
    protected $statusBadRequest = 400;
    protected $miniArrayRequest = 1;
    protected $miniContentString = 0;

    /**
     * Create a new middleware instance.
     */
    public function __construct(Auth $auth, private readonly ClientFactory $clientFactory)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if ($this->auth->guard('api')->check()) {
            if (count($request->all()) < $this->miniArrayRequest
            && strlen($request->getContent()) > $this->miniContentString) {
                return response()->json([
                    "message" => "Format Incorrect",
                    "status" => $this->statusBadRequest
                ], $this->statusBadRequest);
            }
            $client = Client::find($this->auth->guard('api')->user()->id);
            //Search start
            $customerId       = $this->search($request->all(), ['customer_id', 'CustomerId']);
            $requestCard      = $this->search($request->all(), ['CCardId', 'CreditCardId']);
            $requestCustomer  = $this->search($request->all(), ['CustomerReferenceId']);
            $requestAddressId = $this->search($request->all(), ['AddressId']);
            $requestContacId  = $this->search($request->all(), ['CustomerContactId']);
            //Search end

            //Find client autenticate
            $isNotClient = $client && $customerId && $client->client_id !== $customerId;

            //Find Id autenticate with services
            $haveRequest = $requestCard || $requestCustomer || $requestAddressId || $requestContacId;

            $response    = null;
            if (!$isNotClient && $client && $haveRequest) {
                $response = $this->getData('?Query=CustomerId&Value=' . $client->client_id);
            } elseif (!$isNotClient && $requestCard && $haveRequest) {
                $response = $this->getData('?Query=CCardID&Value==' . $requestCard);
            }

            $isNotCard     = $this->searchArray(
                !is_null($client),
                $requestCard,
                $response,
                "Cards",
                "CCarId"
            );
            $isNotCustomer = $this->searchArray(
                !is_null($client),
                $requestCustomer,
                $response,
                "CustomerReferences",
                "CustomerReferenceId"
            );
            $isNotAddress  = $this->searchArray(
                !is_null($client),
                $requestAddressId,
                $response,
                "Address",
                "AddressId"
            );
            $isNotContact  = $this->searchArray(
                !is_null($client),
                $requestContacId,
                $response,
                "CustomerContacts",
                "CustomerContactId"
            );
            if ($isNotClient || $isNotCard || $isNotCustomer || $isNotAddress || $isNotContact) {
                return response()->json(
                    [
                    "message" => $this->messageNotAutorizated,
                    "status" => $this->statusNotAutorizate
                    ],
                    $this->statusNotAutorizate
                );
            }
        }

        return $next($request);
    }

    protected function search(mixed $search, array $keyPrimary): mixed
    {
        if (is_array($search)) {
            foreach ($search as $key => $value) {
                if (in_array($key, $keyPrimary, true)) {
                    return $value;
                }
                if (is_array($value)) {
                    return $this->search($value, $keyPrimary);
                }
            }
        }

        return null;
    }

    protected function searchArray(
        bool $isAutenticate,
        mixed $search,
        array | null $response,
        string $pivot,
        string $keyPivot
    ): ?bool {
        $isHere = true;
        if (
            $isAutenticate &&
            $search &&
            is_array($response) &&
            array_key_exists("Data", $response) &&
            !is_null($response["Data"]) &&
            array_key_exists($pivot, $response["Data"])
        ) {
            foreach ($response['Data'][$pivot] as $key => $value) {
                if ($value[$keyPivot] == $search) {
                    $isHere = false;
                }
            }
            return $isHere;
        }

        if (is_array($response) &&
            array_key_exists("Data", $response) &&
            is_null($response["Data"])) {
            // data not found
            return $isHere;
        }

        return false;
    }

    protected function getData(string $parameters): mixed
    {
        return $this->clientFactory->customeResponse(
            "CusInfo",
            $parameters,
            "",
            "",
            ""
        )["content"];
    }
}
