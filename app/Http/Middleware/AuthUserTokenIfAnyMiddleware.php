<?php

namespace App\Http\Middleware;

use App\Helpers\Box;
use App\Models\Client;
use Closure;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Http\Request;

/**
 * This middleware always succeeds.
 *
 * Auths a user by matching the token in the request with one
 * in the DB if any.
 *
 * This middleware does not validate the token, for that use
 * ValidateTokenMiddleware.
 */
class AuthUserTokenIfAnyMiddleware
{
    /**
     * Auth manager.
     */
    private Guard $auth;

    public function __construct(AuthManager $authManager)
    {
        $this->auth = $authManager->guard('api');
    }

    /**
     * Auths a user based on the token in the request or nothing if not found.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        Box::put($request->bearerToken())
            ->peek(fn($token) => Client::byBaesToken($token))
            ->peek(fn($user) => $this->auth->setUser($user));

        return $next($request);
    }
}
