<?php

namespace App\Http\Middleware;

use App\Dtos\ExternalAuthBiometry;
use App\Dtos\ExternalAuthUser;
use App\Events\CheckFailedEvent;
use App\Http\Responses\ApiResponse;
use App\Tasks\Auth\ValidateTokenTask;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

abstract class ValidateTokenMiddleware
{
    /**
     * Headers the request should have.
     *
     * Authorization is already included.
     *
     * @var array
     */
    public const REQUIRED_HEADERS = [];

    public function __construct(protected ValidateTokenTask $task, protected ApiResponse $response)
    {
    }

    /**
     * Checks whether this request has valid credentials by inspecting
     * the headers.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $isRequestValid = $this->isValid($request);

        if (is_null($isRequestValid)) {
            return response()->json(status: Response::HTTP_FORBIDDEN);
        }

        if (empty($isRequestValid)) {
            CheckFailedEvent::dispatch(static::getCheckFailedMessage(), get_class($this));

            return $this->response->unauthorized();
        }

        return $next($request);
    }

    /**
     * Checks whether this request has valid credentials by inspecting
     * the headers.
     *
     * @param \Illuminate\Http\Request $request
     * @return bool
     */
    public function isValid(Request $request): ?bool
    {
        if (!$this->hasValidHeaders($request)) {
            return false;
        }

        return $this
            ->task
            ->withAuthData($this->getDataFromRequest($request))
            ->do();
    }

    /**
     * Extracts data needed for the validation from the request.
     *
     * @param Request $request
     * @return ExternalAuthBiometry|ExternalAuthUser
     */
    abstract protected function getDataFromRequest(Request $request): ExternalAuthBiometry|ExternalAuthUser;

    /**
     * Whether $request contains the headers in $this->requiredHeaders plus
     * Authorization.
     *
     * @param Request $request
     * @return bool
     */
    protected function hasValidHeaders(Request $request): bool
    {
        if (empty($request->bearerToken())) {
            return false;
        }

        foreach (static::REQUIRED_HEADERS as $header) {
            if (empty($request->header($header))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Returns check failed message for debugging.
     *
     * @return string
     */
    abstract protected static function getCheckFailedMessage(): string;
}
