<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Auth\AuthManager;
use App\Http\Responses\ApiResponse;
use Illuminate\Contracts\Auth\Guard;

/**
 * Auths a user by matching the token in the request with one
 * in the DB.
 *
 * This middleware does not validate the token, for that use
 * ValidateTokenMiddleware.
 */
class AuthUserTokenMiddleware
{
    /**
     * Auth manager.
     */
    private Guard $auth;

    public function __construct(private AuthManager $authManager, private ApiResponse $response)
    {
        $this->auth = $authManager->guard('api');
    }

    /**
     * Auths a user based on the token in the request.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $token = $request->bearerToken();
        if (empty($token)) {
            return $this->response->unauthorized();
        }

        $user = Client::byBaesToken($token);
        if (empty($user)) {
            return $this->response->unauthorized();
        }

        $this->auth->setUser($user);

        return $next($request);
    }
}
