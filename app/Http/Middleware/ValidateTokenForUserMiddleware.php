<?php

namespace App\Http\Middleware;

use App\Dtos\ExternalAuth;
use App\Dtos\ExternalAuthUser;
use Illuminate\Http\Request;

class ValidateTokenForUserMiddleware extends ValidateTokenMiddleware
{
    /**
     * Headers the request should have.
     *
     * Authorization is already included.
     *
     * @var array
     */
    public const REQUIRED_HEADERS = [
        ExternalAuth::DEVICE_HEADER,
        ExternalAuthUser::USER_AUTH_HEADER,
    ];

    /**
     * Extracts data needed for the validation from the request.
     *
     * @param Request $request
     * @return ExternalAuthUser
     */
    protected function getDataFromRequest(Request $request): ExternalAuthUser
    {
        return ExternalAuthUser::fromRequest($request);
    }

    /**
     * Returns check failed message for debugging.
     *
     * @return string
     */
    protected static function getCheckFailedMessage(): string
    {
        return 'Not a registered user';
    }
}
