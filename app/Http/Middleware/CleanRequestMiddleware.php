<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;

use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CleanRequestMiddleware
{
    protected const EXCLUDED_ENDPOINTS = [
        'BIOMETRY',
        'ADDITIONALCCARDS',
    ];

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $isExcluded = $this->endpointIsExcluded($request->path());

        if ($this->bodyIsEmpty($request->all(), $request->getContent()) && !$isExcluded) {
            return response()->json([
                "message" => 'Format Incorrect',
                "status"  => Response::HTTP_BAD_REQUEST,
            ], Response::HTTP_BAD_REQUEST);
        }

        if (!$isExcluded) {
            $sanitizedRequest = $this->sanitize($request->all());
            $request->replace($sanitizedRequest);
        }

        return $next($request);
    }

    protected function bodyIsEmpty(array $formData, string $jsonContent): bool
    {
        return empty($formData) && strlen($jsonContent) > 0;
    }

    protected function endpointIsExcluded(string $path): bool
    {
        return in_array(strtoupper(str_replace("api/", "", $path)), self::EXCLUDED_ENDPOINTS);
    }

    protected function sanitize(mixed $value): mixed
    {
        return match (true) {
            is_array($value) => array_map(fn ($nestedValues) => $this->sanitize($nestedValues), $value),
            is_string($value) => strip_tags($value),
            default => $value,
        };
    }
}
