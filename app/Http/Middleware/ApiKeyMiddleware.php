<?php

namespace App\Http\Middleware;

use App\Events\CheckFailedEvent;
use App\Http\Responses\ApiResponse;
use App\Services\Crypt\Rsa;
use Closure;
use Illuminate\Http\Request;

class ApiKeyMiddleware
{
    public function __construct(
        private readonly Rsa $rsa,
        private readonly ApiResponse $response
    ) {}

    /**
     * Checks that the header APIKey contains a valid value and that it is
     * correct.
     *
     * @param \Illuminate\Http\Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $encryptedApiKey = $request->header('APIKey');

        $improperlyFormattedApiKey = false;
        if (empty($encryptedApiKey)) {
            CheckFailedEvent::dispatch('APIKey header is not present', get_class($this));
            $improperlyFormattedApiKey = true;
        } elseif (!$this->rsa->canDecrypt($encryptedApiKey)) {
            CheckFailedEvent::dispatch('Can\'t decrypt APIKey header', get_class($this));
            $improperlyFormattedApiKey = true;
        }

        if ($improperlyFormattedApiKey) {
            return $this->response->unauthorized();
        }

        $apiKey = $this->rsa->decrypt($encryptedApiKey);
        if ($apiKey != config('app.api.key')) {
            CheckFailedEvent::dispatch('APIKey header does not match', get_class($this));
            return $this->response->unauthorized();
        }

        return $next($request);
    }
}
