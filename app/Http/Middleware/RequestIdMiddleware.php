<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Log\LogManager as Log;
use Symfony\Component\HttpFoundation\Response;

class RequestIdMiddleware
{
    public function __construct(private Log $log)
    {
    }

    /**
     * Adds a request ID to every subsequent log call.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestId = Str::uuid()->toString();

        $this
            ->log
            ->withContext(['request_id' => $requestId]);

        $this
            ->log
            ->info('Serving request', [
                'method' => $request->method(),
                'path' => $request->path(),
            ]);

        $response = $next($request);
        if (!App::environment('production') || $response->status() >= Response::HTTP_BAD_REQUEST) {
            $response->header('Request-Id', $requestId);

            $body = 'Not a JSON response';
            if ($this->isJson($response->getContent())) {
                $body = json_decode($response->getContent(), true);
            }
            $this
                ->log
                ->debug('Response to be sent', [
                    'response' => $body,
                    'status' => $response->getStatusCode(),
                ]);
        }

        return $response;
    }

    /**
     * Whether $string is valid JSON.
     *
     * @param string
     * @return bool
     */
    private function isJson(string $string): bool
    {
        json_decode($string);

        return json_last_error() === JSON_ERROR_NONE;
    }
}
