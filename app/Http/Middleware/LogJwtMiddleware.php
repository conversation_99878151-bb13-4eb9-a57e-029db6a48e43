<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\DTO\ResultData;
use App\Traits\ActionLogger;
use App\Traits\HeaderIpTrait;
use Closure;
use Exception;

use function get_class;

use Illuminate\Http\Request;

use function is_string;
use function str_replace;
use function strtoupper;

class LogJwtMiddleware
{
    use HeaderIpTrait;
    use ActionLogger;

    protected $statusException = "302";
    protected $statusNotFount = 401;
    protected $except = [
        'LOGOUTAPP',
    ];

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        return $next($request);
    }

    public function terminate(mixed $request, mixed $response)
    {
        $clientId = auth('api')->check() ? auth('api')->id() : 0;
        $isException  = in_array(strtoupper(str_replace("api/", "", $request->path())), $this->except);
        $status = $response->status() !== $this->statusException ? $response->status() : $this->statusNotFount;

        if (!$isException) {
            $data = null;
            try {
                if (get_class($response)  === "Illuminate\Http\Response") {
                    $data = $response->content();
                    if (is_string($data)) {
                        $data = json_decode($data);
                    }
                } else {
                    $data = $response->getData();
                }
            } catch (Exception $th) {
                error_log($th->getMessage());
            }


            $ip = $this->getIpHeader($request);

            $result = $data->result ?? "";
            if (empty($result)) {
                $result = $data->message ?? "";
            }

            $this->registerClientApp(new ResultData(
                [
                    "endpointName"   => strtoupper(str_replace("api/", "", $request->path())),
                    "status"         => $status,
                    "clientId"       => $clientId,
                    "deviceId"       => $request->get("device_id"),
                    "ip"             => $ip,
                    "result"         => $result,
                ]
            ));
        }
    }
}
