<?php

namespace App\Events;

use App\Contracts\DeviceEvent;
use App\Contracts\TokenEvent;
use App\Contracts\UserEvent;
use App\Dtos\ExternalToken;
use App\Dtos\Login;
use App\Models\Client;
use App\Models\Device;
use App\Traits\HasToken;
use App\Traits\HasUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SuccessfulLoginEvent implements TokenEvent, UserEvent, DeviceEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;
    use HasUser;
    use HasToken;

    public function __construct(
        public readonly Client $user,
        public readonly Login $login,
        public readonly ExternalToken $token,
    ) {
    }

    /**
     * Returns the device.
     *
     * @return Device
     */
    public function getDevice(): Device
    {
        return Device::firstWhere([
            'device_id' => $this->login->device->id,
            'client_id' => $this->user->id,
        ]);
    }
}
