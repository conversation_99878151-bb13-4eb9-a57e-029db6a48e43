<?php

namespace App\Events;

use App\Contracts\DeviceEvent;
use App\Contracts\TokenEvent;
use App\Contracts\UserEvent;
use App\Dtos\ExternalToken;
use App\Models\Client;
use App\Models\Device;
use App\Traits\HasDevice;
use App\Traits\HasToken;
use App\Traits\HasUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SuccessfulBiometricLoginEvent implements TokenEvent, UserEvent, DeviceEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;
    use HasUser;
    use HasToken;
    use HasDevice;

    public function __construct(
        public readonly Client $user,
        public readonly Device $device,
        public readonly ExternalToken $token,
    ) {
    }
}
