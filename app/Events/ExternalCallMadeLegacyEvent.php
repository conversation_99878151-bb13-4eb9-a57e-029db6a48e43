<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ExternalCallMadeLegacyEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly string $baseUrl,
        public readonly string $gatewayService,
        public readonly string $requestMethod,
        public readonly array $requestHeaders,
        public readonly mixed $requestParameters,
        public readonly string $requestBody,
        public readonly ?string $responseStatus,
        public readonly ?array $responseBody = []
    ) {
    }
}
