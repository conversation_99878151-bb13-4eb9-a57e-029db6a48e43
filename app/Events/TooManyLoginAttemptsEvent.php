<?php

namespace App\Events;

use App\Contracts\RequestEvent;
use App\Traits\HasRequest;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;

class TooManyLoginAttemptsEvent implements RequestEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;
    use HasRequest;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(
        public readonly Request $request,
        public readonly string $nickname,
    ) {
    }
}
