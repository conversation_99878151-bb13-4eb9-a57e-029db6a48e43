<?php

namespace App\Events;

use App\Contracts\RequestEvent;
use App\Traits\HasRequest;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;

class FailedToBiometricLoginDueToLackOfPublicKeyEvent implements RequestEvent
{
    use HasRequest;
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        public readonly Request $request,
        public readonly string $deviceId,
        public readonly string $deviceName,
        public readonly string $signedChallenge,
    ) {
    }
}
