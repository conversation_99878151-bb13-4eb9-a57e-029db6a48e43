<?php

namespace App\Events;

use App\Contracts\RequestEvent;
use App\Traits\HasRequest;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;

class TooManyBiometricLoginAttemptsEvent implements RequestEvent
{
    use HasRequest;
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(public readonly Request $request)
    {
    }
}
