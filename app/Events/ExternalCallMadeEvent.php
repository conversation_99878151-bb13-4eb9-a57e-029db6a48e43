<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ExternalCallMadeEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        public readonly string $baseUrl,
        public readonly string $path,
        public readonly string $repo,
        public readonly int $status,
        public readonly array $requestHeaders,
        public readonly array $requestBody,
        public readonly array $requestQueryParams,
        public readonly array $responseHeaders,
        public readonly array $responseBody,
    ) {
    }
}
