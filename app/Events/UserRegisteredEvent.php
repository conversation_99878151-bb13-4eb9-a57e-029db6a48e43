<?php

namespace App\Events;

use App\Dtos\LoginCredentials;
use App\Models\Client;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRegisteredEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        public readonly Client $user,
        public readonly LoginCredentials $credentials,
    ) {
    }
}
