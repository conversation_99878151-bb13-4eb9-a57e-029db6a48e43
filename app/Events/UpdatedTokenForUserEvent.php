<?php

namespace App\Events;

use App\Contracts\TokenEvent;
use App\Contracts\UserEvent;
use App\Dtos\ExternalToken;
use App\Models\Client;
use App\Traits\HasToken;
use App\Traits\HasUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UpdatedTokenForUserEvent implements TokenEvent, UserEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;
    use HasUser;
    use HasToken;

    public function __construct(
        public readonly Client $user,
        public readonly ExternalToken $token,
    ) {

    }
}
