<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Twilio\Rest\Conversations\V1\ConversationInstance as TwilioConversation;

class ConversationCreatedEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        public readonly TwilioConversation $conversation,
        public readonly string $identity,
    ) {
    }
}
