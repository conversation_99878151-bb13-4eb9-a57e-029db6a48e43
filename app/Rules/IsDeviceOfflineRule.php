<?php

namespace App\Rules;

use App\Models\Client;
use App\Models\Device;
use Illuminate\Contracts\Validation\ValidationRule;

class IsDeviceOfflineRule implements ValidationRule
{
    public function __construct(private Client $client)
    {
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        /** @var Device */
        $device = $this->client->devices()->where('device_id', $value)->first();

        if ((int) $device->online !== Device::OFFLINE) {
            $fail(__('rules.is_device_offline'));
        }
    }
}
