<?php

namespace App\Rules;

use App\Dtos\OtpValidation;
use App\Tasks\ValidateOtpTask;
use Illuminate\Contracts\Validation\ValidationRule;

class IsOtpRule implements ValidationRule
{
    protected string $message;

    public function __construct(
        private ValidateOtpTask $task,
        private ?string $sharedKey,
    ) {
        $this->message = trans('rules.is_otp');
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (empty($this->sharedKey)) {
            $fail($this->message);
            return;
        }

        $passes = $this->task
            ->withOtpData(OtpValidation::make($this->sharedKey, $value))
            ->do();

        if (!$passes) {
            $fail($this->message);
        }
    }
}
