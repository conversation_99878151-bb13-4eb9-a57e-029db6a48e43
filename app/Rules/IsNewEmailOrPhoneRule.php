<?php

namespace App\Rules;

use App\Tasks\CheckEmailOrPhoneIsNewTask;
use Illuminate\Contracts\Validation\ValidationRule;

class IsNewEmailOrPhoneRule implements ValidationRule
{
    public function __construct(private CheckEmailOrPhoneIsNewTask $task)
    {
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        $isValid = $this->task
            ->withEmailOrPhone($value)
            ->do();

        if (!$isValid) {
            $fail(trans('rules.is_new_contact'));
        }
    }
}
