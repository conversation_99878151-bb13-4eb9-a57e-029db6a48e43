<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\ValidationRule;

class IsLatitudeRule implements ValidationRule
{
    protected string $message;

    public function __construct(protected bool $isNullValid = false)
    {
        $this->message = trans('rules.is_latitude');
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if ($this->isNullValid && is_null($value)) {
            return;
        }

        if (!is_numeric($value)) {
            $fail($this->message);
            return;
        }

        $latitude = (float) $value;

        if (-90 > $latitude || $latitude > 90) {
            $fail($this->message);
        }
    }
}
