<?php

namespace App\Rules;

use App\Models\Client;
use Illuminate\Contracts\Validation\ValidationRule;

class IsDeviceOfUserRule implements ValidationRule
{
    public function __construct(private Client $client)
    {
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        $device = $this->client->devices()->where('device_id', $value)->exists();

        if (empty($device)) {
            $fail(__('rules.is_device_of_user'));
        }
    }
}
