<?php

namespace App\Rules;

use App\Tasks\Surveys\CheckSurveyAndOptionRelationTask;
use Illuminate\Contracts\Validation\ValidationRule;

class IsOptionOfSurveyRule implements ValidationRule
{
    protected string $message;

    public function __construct(
        private CheckSurveyAndOptionRelationTask $task,
        private mixed $surveyId,
    ) {
        $this->message = trans('rules.is_survey_and_option_related');
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (!is_numeric($this->surveyId)) {
            $fail($this->message);
        }

        $passes = $this->task
            ->withSurveyId($this->surveyId)
            ->withOptionId($value)
            ->do();

        if (!$passes) {
            $fail($this->message);
        }
    }
}
