<?php

namespace App\Rules;

use App\Models\Client;
use IntlDateFormatter;
use App\Enums\DateFormat;
use App\Services\Configurations;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Verify if the client password is valid aplaying all the rules for the Super Intendancy.
 * This class was created to make easier to apply rules to the password validation.
 */
class SuperIntendancyRules implements ValidationRule
{
    /**
     * Client instance with data given by BAES service.
     *
     * @var Client
     */
    private Client $client;

    private Configurations $configurations;

    /**
     * Basic case rule validation for the password.
     * If need to write another rule without client info put it here
     *
     * @var array
     */
    private $basicCaseRules = [
        '(?=.*?[A-Z])(?=.*?[a-z])(?=.*[0-9])',      //Basic password rule
        '(?=.*[0-9])(?=.*?[#_¿?!¡@$%^+&\/\.*-])',   //Special chars allowed
        '(?!.*(.)\1{2})',                           //No more than 2 repited chars
    ];

    /**
     * Enable or disable client data to leet speak verification
     *
     * @var boolean
     */
    private $checkLeetSpeakDataClient = false;

    /**
     * Enable or disable word list to leet speak verification
     *
     * @var boolean
     */
    private $checkLeetSpeakNotAllowedWords = false;

    /**
     * Basic leet speak chars
     *
     * @var array
     */
    private $leetSpeakReplace = [
        'A' => '4',
        'a' => '4',
        'E' => '3',
        'e' => '3',
        'I' => '1',
        'i' => '1',
        'O' => '0',
        'o' => '0',
        'S' => '5',
        's' => '5',
        'T' => '7',
        't' => '7',
        'J' => '7',
        'j' => '7',
    ];

    /**
     * Regex start case not sensitive
     *
     * @var string
     */
    private $rgxIns = "(?i)";

    /**
     * Regex end case not sensitive
     *
     * @var string
     */
    private $rgxInsEnd = "(?-i)";

    /**
     * Message error default
     *
     * @var string
     */
    private $defaultMessageError = '-El formato de contraseña no es válido.-';

    /**
     * Non allowed words.
     *
     * @var string
     */
    private $nonAllowedWords;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(Client $client)
    {
        $this->client = $client;

        $this->configurations = Configurations::getInstance();

        $this->nonAllowedWords = (string) $this
            ->configurations
            ->getConfigurations(key: 'PALABRAS_NO_PERMITIDAS_PASSWORD');
    }

    /**
     * Validates if a password is valid.
     */
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        $masterRegex = $this->buildMasterRegularExpression();

        if (!preg_match($masterRegex, $value)) {
            $passFormatMessage = (string) $this->configurations->getConfigurations('MENSAJE_PASS_INVALIDA');

            if (!empty($passFormatMessage)) {
                $fail($passFormatMessage);
                return;
            }

            $fail($this->defaultMessageError);
        }
    }

    /**
     * Create the regular expresion to verify if the password is valid.
     * It takes a variety of validations to build the final regular expresion.
     * First take an array of basic cases writen in $basicCaseRules and then joined in
     * the final regular epxresion with the rest of the rules.
     *
     * @return string
     */
    private function buildMasterRegularExpression(): string
    {
        return implode('', [
            '/^',
            $this->basicRules(),
            $this->notAllowDataClient(),
            $this->notAllowMarriedSurname(),
            $this->notAllowClientBirthdates(),
            $this->notAllowedWorlistConfiguration(),
            $this->leetSpeakDataClient(),
            $this->leetSpeakNotAllowedWords(),
            '/'
        ]);
    }

    /**
     * Build basic rules
     *
     * @return string
     */
    private function basicRules(): string
    {
        return implode('', $this->basicCaseRules);
    }

    /**
     * Not allow to include client info to the password such as DUI, NIT, phone number etc.
     *
     * @return string
     */
    private function notAllowDataClient(): string
    {
        $expression = $this->rgxIns;

        $NIT = str_replace('-', '', $this->client->nit);
        $DUI = str_replace('-', '', $this->client->dui);
        $phoneNumber = str_replace('-', '', $this->client->phone_number);

        $pDUI = $this->insertCharAtPosition($DUI, '-', 8);
        $pPhoneNumber = $this->insertCharAtPosition($phoneNumber, '-', 4);

        [$prefixNumber, $sufixNumber] = explode(separator: '-', string: $pPhoneNumber);

        $expression .= $this->regexParameter($NIT);
        $expression .= $this->regexParameter($DUI);
        $expression .= $this->regexParameter($pDUI);
        $expression .= $this->regexParameter($phoneNumber);
        $expression .= $this->regexParameter($pPhoneNumber);
        $expression .= $this->regexParameter($prefixNumber);
        $expression .= $this->regexParameter($sufixNumber);
        $expression .= $this->regexParameter($this->client->first_name);

        if (!empty($this->client->second_name)) {
            $expression .= $this->regexParameter($this->client->second_name);
        }

        $expression .= $this->regexParameter($this->client->first_surname);

        if (!empty($this->client->second_surname)) {
            $expression .= $this->regexParameter($this->client->second_surname);
        }

        $expression .= $this->rgxInsEnd;

        return $expression;
    }

    /**
     * Insert a char in a given string to separate it like a mask.
     * For example from '00000000' to '0000-0000'
     *
     * @param string $string
     * @param string $char
     * @param integer $position
     * @return string
     */
    private function insertCharAtPosition(string $string, string $char, int $position): string
    {
        return implode(
            separator: $char,
            array: str_split(
                string: $string,
                length: $position
            )
        );
    }

    /**
     * Not allow married surname if exists.
     *
     * @return string
     */
    private function notAllowMarriedSurname(): string
    {
        $marriedSurname = $this->client->married_surname;

        return !empty($marriedSurname)
            ? $this->rgxIns . $this->regexParameter(str: $marriedSurname) . $this->rgxInsEnd
            : "";
    }

    /**
     * Not allow to include a list of words defined in the 'PALABRAS_NO_PERMITIDAS_PASSWORD' configuration.
     *
     * @return string
     */
    private function notAllowedWorlistConfiguration(): string
    {
        $invalidWordsList = explode(
            separator: ',',
            string: $this->nonAllowedWords
        );

        $mapWordsToRegEx = implode(
            separator: '',
            array: array_map(
                callback: fn($word): string => $this->regexParameter(str: $word),
                array: $invalidWordsList
            )
        );

        return implode(
            separator: '',
            array: [
                $this->rgxIns,
                $mapWordsToRegEx,
                $this->rgxInsEnd,
            ]
        );
    }


    /**
     * Find the posible birthdate combinations to stablish de validation rule
     * in the regular expression for the password.
     *
     * @param string $birthDate
     * @return string
     */
    private function notAllowClientBirthdates(): string
    {
        $separators = ['-', '\/', '', '_', '\.'];
        $timestamp = strtotime(datetime: $this->client->birthdate);

        if (!$timestamp) {
            return "";
        }

        $date = (new \DateTime())->setTimestamp(timestamp: $timestamp);
        $birthDatePosibleCombinations = [];

        foreach (DateFormat::cases() as $format) {
            $formattedDate = $date->format(format: $format->value);

            foreach ($separators as $ch) {
                $birthDatePosibleCombinations[] = str_replace(search: '-', replace: $ch, subject: $formattedDate);

                if (preg_match('/[a-zA-Z]/', $formattedDate)) {
                    $formatter = new IntlDateFormatter(
                        'es_ES',
                        IntlDateFormatter::FULL,
                        IntlDateFormatter::FULL,
                        config('app.timezone'),
                        IntlDateFormatter::GREGORIAN,
                        $format->translationPattern()
                    );

                    $birthDatePosibleCombinations[] = str_replace(
                        search: '-',
                        replace: $ch,
                        subject: $formatter->format($date)
                    );
                }
            }
        }

        return $this->rgxIns . implode(
            separator: '',
            array: array_map(
                callback: fn($bdate) => $this->regexParameter($bdate),
                array: $birthDatePosibleCombinations
            )
        ) . $this->rgxInsEnd;
    }

    /**
     * Leet Speak in client info such as name or surname
     *
     * @return string
     */
    private function leetSpeakDataClient(): string
    {
        if (!$this->checkLeetSpeakDataClient) {
            return "";
        }

        $expression = $this->rgxIns;

        $expression .= $this->regexParameter(str: $this->leetSpeakFormat(str: $this->client->first_name));

        if (!empty($this->client->second_name)) {
            $expression .= $this->regexParameter(str: $this->leetSpeakFormat(str: $this->client->second_name));
        }

        $expression .= $this->regexParameter(str: $this->leetSpeakFormat(str: $this->client->first_surname));

        if (!empty($this->client->second_surname)) {
            $expression .= $this->regexParameter(str: $this->leetSpeakFormat(str: $this->client->second_surname));
        }

        $expression .= $this->rgxInsEnd;

        return $expression;
    }

    /**
     * Leet Speak in not allowed words list configuration
     *
     * @return string
     */
    private function leetSpeakNotAllowedWords(): string
    {
        if (!$this->checkLeetSpeakNotAllowedWords) {
            return "";
        }

        $invalidWordsList = explode(
            separator: ',',
            string: $this->nonAllowedWords
        );

        $mapWordsToRegEx = implode(
            separator: '',
            array: array_map(
                callback: fn($word): string => $this->regexParameter(str: $this->leetSpeakFormat(str: $word)),
                array: $invalidWordsList
            )
        );

        return $this->rgxIns . $mapWordsToRegEx . $this->rgxInsEnd;
    }

    /**
     * Build Leet Speak in given string
     *
     * @param string $str
     * @link https://es.wikipedia.org/wiki/Escritura_leet
     * @return string
     */
    private function leetSpeakFormat(string $str): string
    {
        return strtr($str, $this->leetSpeakReplace);
    }

    /**
     * Build rule regex string
     *
     * @param string $str
     * @return string
     */
    private function regexParameter(string $str): string
    {
        return "(?!.*$str)";
    }
}
