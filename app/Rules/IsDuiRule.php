<?php

namespace App\Rules;

use App\Services\Dui;
use Illuminate\Contracts\Validation\ValidationRule;

class IsDuiRule implements ValidationRule
{
    public function __construct(private readonly Dui $dui)
    {
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (!$this->dui->isValid($value)) {
            $fail(__('rules.is_dui'));
        }
    }
}
