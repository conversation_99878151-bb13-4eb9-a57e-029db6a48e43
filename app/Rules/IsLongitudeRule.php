<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class IsLongitudeRule implements ValidationRule
{
    protected string $message;

    public function __construct(protected bool $isNullValid = false)
    {
        $this->message = trans('rules.is_longitude');
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->isNullValid && is_null($value)) {
            return;
        }

        if (!is_numeric($value)) {
            $fail($this->message);
            return;
        }

        $latitude = (float) $value;

        if (-180 > $latitude || $latitude > 180) {
            $fail($this->message);
        }
    }
}
