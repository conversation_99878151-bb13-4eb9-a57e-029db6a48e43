<?php

namespace App\Dtos;

use App\Contracts\ContactChannel;
use App\DTO\ExternalRequestResponse;
use App\Repos\GetCustomerContactChannelsRepo;
use Illuminate\Support\Collection;

class ContactChannels extends Dto
{
    /**
     * Contact channels for a customer.
     *
     * Must be a collection of ContactChannel.
     */
    public readonly Collection $channels;

    public function __construct(ContactChannel ...$channels)
    {
        $this->channels = collect($channels);
    }

    public static function makeFromLegacyExternalResponse(ExternalRequestResponse $response): self
    {
        $rawChannels = data_get($response->data, 'ContactTypes', []);

        $channels = collect($rawChannels)->map(function ($rawChannel) {
            return match ($rawChannel['ContactTypeCode']) {
                GetCustomerContactChannelsRepo::PHONE_TYPE_CODE => new Phone(
                    data_get($rawChannel, 'CustomerContacts.0.ContactValue', '')
                ),
                GetCustomerContactChannelsRepo::EMAIL_TYPE_CODE => new Email(
                    data_get($rawChannel, 'CustomerContacts.0.ContactValue', '')
                ),
            };
        });

        return new self(...$channels->all());
    }

    /**
     * Returns the first phone number or $default.
     *
     * @param string $default
     * @return string
     */
    public function getFirstPhone(string $default = ''): string
    {
        return optional(
            $this->getFirstByType(Phone::type())
        )->get() ?? $default;
    }

    /**
     * Returns the first email address or $default.
     *
     * @param string $default
     * @return string
     */
    public function getFirstEmail(string $default = ''): string
    {
        return optional(
            $this->getFirstByType(Email::type())
        )->get() ?? $default;
    }

    /**
     * Returns the first ContactChannel that matches $type.
     *
     * @param string $type
     * @return null|ContactChannel
     */
    protected function getFirstByType(string $type): ?ContactChannel
    {
        return $this
            ->channels
            ->first(fn($channel) => $channel->typeIs($type));
    }

    /**
     * Constructs a new self from a collection.
     *
     * @param Collection $collection Of ContactChannel.
     * @return static
     */
    public static function fromCollection(Collection $collection): self
    {
        return self::make(...$collection->all());
    }
}
