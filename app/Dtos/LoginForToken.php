<?php

namespace App\Dtos;

use App\Traits\HasFactory;

class <PERSON>ginForToken extends Dto
{
    use HasFactory;

    public function __construct(
        public readonly LoginCredentials $credentials,
        public readonly string $deviceId,
        public readonly string $customerId,
    ) {
    }

    /**
     * Returns only $this->credentials as array.
     *
     * @return array
     */
    public function credentialsAsArray(): array
    {
        return $this
            ->credentials
            ->toArray();
    }

    /**
     * Transforms into a GenerateExternalToken DTO.
     *
     * @return GenerateExternalToken
     */
    public function toGenerateExternalToken(): GenerateExternalToken
    {
        return GenerateExternalToken::make(
            $this->credentials->nickname,
            $this->deviceId,
            $this->customerId,
        );
    }
}
