<?php

namespace App\Dtos;

use App\Contracts\ContactChannel as ContactChannelContract;

abstract class ContactChannel extends Dto implements ContactChannelContract
{
    protected const TYPE = 'default_type';

    /**
     * Returns the type of the contact medium.
     *
     * @return string
     */
    public static function type(): string
    {
        return static::TYPE;
    }

    /**
     * Returns whether the type of this medium matches $type.
     *
     * @return bool
     */
    public function typeIs(string $type): bool
    {
        return static::type() === $type;
    }
}
