<?php

namespace App\Dtos\Factories;

use App\Dtos\Answer;
use Illuminate\Support\Str;

class AnswerFactory extends DtoFactory
{
    /**
     * Creates a new DTO with dummy data.
     *
     * @return Answer
     */
    public function create(): Answer
    {
        return Answer::make(
            $this->faker->randomNumber(5, false),
            $this->faker->randomElement([null, Str::random()]),
        );
    }
}
