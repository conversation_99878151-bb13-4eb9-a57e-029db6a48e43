<?php

namespace App\Dtos\Factories;

use App\Dtos\Customer;
use App\Services\Dui;
use Illuminate\Support\Str;

class CustomerFactory extends DtoFactory
{
    /**
     * Creates a new DTO with dummy data.
     *
     * @return Customer
     */
    public function create(): Customer
    {
        $dui = (new Dui())->generate();

        return Customer::make(
            id: random_int(0, PHP_INT_MAX),
            firstName: Str::random(),
            secondName: null,
            firstSurname: Str::random(),
            secondSurname: null,
            marriedName: null,
            birthDate: null,
            dui: $dui,
            nit: $dui,
        );
    }
}
