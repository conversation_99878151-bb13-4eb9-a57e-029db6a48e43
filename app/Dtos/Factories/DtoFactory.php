<?php

namespace App\Dtos\Factories;

use App\Contracts\DtoFactory as DtoFactoryContract;
use App\Dtos\Dto;
use Faker\Factory as FakerFactory;
use Faker\Generator;
use Illuminate\Support\Collection;

abstract class DtoFactory implements DtoFactoryContract
{
    protected Generator $faker;

    public function __construct()
    {
        /**
         * We don't know if we are in a Laravel context
         * to rely on the container.
         */
        $this->faker = FakerFactory::create();
    }

    /**
     * Creates $n DTOs.
     *
     * @return Collection
     */
    public function createMany(int $n): Collection
    {
        return collect(range(1, $n))->map(fn () => static::create());
    }

    /**
     * Creates DTOs with $values. Any missing property is filled
     * with dummy data from $this->create().
     *
     * Passing and array of arrays returns that many DTOs.
     *
     * @param array $values
     * @return Collection
     */
    public function createWith(array $values): Dto|Collection
    {
        if ($this->isArrayOfArrays($values)) {
            return $this
                ->createMany(count($values))
                ->map(fn ($dto, $i) => $dto->duplicate($values[$i]));
        }

        return $this->create()->duplicate($values);
    }

    /**
     * Whether $value is an array that contains arrays.
     *
     * @param mixed $value
     * @return bool
     */
    private function isArrayOfArrays($value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        foreach ($value as $v) {
            if (!is_array($v)) {
                return false;
            }
        }

        return true;
    }
}
