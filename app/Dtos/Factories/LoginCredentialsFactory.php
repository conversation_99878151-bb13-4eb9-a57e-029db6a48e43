<?php

namespace App\Dtos\Factories;

use App\Dtos\LoginCredentials;

class LoginCredentialsFactory extends DtoFactory
{
    /**
     * Creates a new DTO with dummy data.
     *
     * @return LoginCredentials
     */
    public function create(): LoginCredentials
    {
        return LoginCredentials::make(
            $this->faker->userName(),
            $this->faker->password(),
        );
    }
}
