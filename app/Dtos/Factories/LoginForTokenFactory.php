<?php

namespace App\Dtos\Factories;

use App\Dtos\LoginCredentials;
use App\Dtos\LoginForToken;

class LoginForTokenFactory extends DtoFactory
{
    /**
     * Creates a new DTO with dummy data.
     *
     * @return LoginForToken
     */
    public function create(): LoginForToken
    {
        return LoginForToken::make(
            LoginCredentials::factory()->create(),
            $this->faker->uuid(),
            (string) $this->faker->randomNumber(9),
        );
    }
}
