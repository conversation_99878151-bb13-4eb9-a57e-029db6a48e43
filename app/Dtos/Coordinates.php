<?php

namespace App\Dtos;

class Coordinates extends Dto
{
    public function __construct(
        public readonly string $longitude, // x
        public readonly string $latitude, // y
    ) {
    }

    public static function fromFloat(float $longitude, float $latitude): self
    {
        return self::make((string) $longitude, (string) $latitude);
    }

    public static function zero(): self
    {
        return self::make('0', '0');
    }
}
