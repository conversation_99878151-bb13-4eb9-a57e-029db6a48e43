<?php

namespace App\Dtos;

use Illuminate\Http\Request;

class ExternalAuthBiometry extends ExternalAuth
{
    public const DUI_HEADER = 'DuiAuth';
    public const BIOMETRY_HEADER = 'BiometryAuth';

    public function __construct(
        string $deviceId,
        string $ip,
        string $token,
        public readonly string $dui,
        public readonly string $biometry,
    ) {
        parent::__construct($deviceId, $ip, $token);
    }

    /**
     * Construct a new self from a request.
     *
     * @param Request $request
     * @return self
     */
    public static function fromRequest(Request $request): self
    {
        return new self(
            $request->header(self::DEVICE_HEADER, ''),
            $request->ip(),
            $request->bearerToken(),
            $request->header(self::DUI_HEADER, ''),
            $request->header(self::BIOMETRY_HEADER, ''),
        );
    }
}
