<?php

namespace App\Dtos;

class RefreshToken extends Dto
{
    public function __construct(
        public readonly string $refreshToken,
        public readonly GenerateExternalToken $generateTokenData,
    ) {
    }

    /**
     * Makes a new self from an array that has properties as keys.
     *
     * @param array $values
     * @return self
     */
    public static function fromFlatArray(array $values): self
    {
        return new self(
            $values['refreshToken'],
            GenerateExternalToken::make(
                $values['nickname'],
                $values['deviceId'],
                $values['customerId'],
            ),
        );
    }
}
