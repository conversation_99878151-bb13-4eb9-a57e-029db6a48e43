<?php

namespace App\Dtos;

use App\Traits\HasFactory;

class Customer extends Dto
{
    use HasFactory;

    public function __construct(
        public readonly int $id,
        public readonly string $firstName,
        public readonly ?string $secondName,
        public readonly string $firstSurname,
        public readonly ?string $secondSurname,
        public readonly ?string $marriedName,
        public readonly ?string $birthDate,
        public readonly string $dui,
        public readonly string $nit,
    ) {
    }
}
