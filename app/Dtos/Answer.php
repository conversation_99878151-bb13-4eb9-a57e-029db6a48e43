<?php

namespace App\Dtos;

use App\Traits\HasFactory;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class Answer extends Dto
{
    use HasFactory;

    public function __construct(
        public readonly int $optionId,
        public readonly ?string $other,
    ) {
    }

    /**
     * Builds a new collection of self from a request.
     *
     * @param Request $request
     * @return Collection
     */
    public static function manyFromRequest(Request $request): Collection
    {
        $answers = collect();
        foreach ($request->answers as $answer) {
            $answers->push(self::fromArray($answer));
        }

        return $answers;
    }
}
