<?php

namespace App\Dtos;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionMethod;
use ReflectionProperty;

abstract class Dto implements Arrayable
{
    /**
     * Construct a new self from an array.
     *
     * @param array $props Either keyed or as list.
     * @return static
     */
    public static function fromArray(array $props): static
    {
        if (array_is_list($props)) {
            return new static(...$props);
        }

        $params = (new ReflectionMethod(static::class, '__construct'))
            ->getParameters();
        $args = [];
        foreach ($params as $parameter) {
            // Literal names as written in the class.
            if (array_key_exists($parameter->getName(), $props)) {
                $args[] = $props[$parameter->getName()];

                continue;
            }

            // If not, try to match them as snake case.
            $snakedName = Str::snake($parameter->getName());
            if (array_key_exists($snakedName, $props)) {
                $args[] = $props[$snakedName];
            }
        }

        return new static(...$args);
    }

    /**
     * Create a new instance.
     *
     * @param mixed $props
     * @return static
     */
    public static function make(...$props): static
    {
        return new static(...$props);
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->toKeyedArray();
    }

    /**
     * Get the instance as an array but with keys using snake case.
     *
     * @return array
     */
    final public function toSnakeCaseArray(): array
    {
        $props = [];
        foreach ($this->toKeyedArray() as $key => $value) {
            $props[Str::snake($key)] = $value;
        }

        return $props;
    }

    /**
     * Get the instance as an array but with keys using camel case.
     *
     * @return array
     */
    final public function toCamelCaseArray(): array
    {
        $props = [];
        foreach ($this->toKeyedArray() as $key => $value) {
            $props[Str::camel($key)] = $value;
        }

        return $props;
    }

    /**
     * Returns an array by mapping original key => new key of
     * $newKeys and the original values of those props in the DTO.
     *
     * That is: $newKeys = ['propKey' => 'newPropKey'] results in
     * an array ['newPropKey' => 'original value of propKey'].
     *
     * If $newKeys has keys that don't correspond to properties
     * of this DTO, they are ignored. $newKeys does not need to be
     * exhaustive, it does not need to have every property of this DTO.
     *
     * @param array $newKeys Of the form ['propKey' => 'newPropKey'].
     * @return array
     */
    public function toCustomArray(array $newKeys): array
    {
        $newKeyedProps = [];
        $oldKeyedProps = $this->toKeyedArray();
        foreach ($newKeys as $oldKey => $newKey) {
            if (!array_key_exists($oldKey, $oldKeyedProps)) {
                continue;
            }

            $newKeyedProps[$newKey] = $oldKeyedProps[$oldKey];
        }

        return $newKeyedProps;
    }

    /**
     * Like $this->toCustomArray, but it adds $extraValues at the end.
     *
     * @param array $keys
     * @param array $extraValues Will overwrite any DTO values with the same key.
     * @return array
     */
    public function toCustomArrayWith(array $keys, array $extraValues): array
    {
        return array_merge($this->toCustomArray($keys), $extraValues);
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    private function toKeyedArray(): array
    {
        $publicProps = (new ReflectionClass($this))
            ->getProperties(ReflectionProperty::IS_PUBLIC);
        $props = [];
        foreach ($publicProps as $prop) {
            $propName = $prop->getName();
            $props[$propName] = $this->{$propName};
        }

        return $props;
    }

    /**
     * Applies $transformer to each public property of this DTO
     * and returns a new DTO with the new values.
     *
     * @param callable $transformer A function fn (T $value, string $key): T
     * @return static
     */
    public function map(callable $transformer): static
    {
        $newProps = [];
        foreach ($this->toKeyedArray() as $key => $value) {
            $newProps[$key] = $transformer($value, $key);
        }

        return static::fromArray($newProps);
    }

    /**
     * Creates a new DTO with the values returned by $transformer.
     *
     * @param callable $transformer A function fn (array $values): array. The function
     * receives the values of the public properties keyed by name. Must return a similar
     * array.
     * @return static
     */
    public function transform(callable $transformer): static
    {
        return static::fromArray($transformer($this->toKeyedArray()));
    }

    /**
     * Shallow copies the values of this DTO into a new DTO.
     *
     * @param array $newValues If any, this values will be used for the duplicate instead
     * of the ones found in the original DTO.
     * @return static
     */
    public function duplicate(array $newValues = []): static
    {
        return static::fromArray(array_merge($this->toKeyedArray(), $newValues));
    }
}
