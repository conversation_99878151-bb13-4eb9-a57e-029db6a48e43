<?php

namespace App\Dtos;

use Illuminate\Http\Request;

class ExternalAuthUser extends ExternalAuth
{
    public const USER_AUTH_HEADER = 'UserAuth';
    public const USERNAME_HEADER = 'UserName';

    public function __construct(
        string $deviceId,
        string $ip,
        string $token,
        public readonly string $userId,
        public readonly string $username,
    ) {
        parent::__construct($deviceId, $ip, $token);
    }

    /**
     * Construct a new self from a request.
     *
     * @param Request $request
     * @return self
     */
    public static function fromRequest(Request $request): self
    {
        return new self(
            $request->header(self::DEVICE_HEADER, ''),
            $request->ip(),
            $request->bearerToken(),
            $request->header(self::USER_AUTH_HEADER, ''),
            $request->header(self::USERNAME_HEADER, ''),
        );
    }
}
