<?php

namespace App\Dtos;

use App\Traits\HasFactory;

class LoginCredentials extends Dto
{
    use HasFactory;

    public function __construct(
        public readonly string $nickname,
        public readonly string $password,
    ) {}

    public function toVBankCredentialsArray(): array
    {
        return [
            'Username' => $this->nickname,
            'Password' => $this->password
        ];
    }
}
