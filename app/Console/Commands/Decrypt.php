<?php

namespace App\Console\Commands;

use App\Services\Crypt\Rsa;
use Illuminate\Console\Command;

class Decrypt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'decrypt {data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Decrypts a base 64 encrypted string';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(Rsa $rsa)
    {
        $decryptedData = $rsa->decrypt($this->argument('data'));
        $this->line($decryptedData);

        return 0;
    }
}
