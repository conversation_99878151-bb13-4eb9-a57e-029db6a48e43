<?php

namespace App\Console\Commands;

use App\Services\Crypt\Rsa;
use Illuminate\Console\Command;

class Encrypt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'encrypt {data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Encrypts with a public key and returns the result as base 64';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(Rsa $rsa)
    {
        $encryptedData = $rsa->encrypt($this->argument('data'));
        
        $this->line($encryptedData);

        return 0;
    }
}
