<?php

namespace App\Console\Commands;

use App\Models\Device;
use App\Services\Crypt\Rsa;
use Illuminate\Console\Command;
use Tests\Traits\CreatesClients;

class MakeClient extends Command
{
    use CreatesClients;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:client';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates a dummy client';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(Rsa $rsa)
    {
        $user = $this->createClientWithSystemAndPassword();
        $device = Device::factory()->create(['client_id' => $user->id]);

        $this->line('ID: ' . $user->id);
        $this->line('Nickname: ' . $user->nickname);
        $this->line('Password: 123456');

        $this->line('Device ID: ' . $device->device_id);
        $this->line('Device name: ' . $device->device_name);

        $this->line('Encrypted nickname: ' . $rsa->encrypt($user->nickname));
        $this->line('Encrypted password: ' . $rsa->encrypt('123456'));

        return 0;
    }
}
