<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Contracts\QueueWorkerTaskCommandOption;
use App\Console\Commands\QueueTaskOptions\CreateQueueWorkerTask;
use App\Console\Commands\QueueTaskOptions\DeleteQueueWorkerTask;
use App\Console\Commands\QueueTaskOptions\StartQueueWorkerTask;
use App\Console\Commands\QueueTaskOptions\StopQueueWorkerTask;

class ManageQueueWorkerTask extends Command
{
    /**
     * The name and signature of the console command.
     * 
     * @return string
     */
    protected $signature = 'manage:queue-task {--C|create} {--S|start} {--E|end} {--D|delete}';

    /**
     * The console command description.
     * 
     * @return string
     */
    protected $description = 'Manage task that runs artisan queue:work command on Windows Task Scheduler';

    /**
     * Name of the task on Windows Task Scheduler.
     */
    protected string $taskName = 'Laravel Queue Worker Task';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $option = $this->getOption();

        $actionClass = $this->getActionClass($option);

        if (!class_exists($actionClass)) {
            $this->error('The given option is invalid.');
        }

        /** @var QueueWorkerTaskCommandOption */
        $action = app()->make($actionClass);

        $result = $action->execute($this->taskName);

        $this->info($result);
    }

    protected function getOption(): ?string
    {
        foreach (['create', 'start', 'end', 'delete'] as $option) {
            if ($this->option($option)) {
                return $option;
            }
        }

        return null;
    }

    protected function getActionClass(?string $option): string
    {
        return match ($option) {
            null, 'create' => CreateQueueWorkerTask::class,
            'start' => StartQueueWorkerTask::class,
            'end' => StopQueueWorkerTask::class,
            'delete' => DeleteQueueWorkerTask::class,
        };
    }
}
