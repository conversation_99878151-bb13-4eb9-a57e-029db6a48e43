<?php

namespace App\Console\Commands;

use App\Helpers\Box;
use App\Inputs\DeleteUserInput;
use App\Tasks\DeleteUserTask;
use Illuminate\Console\Command;

class DeleteClient extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:client
        {--id= : A comma separated list of IDs}
        {--dui= : A comma separated list of DUIs}
        {--customer-id= : A comma separated list of customer IDs (client_id)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deletes users in the DB by ID, DUI, or customer ID';

    /**
     * Deletes a user.
     *
     * @param DeleteUserTask $task
     * @return int
     */
    public function handle(
        DeleteUserTask $task,
        DeleteUserInput $input,
    ) {
        $users = $input->toUsers(
            Box::put($this->option('id'))->peek($this->toArrayFromCommaString(...)),
            Box::put($this->option('customer-id'))->peek($this->toArrayFromCommaString(...)),
            Box::put($this->option('dui'))->peek($this->toArrayFromCommaString(...)),
        );
        foreach ($users as $user) {
            $task
                ->withUser($user)
                ->forceDelete()
                ->do();
        }

        if ($users->isEmpty()) {
            $this->line('No users to delete');

            return 0;
        }

        $this->line('Deleted users:');
        foreach ($users as $user) {
            $this->line("id: {$user->id}, customer_id: {$user->client_id}, dui: {$user->dui}");
        }

        return 0;
    }

    private function toArrayFromCommaString(string $string): array
    {
        return explode(',', $string);
    }
}
