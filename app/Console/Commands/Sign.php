<?php

namespace App\Console\Commands;

use App\Services\Crypt\Rsa;
use Illuminate\Console\Command;

class Sign extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sign {data} {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Signs a string with the private key found in a file';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $privateKey = file_get_contents($this->argument('file'));
        $signature = Rsa::signWith($this->argument('data'), $privateKey);
        $this->line($signature);

        return 0;
    }
}
