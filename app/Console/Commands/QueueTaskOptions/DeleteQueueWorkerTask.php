<?php

declare(strict_types=1);

namespace App\Console\Commands\QueueTaskOptions;

use App\Contracts\QueueWorkerTaskCommandOption;

class DeleteQueueWorkerTask implements QueueWorkerTaskCommandOption
{
    public function execute(string $taskName): string
    {
        exec("powershell -Command Unregister-ScheduledTask -TaskName '{$taskName}'", $output, $returnCode);

        if ($returnCode !== 0) {
            return "Failed to delete the task.\n" . implode("\n", $output);
        }

        return 'Task deleted successfully.';
    }
}
