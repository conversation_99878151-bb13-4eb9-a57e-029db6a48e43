<?php

declare(strict_types=1);

namespace App\Console\Commands\QueueTaskOptions;

use App\Contracts\QueueWorkerTaskCommandOption;

class StopQueueWorkerTask implements QueueWorkerTaskCommandOption
{
    public function execute(string $taskName): string
    {
        exec("powershell -Command Stop-ScheduledTask -TaskName '{$taskName}'", $output, $returnCode);

        if ($returnCode !== 0) {
            return "Failed to stop the task.\n" . implode("\n", $output);
        }

        return 'Task stopped successfully.';
    }
}
