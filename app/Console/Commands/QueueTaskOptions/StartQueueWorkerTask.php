<?php

declare(strict_types=1);

namespace App\Console\Commands\QueueTaskOptions;

use App\Contracts\QueueWorkerTaskCommandOption;

class StartQueueWorkerTask implements QueueWorkerTaskCommandOption
{
    public function execute(string $taskName): string
    {
        exec("powershell -Command Start-ScheduledTask -TaskName '{$taskName}'", $output, $returnCode);

        if ($returnCode !== 0) {
            return "Failed to start the task.\n" . implode("\n", $output);
        }

        return 'Task started successfully.';
    }
}
