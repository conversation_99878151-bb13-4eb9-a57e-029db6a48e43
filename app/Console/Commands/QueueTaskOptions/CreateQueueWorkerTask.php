<?php

declare(strict_types=1);

namespace App\Console\Commands\QueueTaskOptions;

use App\Contracts\QueueWorkerTaskCommandOption;


class CreateQueueWorkerTask implements QueueWorkerTaskCommandOption
{
    protected const POWERSHELL_SCRIPT_FILENAME = "create_queue_worker_task.ps1";

    protected const QUEUE_WORKER_BATCH_FILENAME = "run_queue_worker.bat";

    /**
     * Creates a queue worker task in the Windows Task Scheduler.
     */
    public function execute(string $taskName): string
    {
        $logPath = dirname(
            config("logging.channels." . config("logging.default") . ".path")
        ) . "\queue_logs.log";

        $executable = base_path("cicd\\powershell\\" . self::QUEUE_WORKER_BATCH_FILENAME);

        $powershellScriptPath = base_path("cicd\\powershell\\" . self::POWERSHELL_SCRIPT_FILENAME);

        $command = "powershell -ExecutionPolicy ByPass " .
            "-File \"{$powershellScriptPath}\" " .
            "-name \"$taskName\" " .
            "-executable \"{$executable}\" " .
            "-arguments \"{$logPath}\"";

        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            return "An error ocurred:\n" . implode("\n", $output);
        }

        return "Task \"$taskName\" created successfully.";
    }
}
