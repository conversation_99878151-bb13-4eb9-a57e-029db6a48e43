<?php

namespace App\Console\Commands;

use App\Services\Crypt\Rsa;
use Illuminate\Console\Command;
use Tests\Traits\CreatesRsaKeys;

class MakeKeys extends Command
{
    use CreatesRsaKeys;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:keys';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates a valid RSA key pair';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(Rsa $rsa)
    {
        [$privateKey, $publicKey] = $this->createKeyPair();

        $this->line('Public key:');
        $this->line($publicKey);
        $this->line('Private key:');
        $this->line($privateKey);

        return 0;
    }
}
