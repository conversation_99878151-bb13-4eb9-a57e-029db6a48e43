# One App Backend

This project serves as an intermediary between Banco Atlántida’s internal services and its mobile applications for Android and iOS.

## Table of contents

1. [Installation](#installation)
    1. [Prerequisites](#prerequisites)
    2. [Common installation steps](#common-installation-steps-windows--linux)
    3. [Windows](#windows-server-installation)
    4. [Linux](#linux-server-installation)
    5. [Docker (Local Development)](#docker-local-development)
2. [Usage](#usage)
    1. [Managing Laravel Queues](#managing-laravel-queues)
        1. [Basics](#basics)
        2. [Running Artisan Queue Worker - Windows Task Scheduler](#running-artisan-queue-worker---windows)
            1. [Automated](#automated)
            2. [Manual](#manual)
        3. [Running Artisan Queue Worker - Linux with Supervisor](#running-artisan-queue-worker---linux)
            1. [Automated](#automated-1)
            2. [Manual](#manual-1)

## Installation

> **NOTE**: _This documentation is intended for deployment on servers currently used by us. For local installation without Docker, users should choose their preferred method of running PHP and Laravel applications on their respective operating systems. This documentation does not include steps to install any of the prerequisites listed below._

This project is designed to run on servers with Windows and Linux operating systems. Additionally, it includes the necessary configuration to operate within Docker containers. Therefore, the installation documentation is divided into three main sections: **Windows**, **Linux**, and **Docker**.

### Prerequisites

Before proceeding with the installation, ensure that the following requirements are met:

-   Apache
-   PHP 8.2+
-   PHP FPM (Currently used only in the UAT y Prod environments)
-   PHP SQL Server driver 5.11.1
-   Laravel 10
-   Composer
-   Docker (Local Development)

### Common Installation Steps (Windows & Linux).

There are a few common installation steps for Windows and Linux. These will be listed in this section and will be referenced as necessary from this and other sections. It is important to mention that these steps are not in any specific order and should be executed following the order from the installation section for the operating system the project is being installed on.

#### 1. Creating ZIP File for Deployment

Currently, there is only support for continuous integration because of this the deployments to the different servers are manual. Therefore, it is required to create a zip file for uploading the project to the server.

From you local machine with this project running as expected, create a `zip` of the project, excluding the following directories and files if they exist:

-   coverage
-   docs
-   docker-compose.yml
-   pasosInstalacion
-   applaudoJenkins
-   jenkins-env.json
-   sonar-project.properties
-   node_modules
-   .phpunit.result.cache
-   .gitignore
-   server.php

> **IMPORTANT**: _Do not remove the vendor directory from the zip as the servers probably don't have composer installed to be able to download the dependencies._.

#### 2. Running migrations

### Windows Server Installation

Follow the next steps to setup the project on a Windows Server:

1. Create a zip file of the project by following [Step 1: Creating Zip File for Deployment](#1-creating-zip-file-for-deployment) in the "Common Installation Steps" section.

2. Upload the zip file to the Windows Server. Since Active Directory users only have direct access to the `C:` drive, make sure to upload the file there first.

3. Stop the server using IIS Manager to prevent any traffic from reaching the server during the deployment, which could block file modifications.

4. Update the backup: delete the existing backup folder `ONE_API.bkp`, then rename the current ONE_API folder `ONE_API` to `ONE_API.bkp`.

5. Extract the zip file’s contents to a new directory named `ONE_API` and move it to `E:\`.

6. Copy the environment variables from the `ONE_API.bkp` directory to the new `ONE_API` directory, and make any necessary updates if there are new variables.

7. Set the correct permissions for the `IUSR` user by granting access to the `storage\logs` and `bootstrap\cache` directories.

8. Restart the server from the IIS Manager.

9. If there are new database migrations, run the `php artisan migrate` command from the root of the project in a terminal with administrative privileges.

10. Follow instructions to run **Laravel Queues** based on the operative system in the [Managing Laravel Queues](#managing-laravel-queues) section.

> **NOTE** _If you encounter issues where changes are not being applied, run the `php artisan optimize:clear` command to clear the project's cache_

### Linux Server Installation

Follow the steps below to set up the project on a Linux Server:

1. Create a zip file of the project by following [Step 1: Creating Zip File for Deployment](#1-creating-zip-file-for-deployment) in the "Common Installation Steps" section.

2. Upload the zip file to the Linux Server.

3. Copy the files on the server from the current version of the project to serve as a backup in case something goes wrong. Execute the following command to copy it to `/var/www/html-backup`

    ```shell
    sudo mv /var/www/html /var/www/html-backup/
    ```

4. Extract the uploaded zip file into the `/var/www/html` directory using the following command:

    ```shell
    # Replace /path/to/zip with the actual path where the zip file was uploaded
    sudo unzip /path/to/zip -d /var/www/html
    ```

5. Restore `/var/www/html` directory permissions running the following commands:

    ```shell
    sudo chown apache:apache -R /var/www/html
    sudo restorecon -R /var/www/html
    sudo chcon -R -t httpd_sys_rw_content_t /var/www/html
    ```

6. Replace environment variables inside `/var/www/html/.env` with the environment variables from `/var/www/html-backup/.env` and add new variables if necessary. **Important** to mention that in some environments we are running the `/var/www/html/.env` file must be removed as environment variables are stored on apache `/etc/httpd/conf.d/env.conf`, so any modfications should be made to this file instead.

7. If there are new database migrations, run the `php artisan migrate` command from the root of the project in a terminal with administrative privileges.

8. Restart `apache` and `php-fpm` services by running the following commands:

    ```shell
    sudo service httpd restart
    sudo service php-fpm restart
    ```

9. Follow instructions to run **Laravel Queues** based on the operative system in the [Managing Laravel Queues](#managing-laravel-queues) section.

> **NOTE** _If you encounter issues where changes are not being applied, run the `php artisan optimize:clear` command to clear the project's cache_

### Docker (Local Development)

Follow the steps below to setup the project with Docker for Local Development:

1. **Set Up Environment Variables**

    Create a .env file at the root of the project directory. You can copy the example environment file and modify it according to your needs:

    ```shell
    cp .env.example .env
    ```

2. **Build and Run Docker Containers**

    To run the application using Docker, you need to use docker-compose. A docker-compose.yml file is included in the project to facilitate this process. The docker-compose.yml file defines services for the web server (PHP), database, and other dependencies.

    Run the following command to build and start the Docker containers:

    ```bash
    docker-compose up -d --build
    ```

3. **Install PHP Dependencies**

    After the containers are up and running, you need to install the PHP dependencies using Composer. To do this, run the following command from your host machine as composer is not currently installed inside `tc_api_php` container:

    ```shell
    composer install
    ```

4. **Run Database Migrations**

    Next, run the Laravel migrations to set up the database schema. Execute the following command inside the `tc_api_php` container:

    ```shell
    docker-compose exec tc_api_php php artisan migrate
    # or in case you find permission errors
    docker-compose exec -u root tc_api_php php artisan migrate
    ```

    > **NOTE** _If this command fails you may need to manually create the database from the `db` container and then execute the command again._

## Usage

### Managing Laravel Queues

CCurrently, in the project, there are some parts of the code that need to be executed asynchronously to avoid blocking I/O or to be able to retry them if needed in case something goes wrong. This is achieved through the use of [Laravel Queues](https://laravel.com/docs/10.x/queues).

#### Basics

Laravel Queues allow you to defer the processing of time-consuming tasks, such as sending emails, to a later time. This feature is activated when the environment variable `QUEUE_DRIVER` is set to something other than sync, like database. In this setup, jobs are stored in the `jobs` table, and failed jobs are stored in the `failed_jobs` table.

**Create migrations to handle queues**: To create these tables, you can use the following Artisan commands to generate the necessary migrations in case they don't exist in the database:

```shell
php artisan queue:table # Creates jobs table migration
php artisan queue:failed-table # Creates failed_jobs table migration
php artisan migrate
```

**Run queue worker**: To run a queue worker (blocking the current terminal) execute the following command:

```shell
php artisan queue:work
```

**List failed jobs**: To list the failed jobs stored inside `faile_jobs` table in the database use the following command:

```shell
php artisan queue:failed
```

**Retry failed jobs**: To move a failed job back to the queue so it can be retried execute the following command:

```shell
php artisan queue:retry <failed-job-id> # retry specific failed job
# or
php artisan queue:retry all # retry all failed jobs
```

**Delete failed jobs**: To remove failed jobs from the `failed_jobs` table. Execute the following command:

```shell
php artisan queue:forget <failed-job-id> # Delete a specific failed job
# or
php artisan queue:flush # Delete all failed jobs
```

#### Running Artisan Queue Worker - Windows

##### Automated

To execute Laravel Artisan Queue Worker persistently on background on Windows Server you need to create a new Task in Windows Task Scheduler, this process has been automated using Laravel Commands to create this task creation, starting, stopping and deleting process. You can find the different commands down below.

To manage the task on Windows is done through the command `php artisan manage:queue-task` with the following arguments:

-   `-C|--create`: Creates the task in the task scheduler and starts it after creation.
-   `-S|--start`: Starts the task if it has been stopped.
-   `-E|--end`: Stops the task if needed.
-   `-D|--delete`: Deletes the task from the task scheduler if needed.

Eg.

```shell
php artisan manage:queue-task -C # or --create
php artisan manage:queue-task -S # or --start
php artisan manage:queue-task -E # or --end
php artisan manage:queue-task -D # or --delete
```

#### Manual

1. Open Window Task Scheduler and click "Create Task".

2. On the "General" Tab, set the name to "Laravel Queue Worker Task", on the "Security Option" section click "Change User or Group" and select "SYSTEM" as user and check the option "Run the task whether user is logged on or not"

3. Switch to "Triggers" tab and click "New". A new pop up will open, in the "Begin the task" selecto choose "At startup" and make sure the option "Enabled" is checked. Press "OK" once your done.

4. Switch to the "Actions" tab and click "New". In the popup set the "Action" to "Start a program" and point the "Program/Script" to "/cicd/powershell/run_queue_worker.bat" batch file. Press "OK" once your done.

5. Switch to the "Conditions" tab and unmark the options "Start the task only if the computer is on AC Power".

6. Switch to the "Settings" tab and mark the option "Allow task to be run on demand" and unmark any other option. At then end set the selector to "Do not start a new instance" in case the task is already running.

7. Finally press "OK" to create the task.

#### Running Artisan Queue Worker - Linux

#### Automated

To execute Laravel Artisan Queue Worker persistenltly on background on Linux is done through [supervisor](http://supervisord.org/). The configuration for supervisor and the supervisor process to run the queue worker can be done by executing the script `cicd/setup_supervisor.sh`:

Eg.

```shell
sudo chmod +x cicd/setup_supervisor.sh
bash cicd/setup_supervisor.sh
```

#### Manual

1. Install `supervisor`

    ```shell
    sudo yum install -y epel-release
    sudo yum install -y supervisor
    ```

2. Create a configuration file for Laravel queue worker in `/etc/supervisord.d/one-app-api.ini` for Red-Hat based distros or `/etc/supervisor/conf.d/one-app-api.conf` Debian based distros with the following content:

    ```shell
    [program:laravel-queue-worker]
    process_name=%(program_name)s_%(process_num)02d
    command=php /var/www/html/artisan queue:work --tries=3
    autostart=true
    autorestart=true
    user=root
    numprocs=1
    redirect_stderr=true
    stdout_logfile=/var/www/html/storage/logs/queue_logs.log
    ```

3. Enable and start supervisor

    ```shell
    sudo systemctl enable supervisord
    sudo systemctl start supervisord
    ```

4. Reload supervisor and apply new configuration

    ```shell
    sudo supervisorctl reread
    sudo supervisorctl update
    ```

#### Running Artisan Queue Worker - Docker

The current docker configuration automatically installs supervisor and the process configuration to run queues by default.
