<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\OTP\OTPController;
use App\Http\Controllers\API\Clients\LoginController;
use App\Http\Controllers\API\Clients\UsersController;
use App\Http\Controllers\API\ExternalRoutesController;
use App\Http\Controllers\API\Clients\ClientsController;
use App\Http\Controllers\API\Facephi\FacephiController;
use App\Http\Controllers\API\Auth\GetLastLoginController;
use App\Http\Controllers\API\Clients\ClientSystemController;
use App\Http\Controllers\API\Clients\RefreshTokenController;
use App\Http\Controllers\API\Auth\StoreBiometryKeyController;
use App\Http\Controllers\API\Clients\BiometricLoginController;
use App\Http\Controllers\API\Clients\ClientCredentialsController;
use App\Http\Controllers\API\Auth\FaceAuthenticationExtController;
use App\Http\Controllers\API\Auth\HomologateUserController;
use App\Http\Controllers\API\Auth\LogoutController;
use App\Http\Controllers\API\ClientPassword\ClientPasswordController;
use App\Http\Controllers\API\Devices\VerificationChallengeController;
use App\Http\Controllers\API\Clients\ClientNicknameFaceAuthController;
use App\Http\Controllers\API\LogNotifications\LogNotificationsController;

Route::get('matchsidescorefacephi', [FacephiController::class, 'score'])->middleware(['log', 'clean.api']);

Route::middleware(['log'])->group(function () {
    Route::post('login', LoginController::class);
    Route::post('biometric-login', BiometricLoginController::class);
});

Route::middleware('session')->group(function () {
    Route::get('getlastlogin', GetLastLoginController::class);
    Route::post('lockbyfaceauth', [ClientCredentialsController::class, 'lockClientByFaceAuth']);
    Route::post('homologate-user', HomologateUserController::class);
    Route::post('resetfirstpassword', [ClientPasswordController::class, 'resetFirstPassword']);
    Route::post('sendotpcode', [OTPController::class, 'sendOTPCode']);
    Route::post('savebiometricpublickey', StoreBiometryKeyController::class);
    Route::delete('users/registration', [UsersController::class, 'remove']);
    Route::post('validateuser', [ClientsController::class, 'validateUser']);
});

Route::middleware('global')->group(function () {
    Route::post('otpvalidatecode', [OTPController::class, 'otpValidateCode']);
    Route::post('resetpassword', [ClientPasswordController::class, 'resetPassword']);
    Route::post('validatepassword', [ClientPasswordController::class, 'validatePassword']);
    Route::post('FaceAuthenticationForNickname', ClientNicknameFaceAuthController::class);
    Route::post('faceauthentication-extension', FaceAuthenticationExtController::class);
});

Route::middleware('token.external.user.auth')->group(function () {
    Route::post('logoutapp', LogoutController::class);
});

Route::middleware('apikey.without.biometry')
    ->group(function () {
        Route::put('devices/{id}/challenge', VerificationChallengeController::class);
        Route::post('remembernickname', [ClientsController::class, 'RememberNickname']);
        Route::put('token', [ExternalRoutesController::class, 'refreshToken']);
        Route::put('token/user', RefreshTokenController::class);
    });

Route::middleware('apikey.with.biometry')
    ->group(function () {
        Route::post('addclientsystem', [ClientSystemController::class, 'store']);
        Route::post('lockclient', [ClientCredentialsController::class, 'lock']);
        Route::post('removeclientsystem', [ClientSystemController::class, 'destroy']);
        Route::post('OTPValidateCodeForNickname', [OTPController::class, 'otpValidateCodeForNickname']);
        Route::post('unlockclient', [ClientCredentialsController::class, 'unlock']);
        Route::post('savefirebasetoken', [LogNotificationsController::class, 'saveFirebaseToken']);
        Route::post('sendotpcodeapikey', [OTPController::class, 'sendOTPCode']);
        Route::post('storeclient', [UsersController::class, 'store']);
        Route::post('users', [UsersController::class, 'store']);
    });

Route::delete('users', [UsersController::class, 'destroyQuery']);
Route::delete('users/{id}', [UsersController::class, 'destroy']);
