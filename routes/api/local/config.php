<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\MaintenancePeriodsController;
use App\Http\Controllers\API\MaintenanceMode\MaintenanceModeController;
use App\Http\Controllers\API\Parameterizables\ParameterizablesController;

Route::get('maintenance-periods', MaintenancePeriodsController::class)->name('maintenance-periods');
Route::post('maintenancemode', [MaintenanceModeController::class, 'maintenanceMode'])->middleware('maintenanceApiKey');
Route::get('configuration', [ParameterizablesController::class, 'configuration'])->middleware('global');
