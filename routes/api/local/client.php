<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\Devices\DevicesController;
use App\Http\Controllers\API\Clients\ClientProfileController;
use App\Http\Controllers\API\ClientDevice\ClientDeviceController;

Route::middleware('session')->group(function () {
    Route::get('deviceslist', [ClientDeviceController::class, 'devices']);
    Route::delete('devices/{id}', [DevicesController::class, 'destroy']);
    Route::get('profile', [ClientProfileController::class, 'getProfile']);
});
