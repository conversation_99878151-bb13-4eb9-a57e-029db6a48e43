<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\Image\ImageController;
use App\Http\Controllers\API\ConversationsController;
use App\Http\Controllers\API\Surveys\SurveysController;
use App\Http\Controllers\API\Benefits\BenefitsController;
use App\Http\Controllers\API\Surveys\SurveyAnswersController;
use App\Http\Controllers\API\Advertisings\AdvertisingsController;
use App\Http\Controllers\API\LogNotifications\LogNotificationsController;
use App\Http\Controllers\API\Parameterizables\ParameterizablesController;

Route::get('imagery/{id}', [ImageController::class, 'images'])->name('api.image');
Route::get('advertisings', [AdvertisingsController::class, 'advertisings'])->middleware('session');

Route::middleware('apikey.with.biometry')->group(function () {
    Route::get('benefits', BenefitsController::class);
    Route::post('sendnotification', [LogNotificationsController::class, 'sendNotification']);
});

Route::middleware('global')->group(function () {
    Route::post('conversations', ConversationsController::class);
    Route::get('parameterizables', [ParameterizablesController::class, 'parameterizables']);
    Route::post('surveys/{id}/answers', SurveyAnswersController::class);
    Route::get('surveys/{id}', [SurveysController::class, 'show']);
    Route::get('surveys', [SurveysController::class, 'index']);
});
