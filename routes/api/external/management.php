<?php

use App\Enums\SecurityType;
use App\Helpers\ToolKit;

ToolKit::mapRoutes(
    security: SecurityType::APIKEY_WITH_BIOMETRY,
    routes: [
        'AFPDocuments',
        'AFPSignDocuments',
        'BaMessagesSendMessage',
        'ccapplications',
        'caapplicationst1',
        'caapplicationst2',
        'ccapplicationst3',
        'ccapplicationst4',
        'ccapplicationst5',
        'ccapplicationst6',
        'ccapplicationst7',
        'ccapplicationst8',
        'ccapplicationst10',
        'CCApplicationVerification',
        'CCApplicationPep',
        'SignDocuments',
    ]
);

ToolKit::mapRoutes(
    security: SecurityType::APIKEY_WITHOUT_BIOMETRY,
    routes: [
        'companies',
        'LogApplication',
    ]
);

ToolKit::mapRoutes(
    security: SecurityType::SESSION,
    routes: [
        'DigitalFile',
        'failurereports',
        'FraudProtectionDel',
        'FraudProtectionGet',
        'FraudProtectionPost',
        'FraudProtectionPut',
        'home',
        'LogApplications',
        'logapplicationsg',
        'LogManagementDetail',
        'LogManagements',
        'LogManagements',
        'ManagementDef',
        'Managements',
        'MenuAlerts',
        'ManCloseAccount',
        'ManNoRecived',
        'NoAlert',
        'TravelReportDel',
        'TravelReportGet',
        'TravelReportPost',
        'TravelReportPut',
        'UnrecognizedPurchaseReport',
        'ValidateStringsCoincidence',
        'punto-express/fields',
        'punto-express/balance-client',
        'punto-express/info-products',
        'punto-express/catalogs',
        'punto-express/categories',
        'punto-express/payment-service',
        'punto-express/balance-client-automatic',
        'punto-express/update-contract',
        'punto-express/get-transaction',
        'punto-express/get-list-contracts',
        'punto-express/get-listas-varias',
        'punto-express/get-info-service',
        'punto-express/get-balance',
        'punto-express/delete-contract',
        'punto-express/create-contract',
    ]
);

ToolKit::mapRoutes(
    security: SecurityType::GLOBAL,
    routes: [
        'catalogs',
        'Documents'
    ],
);
