<?php

use App\Enums\SecurityType;
use App\Helpers\ToolKit;

ToolKit::mapRoutes(
    security: SecurityType::SESSION,
    routes: [
        'AdditionalCCardLimit',
        'AdditionalCCards',
        'AvailblePoints',
        'cardactivation',
        'CardBlock',
        'CardCloseGet',
        'CardClosePut',
        'CardColor',
        'CardOptionGet',
        'CardOptionPost',
        'cardpin',
        'CardReplacement',
        'CardTracking',
        'CardTransactions',
        'CashbackAccount',
        'CashbackTransactions',
        'CreditCardStatement',
        'DebtConsolidationOffer',
        'DebtConsolidationQuery',
        'PaymentInInstallments',
        'PointsPayment',
        'PointStatement',
        'PointTransactions',
        'TransactionUnrecognizedPurchase',
    ],
);

ToolKit::mapRoutes(
    security: SecurityType::APIKEY_WITH_BIOMETRY,
    routes: 'EncryptedPins',
);

ToolKit::mapRoutes(
    security: SecurityType::NONE,
    routes: [
        'CCApplicationDiscarded',
        'CardStatus'
    ],
);
