<?php

use App\Enums\SecurityType;
use App\Helpers\ToolKit;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\ExternalRoutesController;
use App\Http\Controllers\API\ServicesRouter\ServicesRouterController;

ToolKit::mapRoutes(
    security: SecurityType::SESSION,
    routes: [
        'CustomerAddressesd',
        'CustomerAddressesp',
        'CustomerAddressespu',
        'CustomerCCard',
        'CustomerContacts',
        'CustomerContactsg',
        'CustomerContactsp',
        'CustomerData',
        'CustomerDatag',
        'CustomerDatap',
        'CustomerHomeAddresses',
        'CustomerIncomes',
        'CustomerNotifications',
        'CustomerNotificationsPut',
        'CustomerReferences',
        'CustomerReferencesd',
        'CustomerReferencesg',
        'CustomerReferencesp',
        'IdentificationImageAdd',
    ],
);

Route::middleware('session')->group(function () {
    Route::put('CustomerContactsp', [ServicesRouterController::class, 'putServices'])
        ->middleware('update.customer.contact.interceptor');

    Route::post('CustomerAddresses', [ExternalRoutesController::class, 'createCustomerAddresses']);
    Route::put('CustomerAddresses', [ExternalRoutesController::class, 'updateCustomerAddresses']);
    Route::delete('CustomerAddresses', [ExternalRoutesController::class, 'deleteCustomerAddresses']);
    Route::get('CustomerReferences', [ExternalRoutesController::class, 'getCustomerReferences']);
    Route::post('CustomerReferences', [ExternalRoutesController::class, 'createCustomerReferences']);
    Route::put('CustomerReferences', [ExternalRoutesController::class, 'updateCustomerReferences']);
    Route::delete('CustomerReferences', [ExternalRoutesController::class, 'deleteCustomerReferences']);
});

ToolKit::mapRoutes(
    security: SecurityType::GLOBAL,
    routes: [
        'DeliveryAddresses',
        'ValidateCustomerContact'
    ],
);

ToolKit::mapRoutes(
    security: SecurityType::APIKEY_WITH_BIOMETRY,
    routes: [
        'CustomerAFP',
        'identificationimage',
        'ValidateForbiddenWords',
    ],
);
