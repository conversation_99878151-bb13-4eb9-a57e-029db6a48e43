<?php

use App\Enums\SecurityType;
use App\Helpers\ToolKit;

ToolKit::mapRoutes(
    security: SecurityType::APIKEY_WITHOUT_BIOMETRY,
    routes: [
        'biometry',
        'biometry-verification',
        'faceauthentication-open',
        'forgot-password',
        'validate-forgot-password'
    ],
);

ToolKit::mapRoutes(
    security: SecurityType::APIKEY_WITH_BIOMETRY,
    routes: [
        'DocAuthentication',
        'sendotpcode2',
        'verifyOtpCode'
    ],
);

ToolKit::mapRoutes(
    security: SecurityType::GLOBAL,
    routes: [
        'faceauthentication',
        'faceauthenticationext',
        'ValidateOCR'
    ],
);

ToolKit::mapRoutes(
    security: SecurityType::NONE,
    routes: 'Authorization',
);
