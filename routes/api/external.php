<?php

use App\Enums\SecurityType;
use App\Helpers\ToolKit;
use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Route as RouteObject;

$externalRoutePaths = [
    __DIR__ . '/external/auth.php',
    __DIR__ . '/external/card.php',
    __DIR__ . '/external/customer.php',
    __DIR__ . '/external/management.php',
];

requireRouteFiles($externalRoutePaths);

$definedRoutes = Route::getRoutes()->getRoutes();

// Get the URI from the the defined URLS to be able to exclude them from the services router.
$definedUris = array_map(
    callback: fn(RouteObject $route) => str_replace('api/',  '', $route->uri()),
    array: $definedRoutes
);

$definedUris = array_merge($definedUris, ToolKit::getAllMappedRoutes());

Route::middleware('session')->group(function () use ($definedUris) {
    ToolKit::only(ToolKit::getMappedRoutes(SecurityType::SESSION));
    ToolKit::servicesRoutes(SecurityType::SESSION, $definedUris);
});

Route::middleware('apikey.without.biometry')->group(function () {
    ToolKit::only(ToolKit::getMappedRoutes(SecurityType::APIKEY_WITHOUT_BIOMETRY));
});

Route::middleware('apikey.with.biometry')->group(function () use ($definedUris) {
    ToolKit::only(ToolKit::getMappedRoutes(SecurityType::APIKEY_WITH_BIOMETRY));
    ToolKit::servicesRoutes(SecurityType::APIKEY_WITH_BIOMETRY, $definedUris);
});

Route::middleware('global')->group(function ()  use ($definedUris) {
    ToolKit::only(ToolKit::getMappedRoutes(SecurityType::GLOBAL));
    ToolKit::servicesRoutes(SecurityType::GLOBAL, $definedUris);
});

ToolKit::only(ToolKit::getMappedRoutes(SecurityType::NONE));
ToolKit::servicesRoutes(SecurityType::NONE, $definedUris);
