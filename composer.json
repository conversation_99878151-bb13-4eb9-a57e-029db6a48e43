{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "fakerphp/faker": "^1.24.1", "firebase/php-jwt": "^6.11.1", "geoip2/geoip2": "^3.1.0", "guzzlehttp/guzzle": "^7.9.3", "knuckleswtf/scribe": "^5.2", "laravel/framework": "^12.20.0", "laravel/prompts": "^0.3", "laravel/tinker": "^2.10.1", "laravel/ui": "^4.6.1", "league/commonmark": "^2.7", "league/html-to-markdown": "^5.1.1", "nesbot/carbon": "^3.10.1", "nette/schema": "^1.3", "spatie/crypto": "^2.1.0", "twilio/sdk": "^8.6.5"}, "repositories": [], "require-dev": {"friendsofphp/php-cs-fixer": "^3.75.0", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.8.2", "phpstan/phpstan": "^2.1.17", "phpunit/phpunit": "^12.2.7", "phpunit/php-file-iterator": "^6.0.0", "spatie/laravel-ignition": "^2.9.1", "squizlabs/php_codesniffer": "^3.12.2", "vimeo/psalm": "^6.5.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}