<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>Atlantida API Gateway Documentation</title>

    <link href="https://fonts.googleapis.com/css?family=Open+Sans&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="{{ asset("/vendor/scribe/css/theme-default.style.css") }}" media="screen">
    <link rel="stylesheet" href="{{ asset("/vendor/scribe/css/theme-default.print.css") }}" media="print">

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js"></script>

    <link rel="stylesheet"
          href="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/styles/obsidian.min.css">
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/highlight.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jets/0.14.1/jets.min.js"></script>

    <style id="language-style">
        /* starts out as display none and is replaced with js later  */
                    body .content .bash-example code { display: none; }
                    body .content .javascript-example code { display: none; }
            </style>

    <script>
        var tryItOutBaseUrl = "http://localhost:3000";
        var useCsrf = Boolean();
        var csrfUrl = "/sanctum/csrf-cookie";
    </script>
    <script src="{{ asset("/vendor/scribe/js/tryitout-5.2.1.js") }}"></script>

    <script src="{{ asset("/vendor/scribe/js/theme-default-5.2.1.js") }}"></script>

</head>

<body data-languages="[&quot;bash&quot;,&quot;javascript&quot;]">

<a href="#" id="nav-button">
    <span>
        MENU
        <img src="{{ asset("/vendor/scribe/images/navbar.png") }}" alt="navbar-image"/>
    </span>
</a>
<div class="tocify-wrapper">
    
            <div class="lang-selector">
                                            <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                            <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                    </div>
    
    <div class="search">
        <input type="text" class="search" id="input-search" placeholder="Search">
    </div>

    <div id="toc">
                    <ul id="tocify-header-introduction" class="tocify-header">
                <li class="tocify-item level-1" data-unique="introduction">
                    <a href="#introduction">Introduction</a>
                </li>
                            </ul>
                    <ul id="tocify-header-authenticating-requests" class="tocify-header">
                <li class="tocify-item level-1" data-unique="authenticating-requests">
                    <a href="#authenticating-requests">Authenticating requests</a>
                </li>
                                    <ul id="tocify-subheader-authenticating-requests" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="base-authentication">
                                <a href="#base-authentication">Base Authentication</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="security-groups">
                                <a href="#security-groups">Security Groups</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-client" class="tocify-header">
                <li class="tocify-item level-1" data-unique="client">
                    <a href="#client">Client</a>
                </li>
                                    <ul id="tocify-subheader-client" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="client-GETapi-deviceslist">
                                <a href="#client-GETapi-deviceslist">GET /api/deviceslist</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="client-DELETEapi-devices--device-id-">
                                <a href="#client-DELETEapi-devices--device-id-">DELETE /api/devices/{id}</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="client-GETapi-profile">
                                <a href="#client-GETapi-profile">GET /api/profile</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-configuration" class="tocify-header">
                <li class="tocify-item level-1" data-unique="configuration">
                    <a href="#configuration">Configuration</a>
                </li>
                                    <ul id="tocify-subheader-configuration" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="configuration-GETapi-maintenance-periods">
                                <a href="#configuration-GETapi-maintenance-periods">GET /api/maintenance-periods</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="configuration-GETapi-maintenance-mode">
                                <a href="#configuration-GETapi-maintenance-mode">GET /api/maintenance-mode</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="configuration-GETapi-configuration">
                                <a href="#configuration-GETapi-configuration">GET /api/configuration</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-management" class="tocify-header">
                <li class="tocify-item level-1" data-unique="management">
                    <a href="#management">Management</a>
                </li>
                                    <ul id="tocify-subheader-management" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="management-GETapi-benefits">
                                <a href="#management-GETapi-benefits">GET /api/benefits</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="management-GETapi-parameterizables">
                                <a href="#management-GETapi-parameterizables">GET /api/parameterizables</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="management-GETapi-surveys">
                                <a href="#management-GETapi-surveys">GET /api/surveys</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="management-GETapi-surveys--id-">
                                <a href="#management-GETapi-surveys--id-">GET /api/surveys/{id}</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="management-POSTapi-surveys--id--answers">
                                <a href="#management-POSTapi-surveys--id--answers">POST /api/surveys/{id}/answers</a>
                            </li>
                                                                        </ul>
                            </ul>
            </div>

    <ul class="toc-footer" id="toc-footer">
                            <li style="padding-bottom: 5px;"><a href="{{ route("scribe.openapi") }}">View OpenAPI spec</a></li>
                <li><a href="http://github.com/knuckleswtf/scribe">Documentation powered by Scribe ✍</a></li>
    </ul>

    <ul class="toc-footer" id="last-updated">
        <li>Last updated: August 21, 2025</li>
    </ul>
</div>

<div class="page-wrapper">
    <div class="dark-box"></div>
    <div class="content">
        <h1 id="introduction">Introduction</h1>
<aside>
    <strong>Base URL</strong>: <code>http://localhost:3000</code>
</aside>
<pre><code>This documentation aims to provide all the information you need to work with our API.

&lt;aside&gt;As you scroll, you'll see code examples for working with the API in different programming languages in the dark area to the right (or as part of the content on mobile).
You can switch the language used with the tabs at the top right (or from the nav menu at the top left on mobile).&lt;/aside&gt;</code></pre>

        <h1 id="authenticating-requests">Authenticating requests</h1>
<blockquote>
<p><strong>NOTE</strong>: This section provides a high-level overview of authentication for the API Gateway. Each endpoint in the documentation will specify its required headers and parameters to ensure successful requests.</p>
</blockquote>
<p>This API requires authentication for most endpoints. There are different authentication requirements depending on the endpoint type and security group.</p>
<h2 id="base-authentication">Base Authentication</h2>
<p>All API requests must include an encrypted API key in the request headers:</p>
<pre><code>APIKey: your-encrypted-api-key</code></pre>
<h2 id="security-groups">Security Groups</h2>
<h3 id="in-app-endpoints">In-App Endpoints</h3>
<p>For endpoints used within the mobile application after user login, you must include:</p>
<p><strong>Required Headers:</strong></p>
<ul>
<li><code>APIKey</code>: Encrypted API key (always required)</li>
<li><code>Authorization</code>: Bearer token obtained from login</li>
<li><code>UserAuth</code>: Customer ID of the authenticated user</li>
<li><code>DeviceAuth</code>: Unique identifier of the device making the request</li>
</ul>
<h3 id="onboarding-endpoints">Onboarding Endpoints</h3>
<p>For endpoints used during the customer onboarding process (before full registration), you must include:</p>
<p><strong>Required Headers:</strong></p>
<ul>
<li><code>APIKey</code>: Encrypted API key (always required)</li>
<li><code>DuiAuth</code>: DUI (National ID) of the customer</li>
<li><code>BiometryAuth</code>: Biometry token obtained from the biometry consultation endpoint</li>
<li><code>DeviceAuth</code>: Unique identifier of the device making the request</li>
</ul>

        <h1 id="client">Client</h1>

    

                                <h2 id="client-GETapi-deviceslist">GET /api/deviceslist</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Fetches a list of devices associated with the customer account, including device identifiers and relevant metadata.</p>

<span id="example-requests-GETapi-deviceslist">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/deviceslist" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/deviceslist"
);

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-deviceslist">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;device_id&quot;: &quot;50feb8b9-7c41-383c-80e2-f12af5328da7&quot;,
        &quot;device_name&quot;: &quot;test-device&quot;,
        &quot;ultimate_longitud&quot;: 78,
        &quot;ultimate_latitud&quot;: 90,
        &quot;ultimate_conexion&quot;: &quot;2025-08-04T12:55:51.180Z&quot;,
        &quot;online&quot;: 1
    },
    &quot;additional&quot;: {
        &quot;status&quot;: 200
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-deviceslist" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-deviceslist"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-deviceslist"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-deviceslist" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-deviceslist">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-deviceslist" data-method="GET"
      data-path="api/deviceslist"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-deviceslist', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-deviceslist"
                    onclick="tryItOut('GETapi-deviceslist');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-deviceslist"
                    onclick="cancelTryOut('GETapi-deviceslist');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-deviceslist"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/deviceslist</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-deviceslist"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-deviceslist"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-deviceslist"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-deviceslist"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                        </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>data</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>
<p>Device information.</p>
            </summary>
                                                <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>device_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier for the device.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>device_name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Name of the device.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>ultimate_longitud</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Last known longitude of the device.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>ultimate_latitud</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Last known latitude of the device.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>ultimate_conexion</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Timestamp of the last device connection.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>online</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Device online status (1 for online, 0 for offline).</p>
                    </div>
                                    </details>
        </div>
                                        </details>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>additional</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>
<p>Additional response metadata.</p>
            </summary>
                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>status</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>HTTP status code of the response.</p>
                    </div>
                                    </details>
        </div>
                        <h2 id="client-DELETEapi-devices--device-id-">DELETE /api/devices/{id}</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Deletes the device specified by the device-id URL parameter if it exists.</p>

<span id="example-requests-DELETEapi-devices--device-id-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request DELETE \
    "http://localhost:3000/api/devices/50feb8b9-7c41-383c-80e2-f12af5328da7" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/devices/50feb8b9-7c41-383c-80e2-f12af5328da7"
);

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-DELETEapi-devices--device-id-">
            <blockquote>
            <p>Example response (204):</p>
        </blockquote>
                <pre>
<code>Empty response</code>
 </pre>
    </span>
<span id="execution-results-DELETEapi-devices--device-id-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-DELETEapi-devices--device-id-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-DELETEapi-devices--device-id-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-DELETEapi-devices--device-id-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-DELETEapi-devices--device-id-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-DELETEapi-devices--device-id-" data-method="DELETE"
      data-path="api/devices/{device-id}"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('DELETEapi-devices--device-id-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-DELETEapi-devices--device-id-"
                    onclick="tryItOut('DELETEapi-devices--device-id-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-DELETEapi-devices--device-id-"
                    onclick="cancelTryOut('DELETEapi-devices--device-id-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-DELETEapi-devices--device-id-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-red">DELETE</small>
            <b><code>api/devices/{device-id}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="DELETEapi-devices--device-id-"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="DELETEapi-devices--device-id-"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="DELETEapi-devices--device-id-"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="DELETEapi-devices--device-id-"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>device-id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="device-id"                data-endpoint="DELETEapi-devices--device-id-"
               value="50feb8b9-7c41-383c-80e2-f12af5328da7"
               data-component="url">
    <br>
<p>Unique identifier for the device. Example: <code>50feb8b9-7c41-383c-80e2-f12af5328da7</code></p>
            </div>
                    </form>

                    <h2 id="client-GETapi-profile">GET /api/profile</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Retrieves the profile and background images associated with the authenticated client.</p>

<span id="example-requests-GETapi-profile">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/profile" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/profile"
);

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-profile">
            <blockquote>
            <p>Example response (200, Successfully retrieves the profile information for the authenticated client.):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;customer_id&quot;: &quot;7332398988435033162&quot;,
    &quot;creditcard_application_id&quot;: 1582,
    &quot;nickname&quot;: &quot;Vo0xALiRxvhqveUv&quot;,
    &quot;first_name&quot;: &quot;sed&quot;,
    &quot;second_name&quot;: null,
    &quot;first_surname&quot;: &quot;aut&quot;,
    &quot;second_surname&quot;: null,
    &quot;married_surname&quot;: null,
    &quot;dui&quot;: &quot;00253852-0&quot;,
    &quot;nit&quot;: &quot;00253852-0&quot;,
    &quot;email&quot;: &quot;<EMAIL>&quot;,
    &quot;phone_number&quot;: &quot;567314892&quot;,
    &quot;status&quot;: 200
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-profile" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-profile"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-profile"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-profile" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-profile">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-profile" data-method="GET"
      data-path="api/profile"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-profile', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-profile"
                    onclick="tryItOut('GETapi-profile');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-profile"
                    onclick="cancelTryOut('GETapi-profile');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-profile"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/profile</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-profile"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-profile"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-profile"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-profile"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                        </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>customer_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier for the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>creditcard_application_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>ID of the customer's credit card application.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>nickname</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Nickname of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>first_name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>First name of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>second_name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Second name of the customer, if any.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>first_surname</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>First surname of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>second_surname</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Second surname of the customer, if any.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>married_surname</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Married surname of the customer, if any.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>dui</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>DUI of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>nit</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>NIT of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Email address of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>phone_number</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Phone number of the customer.</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>status</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>HTTP status code of the response.</p>
        </div>
                    <h1 id="configuration">Configuration</h1>

    

                                <h2 id="configuration-GETapi-maintenance-periods">GET /api/maintenance-periods</h2>

<p>
</p>

<p>Checks if system is under maintenance. Returns maintenance details if active, otherwise HTTP 204.</p>

<span id="example-requests-GETapi-maintenance-periods">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/maintenance-periods" \
    --header "APIKey: Encrypted string used for authenticating requests to the API."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/maintenance-periods"
);

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-maintenance-periods">
            <blockquote>
            <p>Example response (204, Indicates that the services are currently available and not under maintenance.):</p>
        </blockquote>
                <pre>
<code>Empty response</code>
 </pre>
            <blockquote>
            <p>Example response (200, Returned when an ongoing maintenance period is detected. The &#039;message&#039; field contains details about the maintenance.):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Actualmente nos encontramos en mantenimiento. Por favor, vuelve a intentar m&aacute;s tarde.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-maintenance-periods" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-maintenance-periods"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-maintenance-periods"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-maintenance-periods" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-maintenance-periods">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-maintenance-periods" data-method="GET"
      data-path="api/maintenance-periods"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-maintenance-periods', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-maintenance-periods"
                    onclick="tryItOut('GETapi-maintenance-periods');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-maintenance-periods"
                    onclick="cancelTryOut('GETapi-maintenance-periods');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-maintenance-periods"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/maintenance-periods</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-maintenance-periods"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                        </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>message</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Details about the maintenance period.</p>
        </div>
                        <h2 id="configuration-GETapi-maintenance-mode">GET /api/maintenance-mode</h2>

<p>
</p>

<p>Verifies if the application is in maintenance mode and returns appropriate status with encrypted API key.</p>

<span id="example-requests-GETapi-maintenance-mode">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/maintenance-mode" \
    --header "APIKey: Encrypted string used for authenticating requests to the API."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/maintenance-mode"
);

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-maintenance-mode">
            <blockquote>
            <p>Example response (200, Services are available and not under maintenance):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;result&quot;: &quot;Ok&quot;,
    &quot;apikey&quot;: &quot;encoded_api_key_string&quot;,
    &quot;status&quot;: 200,
    &quot;code&quot;: 0
}</code>
 </pre>
            <blockquote>
            <p>Example response (503, System is currently under maintenance):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;result&quot;: &quot;Actualmente nos encontramos en mantenimiento. Por favor, vuelve a intentar m&aacute;s tarde.&quot;,
    &quot;apikey&quot;: &quot;encoded_api_key_string&quot;,
    &quot;status&quot;: 503,
    &quot;code&quot;: 1
}</code>
 </pre>
            <blockquote>
            <p>Example response (500, Internal server error):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: 500,
    &quot;code&quot;: 5000,
    &quot;message&quot;: &quot;Server error message&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-maintenance-mode" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-maintenance-mode"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-maintenance-mode"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-maintenance-mode" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-maintenance-mode">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-maintenance-mode" data-method="GET"
      data-path="api/maintenance-mode"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-maintenance-mode', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-maintenance-mode"
                    onclick="tryItOut('GETapi-maintenance-mode');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-maintenance-mode"
                    onclick="cancelTryOut('GETapi-maintenance-mode');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-maintenance-mode"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/maintenance-mode</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-maintenance-mode"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                        </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>result</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Result message indicating system status or maintenance message</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>apikey</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Double-encoded API key for client authentication</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>status</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>HTTP status code</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>code</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Application-specific code (0 for success, 1 for maintenance, 5000 for errors)</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>message</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Error message (only present in error responses)</p>
        </div>
                        <h2 id="configuration-GETapi-configuration">GET /api/configuration</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Retrieve specific configuration value by its code if allowed</p>

<span id="example-requests-GETapi-configuration">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/configuration?code=" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID." \
    --header "DuiAuth: DUI of the customer." \
    --header "BiometryAuth: Biometry ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/configuration"
);

const params = {
    "code": "",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "DuiAuth": "DUI of the customer.",
    "BiometryAuth": "Biometry ID.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-configuration">
            <blockquote>
            <p>Example response (200, Successfully retrieved configuration (if exists and allowed)):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;result&quot;: &quot;configuration_value_or_object&quot;,
    &quot;status&quot;: 200
}</code>
 </pre>
            <blockquote>
            <p>Example response (200, Configuration not found or not allowed):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;result&quot;: &quot;No se encontr&oacute; ninguna&quot;,
    &quot;status&quot;: 200
}</code>
 </pre>
            <blockquote>
            <p>Example response (500, Internal server error):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: 500,
    &quot;code&quot;: 5000,
    &quot;message&quot;: &quot;Server error message&quot;
}</code>
 </pre>
            <blockquote>
            <p>Example response (400, Bad request - database constraint violation):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: 400,
    &quot;code&quot;: 5000,
    &quot;message&quot;: &quot;Database error message&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-configuration" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-configuration"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-configuration"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-configuration" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-configuration">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-configuration" data-method="GET"
      data-path="api/configuration"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-configuration', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-configuration"
                    onclick="tryItOut('GETapi-configuration');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-configuration"
                    onclick="cancelTryOut('GETapi-configuration');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-configuration"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/configuration</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-configuration"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-configuration"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-configuration"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-configuration"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DuiAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DuiAuth"                data-endpoint="GETapi-configuration"
               value="DUI of the customer."
               data-component="header">
    <br>
<p>Example: <code>DUI of the customer.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>BiometryAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="BiometryAuth"                data-endpoint="GETapi-configuration"
               value="Biometry ID."
               data-component="header">
    <br>
<p>Example: <code>Biometry ID.</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>code</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="code"                data-endpoint="GETapi-configuration"
               value=""
               data-component="query">
    <br>
<p>Configuration code to retrieve</p>
            </div>
                </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>result</code></b>&nbsp;&nbsp;
<small>mixed</small>&nbsp;
 &nbsp;
<br>
<p>Configuration value (can be string, object, array) or error message if not found/allowed</p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>status</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>HTTP status code</p>
        </div>
                    <h1 id="management">Management</h1>

    

                                <h2 id="management-GETapi-benefits">GET /api/benefits</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Retrieves the list of benefits a customer can choose when opening their account.</p>

<span id="example-requests-GETapi-benefits">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/benefits?format=html" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID." \
    --header "DuiAuth: DUI of the customer." \
    --header "BiometryAuth: Biometry ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/benefits"
);

const params = {
    "format": "html",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "DuiAuth": "DUI of the customer.",
    "BiometryAuth": "Biometry ID.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-benefits">
            <blockquote>
            <p>Example response (200, Successfully retrieved benefits):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;title&quot;: &quot;Viajes&quot;,
            &quot;description&quot;: &quot;&lt;div&gt;Por cada d&oacute;lar en compras te damos &lt;strong&gt;1.5 puntos&lt;/strong&gt; para canjear &lt;strong&gt;por millas Lifemiles&lt;/strong&gt;. Pod&eacute;s acumular hasta 15,000 puntos mensuales &iexcl;Tus puntos nunca vencen!&lt;/div&gt;&quot;,
            &quot;example&quot;: &quot;Ej: Si gastas $5,000 por mes recibes los 7,500 puntos&quot;,
            &quot;main&quot;: false,
            &quot;order&quot;: 3,
            &quot;longcode&quot;: &quot;VIAJES&quot;,
            &quot;icon&quot;: &quot;http://localhost:3000/api/imagery/1&quot;,
            &quot;storehouse_id&quot;: 1
        },
        {
            &quot;title&quot;: &quot;Compras&quot;,
            &quot;description&quot;: &quot;&lt;div&gt;Por cada d&oacute;lar en compras te damos &lt;strong&gt;3 puntos&lt;/strong&gt;. Pod&eacute;s acumular hasta 30,000 puntos mensuales &iexcl;Tus puntos nunca vencen!&lt;/div&gt;&quot;,
            &quot;example&quot;: &quot;Ej: Si gastas $5,000 en el mes acumulas 15,000 puntos&quot;,
            &quot;main&quot;: true,
            &quot;order&quot;: 1,
            &quot;longcode&quot;: &quot;COMPRAS&quot;,
            &quot;icon&quot;: &quot;http://localhost:3000/api/imagery/2&quot;,
            &quot;storehouse_id&quot;: 2
        },
        {
            &quot;title&quot;: &quot;Ahorro&quot;,
            &quot;description&quot;: &quot;&lt;div&gt;Por cada d&oacute;lar en compras te regresamos &lt;strong&gt;$0.015 (1.5% de cashback)&lt;/strong&gt; con un l&iacute;mite m&aacute;ximo de $150 mensuales en tu cuenta de ahorro&lt;/div&gt;&quot;,
            &quot;example&quot;: &quot;Ej: Si en un mes tus compras suman $5,000 te devolvemos $75&quot;,
            &quot;main&quot;: false,
            &quot;order&quot;: 2,
            &quot;longcode&quot;: &quot;CASHBACK&quot;,
            &quot;icon&quot;: &quot;http://localhost:3000/api/imagery/3&quot;,
            &quot;storehouse_id&quot;: 3
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-benefits" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-benefits"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-benefits"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-benefits" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-benefits">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-benefits" data-method="GET"
      data-path="api/benefits"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-benefits', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-benefits"
                    onclick="tryItOut('GETapi-benefits');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-benefits"
                    onclick="cancelTryOut('GETapi-benefits');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-benefits"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/benefits</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-benefits"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-benefits"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-benefits"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-benefits"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DuiAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DuiAuth"                data-endpoint="GETapi-benefits"
               value="DUI of the customer."
               data-component="header">
    <br>
<p>Example: <code>DUI of the customer.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>BiometryAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="BiometryAuth"                data-endpoint="GETapi-benefits"
               value="Biometry ID."
               data-component="header">
    <br>
<p>Example: <code>Biometry ID.</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>format</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
                              name="format"                data-endpoint="GETapi-benefits"
               value="html"
               data-component="query">
    <br>
<p>Specifies the format in which the benefits should be returned. Value must be either &quot;html&quot; or &quot;markdown&quot;, if not provided it will default to &quot;html&quot; Example: <code>html</code></p>
            </div>
                </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>data</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>Array of benefit objects returned to the customer.</p>
            </summary>
                                                <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>title</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Display name of the benefit shown to the customer.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>description</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Detailed explanation of the benefit, returned in either HTML or Markdown format.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>example</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Illustrative example demonstrating how the benefit can be used.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>main</code></b>&nbsp;&nbsp;
<small>boolean</small>&nbsp;
 &nbsp;
<br>
<p>Indicates if this benefit is the default or primary option for customers.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>order</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Specifies the sequence in which benefits are presented to the customer.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>longcode</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier code assigned to the benefit.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>icon</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Reference to the icon (name or path) used to visually represent the benefit.</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>storehouse_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Identifier linking the benefit to an associated image or resource.</p>
                    </div>
                                    </details>
        </div>
                                        </details>
        </div>
                        <h2 id="management-GETapi-parameterizables">GET /api/parameterizables</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Retrieve parameterizable configuration items filtered by group</p>

<span id="example-requests-GETapi-parameterizables">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/parameterizables?group=" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID." \
    --header "DuiAuth: DUI of the customer." \
    --header "BiometryAuth: Biometry ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/parameterizables"
);

const params = {
    "group": "",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "DuiAuth": "DUI of the customer.",
    "BiometryAuth": "Biometry ID.",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-parameterizables">
            <blockquote>
            <p>Example response (200, Successfully retrieved parameterizables):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: [
        {
            &quot;code&quot;: &quot;PARAM_001&quot;,
            &quot;parameter_type&quot;: &quot;string&quot;,
            &quot;parameter&quot;: &quot;example_value&quot;,
            &quot;group&quot;: &quot;general&quot;,
            &quot;storehouse_id&quot;: 1
        },
        {
            &quot;code&quot;: &quot;PARAM_002&quot;,
            &quot;parameter_type&quot;: &quot;boolean&quot;,
            &quot;parameter&quot;: &quot;true&quot;,
            &quot;group&quot;: &quot;general&quot;,
            &quot;storehouse_id&quot;: 1
        }
    ],
    &quot;additional&quot;: {
        &quot;status&quot;: 200
    }
}</code>
 </pre>
            <blockquote>
            <p>Example response (500, Internal server error):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: 500,
    &quot;code&quot;: 5000,
    &quot;message&quot;: &quot;Server error message&quot;
}</code>
 </pre>
            <blockquote>
            <p>Example response (400, Bad request - database constraint violation):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: 400,
    &quot;code&quot;: 5000,
    &quot;message&quot;: &quot;Database error message&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-parameterizables" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-parameterizables"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-parameterizables"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-parameterizables" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-parameterizables">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-parameterizables" data-method="GET"
      data-path="api/parameterizables"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-parameterizables', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-parameterizables"
                    onclick="tryItOut('GETapi-parameterizables');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-parameterizables"
                    onclick="cancelTryOut('GETapi-parameterizables');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-parameterizables"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/parameterizables</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-parameterizables"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-parameterizables"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-parameterizables"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-parameterizables"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DuiAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DuiAuth"                data-endpoint="GETapi-parameterizables"
               value="DUI of the customer."
               data-component="header">
    <br>
<p>Example: <code>DUI of the customer.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>BiometryAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="BiometryAuth"                data-endpoint="GETapi-parameterizables"
               value="Biometry ID."
               data-component="header">
    <br>
<p>Example: <code>Biometry ID.</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>group</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="group"                data-endpoint="GETapi-parameterizables"
               value=""
               data-component="query">
    <br>
<p>Group name to filter parameterizables</p>
            </div>
                </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>data</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>Array of parameterizable items</p>
            </summary>
                                                <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>code</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Unique parameter code</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>parameter_type</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Type of the parameter (string, boolean, integer, etc.)</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>parameter</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Parameter value</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>group</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Group classification of the parameter</p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>storehouse_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Associated storehouse identifier</p>
                    </div>
                                    </details>
        </div>
                                        </details>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>additional</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>
<p>Additional response metadata</p>
            </summary>
                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>status</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>HTTP status code</p>
                    </div>
                                    </details>
        </div>
                        <h2 id="management-GETapi-surveys">GET /api/surveys</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Retrieves a single survey by its code using the 'name' query parameter. Returns the survey's ID, name, description, and questions. Use this endpoint to look up a specific survey a customer can participate in.</p>

<span id="example-requests-GETapi-surveys">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/surveys?name=PRPF" \
    --header "Accept: application/json" \
    --header "Content-Type: application/json" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID." \
    --header "DuiAuth: DUI of the customer." \
    --header "BiometryAuth: Biometry ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/surveys"
);

const params = {
    "name": "PRPF",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "DuiAuth": "DUI of the customer.",
    "BiometryAuth": "Biometry ID.",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-surveys">
            <blockquote>
            <p>Example response (200, Successfully retrieved survey details and questions):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;id&quot;: 5,
        &quot;name&quot;: &quot;PRPF&quot;,
        &quot;description&quot;: &quot;&iquest;Con qu&eacute; probabilidad recomendar&iacute;as este proceso de solicitud a tus amigos o familiares?&quot;,
        &quot;questions&quot;: [
            {
                &quot;id&quot;: 1,
                &quot;survey_id&quot;: 5,
                &quot;type&quot;: &quot;OPEN&quot;,
                &quot;question&quot;: &quot;Test&quot;,
                &quot;options&quot;: [
                    {
                        &quot;id&quot;: 1,
                        &quot;question_id&quot;: 1,
                        &quot;value&quot;: &quot;Test&quot;,
                        &quot;type&quot;: &quot;OPEN&quot;
                    }
                ]
            }
        ]
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-surveys" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-surveys"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-surveys"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-surveys" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-surveys">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-surveys" data-method="GET"
      data-path="api/surveys"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-surveys', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-surveys"
                    onclick="tryItOut('GETapi-surveys');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-surveys"
                    onclick="cancelTryOut('GETapi-surveys');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-surveys"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/surveys</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-surveys"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-surveys"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-surveys"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-surveys"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-surveys"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-surveys"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DuiAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DuiAuth"                data-endpoint="GETapi-surveys"
               value="DUI of the customer."
               data-component="header">
    <br>
<p>Example: <code>DUI of the customer.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>BiometryAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="BiometryAuth"                data-endpoint="GETapi-surveys"
               value="Biometry ID."
               data-component="header">
    <br>
<p>Example: <code>Biometry ID.</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="name"                data-endpoint="GETapi-surveys"
               value="PRPF"
               data-component="query">
    <br>
<p>Survey code (e.g., PRPF) used to look up the survey by name. Example: <code>PRPF</code></p>
            </div>
                </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>data</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>
<p>Survey details including questions and options.</p>
            </summary>
                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier of the survey.</p>
                    </div>
                                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Name of the survey.</p>
                    </div>
                                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>description</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Survey description or prompt.</p>
                    </div>
                                                                <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>questions</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>List of questions in the survey.</p>
            </summary>
                                                <div style=" margin-left: 28px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier of the question.</p>
                    </div>
                                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>survey_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Identifier of the survey this question belongs to.</p>
                    </div>
                                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>question</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Text of the question.</p>
                    </div>
                                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>type</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Type of question (e.g., CTL For Cualitative, CNT For Cuantitative).</p>
                    </div>
                                                                <div style=" margin-left: 42px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>options</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>Available options for the question.</p>
            </summary>
                                                <div style=" margin-left: 56px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier of the option.</p>
                    </div>
                                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>question_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Identifier of the question this option belongs to.</p>
                    </div>
                                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>value</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Option value or text.</p>
                    </div>
                                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>type</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Type of option (e.g., OPTION_OPEN, OPTION_CLOSED).</p>
                    </div>
                                    </details>
        </div>
                                        </details>
        </div>
                                        </details>
        </div>
                                        </details>
        </div>
                                        </details>
        </div>
                        <h2 id="management-GETapi-surveys--id-">GET /api/surveys/{id}</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Fetches detailed information about a specific survey, including its questions and available options for each question. Useful for displaying survey structure and choices to customers.</p>

<span id="example-requests-GETapi-surveys--id-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://localhost:3000/api/surveys/" \
    --header "Accept: application/json" \
    --header "Content-Type: application/json" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID." \
    --header "DuiAuth: DUI of the customer." \
    --header "BiometryAuth: Biometry ID."</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/surveys/"
);

const headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "DuiAuth": "DUI of the customer.",
    "BiometryAuth": "Biometry ID.",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-surveys--id-">
            <blockquote>
            <p>Example response (200, Successfully retrieved survey details and questions):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;data&quot;: {
        &quot;id&quot;: 5,
        &quot;name&quot;: &quot;PRPF&quot;,
        &quot;description&quot;: &quot;&iquest;Con qu&eacute; probabilidad recomendar&iacute;as este proceso de solicitud a tus amigos o familiares?&quot;,
        &quot;questions&quot;: [
            {
                &quot;id&quot;: 1,
                &quot;survey_id&quot;: 5,
                &quot;type&quot;: &quot;OPEN&quot;,
                &quot;question&quot;: &quot;Test&quot;,
                &quot;options&quot;: [
                    {
                        &quot;id&quot;: 1,
                        &quot;question_id&quot;: 1,
                        &quot;value&quot;: &quot;Test&quot;,
                        &quot;type&quot;: &quot;OPEN&quot;
                    }
                ]
            }
        ]
    }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-surveys--id-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-surveys--id-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-surveys--id-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-surveys--id-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-surveys--id-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-surveys--id-" data-method="GET"
      data-path="api/surveys/{id}"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-surveys--id-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-surveys--id-"
                    onclick="tryItOut('GETapi-surveys--id-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-surveys--id-"
                    onclick="cancelTryOut('GETapi-surveys--id-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-surveys--id-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/surveys/{id}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-surveys--id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-surveys--id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="GETapi-surveys--id-"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-surveys--id-"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="GETapi-surveys--id-"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="GETapi-surveys--id-"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DuiAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DuiAuth"                data-endpoint="GETapi-surveys--id-"
               value="DUI of the customer."
               data-component="header">
    <br>
<p>Example: <code>DUI of the customer.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>BiometryAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="BiometryAuth"                data-endpoint="GETapi-surveys--id-"
               value="Biometry ID."
               data-component="header">
    <br>
<p>Example: <code>Biometry ID.</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="id"                data-endpoint="GETapi-surveys--id-"
               value=""
               data-component="url">
    <br>
<p>Unique identifier of the survey to retrieve details for.</p>
            </div>
                    </form>

    <h3>Response</h3>
    <h4 class="fancy-heading-panel"><b>Response Fields</b></h4>
    <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>data</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>
<p>Survey details including questions and options.</p>
            </summary>
                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier of the survey.</p>
                    </div>
                                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>name</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Name of the survey.</p>
                    </div>
                                                                <div style="margin-left: 14px; clear: unset;">
                        <b style="line-height: 2;"><code>description</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Survey description or prompt.</p>
                    </div>
                                                                <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>questions</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>List of questions in the survey.</p>
            </summary>
                                                <div style=" margin-left: 28px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier of the question.</p>
                    </div>
                                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>survey_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Identifier of the survey this question belongs to.</p>
                    </div>
                                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>question</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Text of the question.</p>
                    </div>
                                                                <div style="margin-left: 42px; clear: unset;">
                        <b style="line-height: 2;"><code>type</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Type of question (e.g., CTL For Cualitative, CNT For Cuantitative).</p>
                    </div>
                                                                <div style=" margin-left: 42px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>options</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>Available options for the question.</p>
            </summary>
                                                <div style=" margin-left: 56px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
 &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Unique identifier of the option.</p>
                    </div>
                                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>question_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
<br>
<p>Identifier of the question this option belongs to.</p>
                    </div>
                                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>value</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Option value or text.</p>
                    </div>
                                                                <div style="margin-left: 70px; clear: unset;">
                        <b style="line-height: 2;"><code>type</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
<br>
<p>Type of option (e.g., OPTION_OPEN, OPTION_CLOSED).</p>
                    </div>
                                    </details>
        </div>
                                        </details>
        </div>
                                        </details>
        </div>
                                        </details>
        </div>
                                        </details>
        </div>
                        <h2 id="management-POSTapi-surveys--id--answers">POST /api/surveys/{id}/answers</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Submit and save customer responses for a specific survey identified by its ID.</p>

<span id="example-requests-POSTapi-surveys--id--answers">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "http://localhost:3000/api/surveys//answers" \
    --header "Accept: application/json" \
    --header "Content-Type: application/json" \
    --header "APIKey: Encrypted string used for authenticating requests to the API." \
    --header "Authorization: Bearer token" \
    --header "UserAuth: Customer ID." \
    --header "DeviceAuth: Device ID." \
    --header "DuiAuth: DUI of the customer." \
    --header "BiometryAuth: Biometry ID." \
    --data "{
    \"answers\": [
        {
            \"optionId\": 1,
            \"other\": null
        },
        {
            \"optionId\": 2,
            \"other\": \"Some text\"
        }
    ]
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://localhost:3000/api/surveys//answers"
);

const headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "APIKey": "Encrypted string used for authenticating requests to the API.",
    "Authorization": "Bearer token",
    "UserAuth": "Customer ID.",
    "DeviceAuth": "Device ID.",
    "DuiAuth": "DUI of the customer.",
    "BiometryAuth": "Biometry ID.",
};

let body = {
    "answers": [
        {
            "optionId": 1,
            "other": null
        },
        {
            "optionId": 2,
            "other": "Some text"
        }
    ]
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-POSTapi-surveys--id--answers">
            <blockquote>
            <p>Example response (201, Survey answers successfully submitted):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">[]</code>
 </pre>
            <blockquote>
            <p>Example response (422, Validation error - missing or invalid answers):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;El campo answers es obligatorio.&quot;,
    &quot;errors&quot;: {
        &quot;answers&quot;: [
            &quot;El campo answers es obligatorio.&quot;
        ]
    }
}</code>
 </pre>
    </span>
<span id="execution-results-POSTapi-surveys--id--answers" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-surveys--id--answers"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-surveys--id--answers"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-surveys--id--answers" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-surveys--id--answers">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-POSTapi-surveys--id--answers" data-method="POST"
      data-path="api/surveys/{id}/answers"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-surveys--id--answers', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-POSTapi-surveys--id--answers"
                    onclick="tryItOut('POSTapi-surveys--id--answers');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-POSTapi-surveys--id--answers"
                    onclick="cancelTryOut('POSTapi-surveys--id--answers');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-POSTapi-surveys--id--answers"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/surveys/{id}/answers</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="POSTapi-surveys--id--answers"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="POSTapi-surveys--id--answers"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>APIKey</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="APIKey"                data-endpoint="POSTapi-surveys--id--answers"
               value="Encrypted string used for authenticating requests to the API."
               data-component="header">
    <br>
<p>Example: <code>Encrypted string used for authenticating requests to the API.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="POSTapi-surveys--id--answers"
               value="Bearer token"
               data-component="header">
    <br>
<p>Example: <code>Bearer token</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>UserAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="UserAuth"                data-endpoint="POSTapi-surveys--id--answers"
               value="Customer ID."
               data-component="header">
    <br>
<p>Example: <code>Customer ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DeviceAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DeviceAuth"                data-endpoint="POSTapi-surveys--id--answers"
               value="Device ID."
               data-component="header">
    <br>
<p>Example: <code>Device ID.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>DuiAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="DuiAuth"                data-endpoint="POSTapi-surveys--id--answers"
               value="DUI of the customer."
               data-component="header">
    <br>
<p>Example: <code>DUI of the customer.</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>BiometryAuth</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="BiometryAuth"                data-endpoint="POSTapi-surveys--id--answers"
               value="Biometry ID."
               data-component="header">
    <br>
<p>Example: <code>Biometry ID.</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="id"                data-endpoint="POSTapi-surveys--id--answers"
               value=""
               data-component="url">
    <br>
<p>Unique identifier of the survey for which answers are being submitted.</p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>answers</code></b>&nbsp;&nbsp;
<small>array</small>&nbsp;
 &nbsp;
<br>
<p>List of answers submitted for the survey.</p>
            </summary>
                                                <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>0</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
<i>optional</i> &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>optionId</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="optionId"                data-endpoint="POSTapi-surveys--id--answers"
               value="1"
               data-component="body">
    <br>
<p>ID referencing the selected option for a specific question. Example: <code>1</code></p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>other</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
                              name="other"                data-endpoint="POSTapi-surveys--id--answers"
               value="Some text"
               data-component="body">
    <br>
<p>Open answer provided by the user, if applicable. Example: <code>Some text</code></p>
                    </div>
                                    </details>
        </div>
                                                                    <div style=" margin-left: 14px; clear: unset;">
        <details>
            <summary style="padding-bottom: 10px;">
                <b style="line-height: 2;"><code>1</code></b>&nbsp;&nbsp;
<small>object</small>&nbsp;
<i>optional</i> &nbsp;
<br>

            </summary>
                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>optionId</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="optionId"                data-endpoint="POSTapi-surveys--id--answers"
               value="1"
               data-component="body">
    <br>
<p>ID referencing the selected option for a specific question. Example: <code>1</code></p>
                    </div>
                                                                <div style="margin-left: 28px; clear: unset;">
                        <b style="line-height: 2;"><code>other</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="text" style="display: none"
                              name="other"                data-endpoint="POSTapi-surveys--id--answers"
               value=""
               data-component="body">
    <br>
<p>Open answer provided by the user, if applicable.</p>
                    </div>
                                    </details>
        </div>
                                        </details>
        </div>
        </form>

            

        
    </div>
    <div class="dark-box">
                    <div class="lang-selector">
                                                        <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                                        <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                            </div>
            </div>
</div>
</body>
</html>
