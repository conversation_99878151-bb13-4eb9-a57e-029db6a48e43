FROM php:8.3-apache-bookworm

RUN apt-get -y update && apt-get -y upgrade

RUN apt-get -y --allow-change-held-packages update && apt-get -y --allow-change-held-packages dist-upgrade

RUN a2enmod rewrite

RUN apt install -y libicu-dev \
    && docker-php-ext-install -j$(nproc) intl
RUN apt install -y libxml2-dev \
    && docker-php-ext-install xml
RUN apt-get install -y libcurl3-dev --fix-missing \
    && docker-php-ext-install curl

RUN apt-get install -y \
    apt-utils \
    unixodbc-dev \
    gnupg \
    libldap2-dev \
    libz-dev \
    libpq-dev \
    libjpeg-dev \
    libpng-dev \
    libssl-dev \
    libzip-dev \
    unzip \
    zip \
    supervisor

COPY supervisor/supervisor.conf /etc/supervisor/conf.d/supervisor.conf

RUN pecl install xdebug
RUN docker-php-ext-enable xdebug
RUN docker-php-ext-install pdo

# Install dependencies for php-gd
RUN apt-get update && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libwebp-dev \
    libxpm-dev

# Configure and install php-gd extension
RUN docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp --with-xpm
RUN docker-php-ext-install gd


# MS SQL Server drivers
RUN apt-get install -y debian-keyring debian-archive-keyring
RUN apt-key update
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg
RUN apt install -y libtool libtool-bin
RUN echo msodbcsql18 msodbcsql/ACCEPT_EULA boolean true | debconf-set-selections
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
RUN curl https://packages.microsoft.com/config/debian/12/prod.list > /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update
RUN ACCEPT_EULA=Y apt-get install -y --allow-change-held-packages msodbcsql18 mssql-tools18
COPY sqlserver/libltdl.la /usr/lib/x86_64-linux-gnu/libltdl.la

RUN pecl install sqlsrv-5.12.0
RUN pecl install pdo_sqlsrv-5.12.0
RUN docker-php-ext-enable sqlsrv pdo_sqlsrv

# User setting
ARG UID=MUST_SET
RUN if [ -z "$UID" ]; then echo 'Env var UID must be set. Try HOST_UID=$(id -u) docker-compose up --build'; exit 1; fi
RUN apt update && \
    apt install -y sudo && \
    addgroup nonroot && \
    adduser --uid $UID --ingroup nonroot --disabled-password --gecos "" nonroot && \
    echo 'nonroot ALL=(ALL) NOPASSWD: ALL' >> /etc/sudoers
RUN chown -R nonroot:nonroot /var/log/supervisor

COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh
CMD ["/usr/local/bin/entrypoint.sh"]

USER nonroot
