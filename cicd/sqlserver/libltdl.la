# libltdl.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.2 Debian-2.4.2-1.11+b1
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libltdl.so.7'

# Names of this library.
library_names='libltdl.so.7.3.0 libltdl.so.7 libltdl.so'

# The name of the static archive.
old_library='libltdl.a'

# Linker flags that can not go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=' -ldl'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libltdl.
current=10
age=3
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/usr/lib/x86_64-linux-gnu'
