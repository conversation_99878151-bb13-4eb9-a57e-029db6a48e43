param (
    [Parameter(Mandatory = $false)]
    [string] $name = 'LaravelQueueWorker',

    [Parameter(Mandatory = $true)]
    [string] $executable,

    [Parameter(Mandatory = $false)]
    [string] $arguments
)

try {
    Write-Output $action

    $action = New-ScheduledTaskAction -Execute $executable -Argument $arguments

    $trigger = New-ScheduledTaskTrigger -AtStartup 

    $settings = New-ScheduledTaskSettingsSet `
        -StartWhenAvailable `
        -AllowStartIfOnBatteries `
        -DontStopIfGoingOnBatteries `
        -DontStopOnIdleEnd `
        -MultipleInstances IgnoreNew `

    $principal = New-ScheduledTaskPrincipal `
        -UserId 'SYSTEM' `
        -LogonType ServiceAccount `
        -RunLevel Highest 

    Register-ScheduledTask `
        -TaskName $name `
        -Action $action `
        -Trigger $trigger `
        -Settings $settings -Principal $principal `

    Start-ScheduledTask -TaskName $name
}
catch {
    $errorMessage = $_.Exception.Message

    Write-Error "Failed to register or start the scheduled task: $errorMessage"

    exit 1
}
