@echo off
@REM Change directory to the location of this file
cd /d "%~dp0"

@REM Move two levels out to the root of the project
cd ..\..

@REM Set default log file path if not provided as an argument
set "logFilePath=%~1"
if "%logFilePath%"=="" (
    set "logFilePath=storage\logs\queue_logs.log"
)

@REM Run the Artisan Queue command and redirect output
php artisan queue:work --daemon >> "%logFilePath%" 2>&1
