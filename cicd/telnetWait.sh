#!/bin/sh

set -e
container_name="db"
port="1433"
max_attempts="120"
interval="5"

waitingDb() {
    echo "Waiting for container '$container_name' to open port $port..."

    i=0
    while [ "$i" -lt "$max_attempts" ]; do
        if telnet "$container_name" "$port" >/dev/null 2>&1; then
            i=$((max_attempts + 1))
            echo "Port $port on container '$container_name' is open."
            return 0
        elif [ "$i" -eq "$((max_attempts - 1))" ]; then
            echo "Port $port on container '$container_name' did not open within the specified time."
            return 1
        else
            echo "retry $i Port $port on container '$container_name' did not open within the specified time."
        fi

        i=$((i + 1))
        sleep "$interval"
    done
}

waitingDb
exec "$@"