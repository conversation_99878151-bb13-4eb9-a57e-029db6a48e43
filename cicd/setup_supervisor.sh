#!/bin/bash

# Get the directory of the script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LARAVEL_DIR="$(dirname "$SCRIPT_DIR")"

# Detect the Linux distribution
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$ID
else
    echo "Cannot detect the operating system."
    exit 1
fi

# Install supervisor based on the detected distribution
case "$OS" in
    ubuntu|debian)
        if command -v supervisorctl &> /dev/null; then
            echo "Supervisor is already installed."
        else
            echo "Supervisor is not installed. Installing supervisor..."
            sudo apt-get update
            sudo apt-get install -y supervisor
        fi
        SUPERVISOR_CONF="/etc/supervisor/conf.d/one-app-api.conf"
        ;;
    centos|rhel|fedora|almalinux)
        if command -v supervisorctl &> /dev/null; then
            echo "Supervisor is already installed."
        else
            echo "Supervisor is not installed. Installing supervisor..."
            sudo yum update -y
            sudo yum install -y epel-release
            sudo yum install -y supervisor
        fi
        SUPERVISOR_CONF="/etc/supervisord.d/one-app-api.ini"
        ;;
    *)
        echo "Unsupported operating system: $OS"
        exit 1
        ;;
esac

# Create supervisor configuration for Laravel queues
sudo bash -c "cat > $SUPERVISOR_CONF" <<EOL
[program:laravel-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php $LARAVEL_DIR/artisan queue:work --tries=3
autostart=true
autorestart=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=$LARAVEL_DIR/storage/logs/queue_logs.log
EOL

# Enable and start supervisor
if [[ "$OS" == "centos" || "$OS" == "rhel" || "$OS" == "fedora" || "$OS" == "almalinux" ]]; then
    sudo systemctl enable supervisord
    sudo systemctl start supervisord
fi

# Reload supervisor to apply the new configuration
sudo supervisorctl reread
sudo supervisorctl update

echo "Supervisor has been installed and configured to run Laravel queues."
