FROM composer:2.4 AS composer
FROM php:8.3-apache-bookworm
COPY --from=composer /usr/bin/composer /usr/bin/composer
RUN composer --version && php -v
RUN apt-get -y --allow-change-held-packages update && apt-get -y --allow-change-held-packages dist-upgrade
RUN apt install -y telnet
RUN a2enmod rewrite

RUN apt install -y libicu-dev \
    && docker-php-ext-install -j$(nproc) intl
RUN apt install -y libxml2-dev \
    && docker-php-ext-install xml
RUN apt-get install -y libcurl3-dev --fix-missing \
    && docker-php-ext-install curl
RUN pecl install xdebug
# Adding support to debug php
RUN apt-get install -y \
    apt-utils \
    unixodbc-dev \
    gnupg \
    libldap2-dev \
    libz-dev \
    libpq-dev \
    libjpeg-dev \
    libpng-dev \
    libssl-dev \
    libzip-dev \
    unzip \
    zip

# MS SQL Server drivers
RUN docker-php-ext-install pdo
RUN apt-get install -y debian-keyring debian-archive-keyring
RUN apt-key update
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg
RUN apt install -y libtool libtool-bin
RUN echo msodbcsql18 msodbcsql/ACCEPT_EULA boolean true | debconf-set-selections
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
RUN curl https://packages.microsoft.com/config/debian/12/prod.list > /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update
RUN ACCEPT_EULA=Y apt-get install -y --allow-change-held-packages msodbcsql18 mssql-tools18
RUN ln -sfn /opt/mssql-tools18/bin/sqlcmd /usr/bin/sqlcmd
COPY cicd/sqlserver/libltdl.la /usr/lib/x86_64-linux-gnu/libltdl.la

# Install dependencies for php-gd
RUN apt-get update && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libwebp-dev \
    libxpm-dev

# Configure and install php-gd extension
RUN docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp --with-xpm
RUN docker-php-ext-install gd

RUN pecl install sqlsrv-5.12.0
RUN pecl install pdo_sqlsrv-5.12.0
RUN docker-php-ext-enable sqlsrv pdo_sqlsrv xdebug
COPY cicd/telnetWait.sh /usr/local/bin/custom-entrypoint.sh
RUN chmod o+x /usr/local/bin/custom-entrypoint.sh
# ENTRYPOINT ["custom-entrypoint.sh"]
# EXPOSE 80
# CMD ["apache2-foreground"]