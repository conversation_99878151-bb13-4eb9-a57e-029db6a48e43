[supervisord]
logfile=/var/www/html/storage/logs/supervisor.log
nodaemon=true

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0760

[environment:supervisorctl]
SUPERVISOR_SERVER_URL=unix:///tmp/supervisor.sock

[program:laravel-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work --sleep=3 --tries=3 --timeout=90
autostart=true
autorestart=true
numprocs=1 
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue_logs.log
stopwaitsecs=3600
