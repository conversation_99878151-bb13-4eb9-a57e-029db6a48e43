version: '3.8'

services:
  sonarqube:
    profiles: [services]
    image: sonarqube:latest
    ports:
      - "9000:9000"
    networks:
      - sonar-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/api/system/status"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  sonar-scanner:
    profiles: [tools]
    image: sonarsource/sonar-scanner-cli
    env_file:
      - ../../.env
    volumes:
      - ../../:/usr/src
      - ./coverage.out:/usr/src/coverage.out
    working_dir: /usr/src
    command: >
      sonar-scanner -Dsonar.host.url=${SONAR_HOST_URL:-http://sonarqube:9000} -Dsonar.login=${SONAR_TOKEN} -X

    # depends_on:
    #   sonarqube:
    #     condition: service_healthy
    networks:
      - sonar-network
  
  trivy:
    profiles: [tools]
    image: aquasec/trivy:latest
    volumes:
      - ../:/workspace
    working_dir: /workspace
    command: fs --security-checks vuln,config --skip-dirs "tmp/,import_service/" .
    networks:
      - sonar-network

networks:
  sonar-network:
    driver: bridge
