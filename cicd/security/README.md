# Security

## Run local SonarQube

```bash
docker-compose -f cicd/security/docker-compose.yml --profile services up -d
```

Then go to `http://localhost:9000` and login with `admin/admin` and change the password.

After that, go to `http://127.0.0.1:9000/account/security` and generate a token and add to the `.env` file as `SONAR_TOKEN`.


## Run tests

```bash
docker-compose build
docker-compose up -d


docker exec -it db bash
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P "12345678Abc" -C

CREATE DATABASE tc_api_tests;
go


docker-compose exec run php bash
php artisan test
```

## Run SonarQube Scanner

```bash
docker-compose -f cicd/security/docker-compose.yml --profile tools run sonar-scanner
```