# Endpoints

A continuación se describen los endpoints de este sistema y la forma de interactuar con ellos.

## Headers

Los endpoints requieren determinadas combinaciones de headers. Cada endpoint especifica la combinación necesitada en el listado. Este apartado especifica las combinaciones y su contenido.

### Ninguno

No se requiere ningún header especial en la petición.

### API key

- `APIKey`: La llave de la API Gateway encriptada.

### Biometría

- `APIKey`: La llave de la API Gateway encriptada.
- `Authorization`: `Bearer` + token que se obtiene usando el endpoint `POST /biometry`.
- `DeviceAuth`: Misma información que la enviada en `POST /biometry`.
- `DuiAuth`: Misma información que la enviada en `POST /biometry`.
- `BiometryAuth`: Se obtiene usando el endpoint `POST /biometry`.

### Usuario registrado

- `APIKey`: La llave de la API Gateway encriptada.
- `Authorization`: `Bearer` + token que se obtiene al hacer login.
- `DeviceAuth`: Misma información que la enviada al momento de hacer login
- `UserAuth`: `client_id` del usuario en el sistema del banco.

### Ambos

Algunos endpoints aceptan autenticación de los grupos Biometría, Usuario registrado. Es decir, se pueden enviar los headers de un grupo o del otro. **No ambos al mismo tiempo**.

### Dummy header

Algunos endpoints aceptan el header `Dummy`, con valores separados por coma, que desactivan determinados comportamientos del sistema para ayudar el desarrollo. Esto solo está disponible en ambientes distintos de `production`. A continuación se describen los valores posibles y comportamiento general, pero se recomienda ver el apartado "Dummy header" de cada endpoint para conocer exactamente los cambios.

- `no_external`: En general, desactiva las llamadas a servicios de terceros y asume una respuesta positiva de estos.

## Endpoints

### Login

Hace login a la plataforma, el token devuelto se debe enviar en las peticiones de endpoints que requieren acceso.

Headers: Ninguno.

```
POST /api/login
```

Petición:

```json
{
    "nickname": "A9Z+23OCOuLkLLBt++bM0=",
    "password": "0EgssM73kX3WUEBA=",
    "device_name": "vlcl",
    "device_id": "c5a92bde-a18b-3da0-9b59-4f1cb9e20712",
    "firebase_token": "9a329702-e5b1-38e2-a2cf-8d03fde64165",
    "latitude": "0",
    "longitude": "0",
    "is_huawei": false
}
```

* `nickname`: Requerido. Cadena encriptada.
* `password`: Requerido. Cadena encriptada.
* `device_name`: Requerido. Cadena.
* `device_id`: Requerido. Cadena.
* `firebase_token`: Requerido. Cadena.
* `latitude`: Opcional pero la propiedad siempre debe estar presente. Cadena. El valor númerico debe estar entre -90 y 90.
* `longitude`: Opcional pero la propiedad siempre debe estar presente. Cadena. El valor númerico debe estar entre -180 y 180.
* `is_huawei`: Opcional. Bool. Se asume `false` si ausente.

Respuesta 200:

```json
{
    "data": {
        "token": "eyJ0eXAiOijMM9L8VPwY",
        "refresh_token": "eyJ0eXAiOijMM9L8VPwY",
        "customer_id": "17",
        "creditcard_application_id": 921,
        "message": null,
        "code": 1
    }
}
```

* `token`: Cadena. Token de autorización.
* `client_id`: Cadena. ID único de cliente. Puede ser que no represente un valor númerico.
* `creditcard_application_id`: Cadena. Puede ser que no represente un valor númerico.
* `message`: Cadena. Nullable. Puede especificar el valor "Debe ingresar nueva clave" que denota que el usuario debe crear una nueva clave antes de proceder.
* `code`: Entero. 1: Login exitoso. 3: Cambiar contraseña por primera vez.

Respuesta 400:

```json
{
    "message": "Tus datos de inicio no son correctos. Intenta nuevamente",
    "error_code": "wrong_credentials",
    "code": 7,
    "available_attempts": 2
}
```

Credenciales incorrectas. El número de intentos se reinicia con un login exitoso (respuestas 200) o cambiando la contraseña con `POST /api/resetpassword`.

```json
{
    "message": "Se debe cambiar la contraseña por primera vez.",
    "error_code": "invalid_first_login_period",
    "code": 4
}
```

Antes de proseguir, se debe de cambiar la contraseña.

```json
{
    "message": "Tu contraseña ha expirado.",
    "error_code": "invalid_password_period",
    "code": 4
}
```

Antes de proseguir, se debe de cambiar la contraseña.

En el caso de que la respuesta no incluya un cuerpo, cualquiera de los siguientes es posible:

* No se pudo desencriptar uno o más campos.

Respuesta 403:

```json
{
    "message": "Tu usuario se encuentra bloqueado. Llámanos al 2266-7777.",
    "error_code": "user_blocked_by_otp",
    "code": 10
}
```

El usuario está bloqueado por OTP.

```json
{
    "message": "Tu usuario se encuentra bloqueado. Llámanos al 2266-7777.",
    "error_code": "user_blocked_by_face_auth",
    "code": 10
}
```

El usuario está bloqueado por face auth.

```json
{
    "message": "Tu usuario se encuentra bloqueado. Llámanos al 2266-7777.",
    "error_code": "user_disabled",
    "code": 10
}
```

El usuario ha sido desactivado. Se debe cambiar la contraseña con `POST /api/resetpassword` para desbloquearlo.

```json
{
    "message": "Este usuario no tiene permisos de ingreso al sistema.",
    "error_code": "no_permission_for_system",
    "code": 8
}
```

El usuario no tiene el sistema predeterminado asignado.

Respuesta 422:

```json
{
    "message": "Ha alcanzado el maximo de dispositivos registrados.",
    "error_code": "max_devices_reached",
    "code": 5
}
```

El usuario debe desenlazar dispositivos a su cuenta o usar un dispositivo previamente registrado antes de continuar.

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "nickname": [
            "El campo nickname es obligatorio."
        ],
        "password": [
            "El campo contraseña es obligatorio."
        ],
        "device_id": [
            "El campo device id es obligatorio."
        ],
        "device_name": [
            "El campo device name es obligatorio."
        ],
        "firebase_token": [
            "El campo firebase token es obligatorio."
        ],
        "longitude": [
            "El campo longitude es obligatorio."
        ],
        "latitude": [
            "El campo latitude es obligatorio."
        ]
    }
}
```

Cualquiera de los campos no sigue las reglas de validación antes descritas.

### Biometric login

Hace login a la plataforma usando un dispositivo previamente registrado sin usar usuario ni contraseña. El token devuelto se debe enviar en las peticiones de endpoints que requieren acceso.

Headers: Ninguno.

```
POST /api/biometric-login
```

Petición:

```json
{
    "signed_data": "A9Z+23OCOuLkLLBt++bM0=",
    "device_name": "vlcl",
    "device_id": "c5a92bde-a18b-3da0-9b59-4f1cb9e20712"
}
```

* `signed_data`: Requerido. Cadena. El challenge del device firmado con la llave privada del mismo device en base 64.
* `device_name`: Requerido. Cadena. Previamente registrado.
* `device_id`: Requerido. Cadena. Previamente registrado.

Respuesta 200:

```json
{
    "data": {
        "token": "eyJ0eXAiOijMM9L8VPwY",
        "refresh_token": "eyJ0eXAiOijMM9L8VPwY",
        "customer_id": "17",
        "creditcard_application_id": 921,
        "message": null,
        "code": 1
    }
}
```

* `token`: Cadena. Token de autorización.
* `refresh_token`: Cadena. Token para actualizar.
* `customer_id`: Cadena. ID único de cliente. Puede ser que no represente un valor númerico.
* `creditcard_application_id`: Entero. Nullable.
* `message`: Cadena. Nullable.

Respuesta 400:

```json
{
    "message": "Dispositivo no encontrado.",
    "error_code": "device_not_found",
    "code": 2
}
```

La combinación de nombre y ID del dispositivo no se encuentran registrados. Para registrar un nuevo dispositivo se debe hacer `POST /login` con el mismo.

```json
{
    "message": "Tu contraseña ha expirado.",
    "error_code": "invalid_password_period",
    "code": 4
}
```

Antes de proseguir, se debe de cambiar la contraseña.

```json
{
    "message": "Tus datos de inicio no son correctos. Intenta nuevamente.",
    "error_code": "wrong_biometric_credentials",
    "code": 9
}
```

Datos incorrectos.

Respuesta 403:

```json
{
    "message": "Tu usuario se encuentra bloqueado. Llámanos al 2266-7777.",
    "error_code": "user_blocked_by_otp",
    "code": 10
}
```

El usuario está bloqueado por OTP.

```json
{
    "message": "Tu usuario se encuentra bloqueado. Llámanos al 2266-7777.",
    "error_code": "user_blocked_by_face_auth",
    "code": 10
}
```

El usuario está bloqueado por face auth.

```json
{
    "message": "Tu usuario se encuentra bloqueado. Llámanos al 2266-7777.",
    "error_code": "user_disabled",
    "code": 10
}
```

El usuario ha sido desactivado.

```json
{
    "message": "Este usuario no tiene permisos de ingreso al sistema.",
    "error_code": "no_permission_for_system",
    "code": 8
}
```

El usuario no tiene el sistema predeterminado asignado.

Respuesta 422:

```json
{
    "message": "Device lacks a public key.",
    "error_code": "device_without_public_key",
    "code": 11
}
```

Se debe proveer primero una llave pública al dispositivo por medio de `POST /savebiometricpublickey` antes de continuar.

```json
{
    "message": "Device lacks a challenge.",
    "error_code": "device_without_challenge",
    "code": 12
}
```

Se debe generar primero un challenge del dispositivo por medio de `PUT /devices/{id}/challenge` antes de continuar.

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "signed_data": [
            "El campo signed data es obligatorio."
        ],
        "device_name": [
            "El campo device name es obligatorio."
        ],
        "device_id": [
            "El campo device id es obligatorio."
        ]
    }
}
```

Cualquiera de los campos no sigue las reglas de validación antes descritas.

Respuesta 429:

```json
{
    "message": "Demasiados intentos.",
    "error_code": "too_many_attempts",
    "code": 6,
    "blocking_time": "1"
}
```

Demasiados intentos fallidos seguidos. Se debe esperar los minutos indicados en `blocking_time` y volver a intentar.

### Actualizar número de teléfono

Actualiza el número de teléfono del usuario actual.

Headers: Usuario registrado.

```
PUT /api/users/phone
```

Petición:

```json
{
    "shared_key": "sha9dd89",
    "otp_code": "q0w9b",
    "phone_number": "57503571"
}
```

* `shared_key`: Requerido. Cadena.
* `otp_code`: Requerido. Cadena.
* `phone_number`: Requerido. Cadena. Deben ser 8 dígitos exactamente.

Respuesta 204: La respuesta no incluye un cuerpo. El número ha sido actualizado.

Respuesta 422:

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "shared_key": [
            "El campo shared key debe ser una cadena de caracteres."
        ],
        "otp_code": [
            "No es un OTP válido."
        ],
        "phone_number": [
            "phone number ya ha sido registrado."
        ]
    }
}
```

Cualquiera de los campos no sigue las reglas de validación antes descritas.

#### Dummy header

Si se incluye el header `Dummy: no_external`:

- La validez del código OTP se verifica usando un servicio de terceros, en este modo la petición no se hace y se asume que el código siempre es válido.

### Actualizar email

Actualiza el email del usuario actual.

Headers: Usuario registrado.

```
PUT /api/users/email
```

Petición:

```json
{
    "shared_key": "sha9dd89",
    "otp_code": "q0w9b",
    "email": "<EMAIL>"
}
```

* `shared_key`: Requerido. Cadena.
* `otp_code`: Requerido. Cadena.
* `email`: Requerido. Cadena. Debe ser una dirección de email.

Respuesta 204: La respuesta no incluye un cuerpo. El email ha sido actualizado.

Respuesta 422:

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "shared_key": [
            "El campo shared key debe ser una cadena de caracteres."
        ],
        "otp_code": [
            "No es un OTP válido."
        ],
        "email": [
            "email ya ha sido registrado."
        ]
    }
}
```

Cualquiera de los campos no sigue las reglas de validación antes descritas.

### Refrescar token de un usuario registrado

Este endpoint refresca el token obtenido con `POST /api/login` y devuelve uno nuevo. No se debe confundir con `PUT /api/token` que se usa para refrescar tokens obtenidos usando `POST /api/biometry`.

Headers: API key.

```
PUT /api/token/user
```

Petición:

```json
{
    "device_id": "DFsuqtOqK",
    "refresh_token": "q0w9b"
}
```

* `device_id`: Requerido. Cadena. Debe ser el mismo que se usó al momento de hacer login.
* `refresh_token`: Requerido. Cadena. Proveído al momento de hacer login.

Respuesta 200:

```json
{
    "data": {
        "token": "IhG8QEs",
        "refresh_token": "IhG8QEs"
    }
}
```

Respuesta 400:

```json
{
    "message": "El refresh token es inválido o ha caducado. Ingresa con tus credenciales de nuevo.",
    "error_code": "invalid_refresh_token",
    "code": null
}
```

El refresh token es inválido porque ha caducado u otra razón. Se debe hacer login de nuevo.

Respuesta 500: 

```
Ha ocurrido un error desconocido. Se puede intentar hacer login de nuevo.
```

### Verificar DUI

Verifica si el DUI pertenece a un usuario registrado en la plataforma.

Headers: Biometría.

```
GET /api/duis/{dui}
```

Parámetros:

* `dui`: DUI a verificar en la plataforma.

Petición: La petición no requiere datos en el cuerpo.

Respuesta 200: La respuesta no incluye un cuerpo. En este caso el DUI se encuentra registrado.

Respuesta 404: En este caso el DUI no se encuentra registrado.

Respuesta 422:

* `dui`:
    * "No es un DUI válido": No es una cadena de 8 dígitos, guión, un dígito. No se trata de un DUI computacionalmente válido. En este caso se puede asumir que no se encuentra registrado en la plataforma.

### Obtener un nuevo verification challenge

Genera una nueva de verificación para ser usada en login.

Headers: API key.

```
PUT /api/devices/{id}/challenge
```

Parámetros:

* `id`: El `device_id` de un dispositivo.

Petición: La petición no requiere datos en el cuerpo.

Respuesta 200:

```json
{
    "data": {
        "verification_challenge": "IhG8QEs/LDzevThcyCusAl8yCJEmn=="
    }
}
```

* `verification_challenge`: Se encuentra encriptado con la llave pública del dispositivo.

Respuesta 404: `id` no pertenece a un dispositivo registrado en la plataforma.

### Crear una conversación de Twilio

Crea una nueva conversación de Twilio y agrega un participante de chat web con el `ChatGrant` correspondiente.

Headers: Ambos.

```
POST /api/conversations
```

Petición:

```json
{
    "identity": "some_id"
}
```

* `identity`: Requerido. Cadena. Alfanumérico con `-`, `_`. De 2 a 30 caracteres. Identificador del participante.

Respuesta 200:

```json
{
    "data": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbG",
        "conversation_sid": "CHa673408202264598986b599b6023f5eb"
    }
}
```

* `access_token`: Token para la conversación creada. Véase https://www.twilio.com/docs/conversations/create-tokens
* `conversation_sid`: Identificador de la conversación creada. Véase https://www.twilio.com/docs/conversations/api/conversation-resource

Respuesta 422:

* `identity`:
    * No está presente. No es una cadena. Contiene caracteres distintos de a-z, 0-9, `_`, `-`. Tiene menos de 2 caracteres. Tiene más de 30 caracteres.

### Registrar un nuevo usuario

Registra un nuevo usuario en la plataforma para que pueda hacer login.

Headers: Biometría.

```
POST /api/users
```

Petición:

```json
{
    "customer_id": 100,
    "creditcard_application_id": 101
}
```

* `customer_id`: Requerido. Entero. Se obtiene usando los endpoints externos.
* `creditcard_application_id`: Requerido. Entero. Se obtiene usando los endpoints externos.

Respuesta 201: La respuesta no incluye un cuerpo. Se le enviará un email con los datos de acceso al usuario. El email es el proveído en los endpoints externos.

Respuesta 422:

* `customer_id`:
    * No está presente. No es un entero.
* `creditcard_application_id`:
    * No está presente. No es un entero.

### Obtener una encuesta por nombre

Muestra la encuesta solicitada por nombre.

Headers: Ambos.

```
GET /api/surveys?name={name}
```

Parámetros:

* `name`: Cadena. El nombre de una encuesta.

Petición: La petición no requiere datos en el cuerpo.

Respuesta 200:

```json
{
    "data":
    {
        "description": "61rUYNCpSFJoyjXG",
        "id": 1,
        "name": "jtJhUSQPz4EZfEnl",
        "questions":
        [
            {
                "id": 1,
                "options":
                [
                    {
                        "id": 1,
                        "question_id": 1,
                        "type": "OPTION_OPEN",
                        "value": "6UeSxRBo55wg9XkZ"
                    },
                    {
                        "id": 2,
                        "question_id": 1,
                        "type": "OPTION_CLOSED",
                        "value": "JOtCFbpBKkHB7Z9J"
                    },
                    {
                        "id": 3,
                        "question_id": 1,
                        "type": "OPTION_OPEN",
                        "value": "k9V53gFX4hNoIIEg"
                    }
                ],
                "question": "xkLMcwcDlCHhS29x",
                "survey_id": 1,
                "type": "CNT"
            },
            {
                "id": 2,
                "options":
                [
                    {
                        "id": 4,
                        "question_id": 2,
                        "type": "OPTION_OPEN",
                        "value": "W9QOhl5bT4ET2vN1"
                    },
                    {
                        "id": 5,
                        "question_id": 2,
                        "type": "OPTION_CLOSED",
                        "value": "8BkqS6P9SbmcKcKe"
                    }
                ],
                "question": "1u72lBR588q1H05d",
                "survey_id": 1,
                "type": "CTL"
            },
            {
                "id": 3,
                "options":
                [
                    {
                        "id": 6,
                        "question_id": 3,
                        "type": "OPTION_OPEN",
                        "value": "zr4UhOu0npbupe6D"
                    },
                    {
                        "id": 7,
                        "question_id": 3,
                        "type": "OPTION_OPEN",
                        "value": "9HMq4KzEEuetTd8u"
                    },
                    {
                        "id": 8,
                        "question_id": 3,
                        "type": "OPTION_CLOSED",
                        "value": "frvloZJ1sYN8N1k9"
                    }
                ],
                "question": "6fm1wqEXZzUs6KvY",
                "survey_id": 1,
                "type": "CNT"
            }
        ]
    }
}
```

* `description`: Cadena. Descripción de la encuesta.
* `id`: Entero. ID de la encuesta.
* `name`: Cadena. Nombre de la encuesta.
* `questions`: Arreglo. Preguntas de la encuesta.
* `questions.*.id`: Entero. ID de la pregunta.
* `questions.*.question`: Cadena. La pregunta.
* `questions.*.type`: Cadena. Tipo de pregunta.
    * `CNT`: Cuantitativa.
    * `CTL`: Cualitativa.
* `questions.*.survey_id`: Entero. ID de la encuesta a la que la pregunta pertenece.
* `questions.*.options`: Arreglo. Opciones posibles para la respuesta.
* `questions.*.options.*.id`: Entero. ID de la opción.
* `questions.*.options.*.question_id`: Entero. ID de la pregunta a la que la opción pertenece.
* `questions.*.options.*.type`: Cadena. Tipo de opción.
    * `OPTION_CLOSED`: Cerrada.
    * `OPTION_OPEN`: Abierta.
* `questions.*.options.*.value`: Cadena. Texto de la opción.

Respuesta 404: No se encontró una encuesta con ese nombre.

### Obtener una encuesta por ID

Muestra la encuesta solicitada por ID.

Headers: Ambos.

```
GET /api/surveys/{id}
```

Petición: La petición no requiere datos en el cuerpo.

Respuesta 200:

```json
{
    "data":
    {
        "description": "61rUYNCpSFJoyjXG",
        "id": 1,
        "name": "jtJhUSQPz4EZfEnl",
        "questions":
        [
            {
                "id": 1,
                "options":
                [
                    {
                        "id": 1,
                        "question_id": 1,
                        "type": "OPTION_OPEN",
                        "value": "6UeSxRBo55wg9XkZ"
                    },
                    {
                        "id": 2,
                        "question_id": 1,
                        "type": "OPTION_CLOSED",
                        "value": "JOtCFbpBKkHB7Z9J"
                    },
                    {
                        "id": 3,
                        "question_id": 1,
                        "type": "OPTION_OPEN",
                        "value": "k9V53gFX4hNoIIEg"
                    }
                ],
                "question": "xkLMcwcDlCHhS29x",
                "survey_id": 1,
                "type": "CNT"
            },
            {
                "id": 2,
                "options":
                [
                    {
                        "id": 4,
                        "question_id": 2,
                        "type": "OPTION_OPEN",
                        "value": "W9QOhl5bT4ET2vN1"
                    },
                    {
                        "id": 5,
                        "question_id": 2,
                        "type": "OPTION_CLOSED",
                        "value": "8BkqS6P9SbmcKcKe"
                    }
                ],
                "question": "1u72lBR588q1H05d",
                "survey_id": 1,
                "type": "CTL"
            },
            {
                "id": 3,
                "options":
                [
                    {
                        "id": 6,
                        "question_id": 3,
                        "type": "OPTION_OPEN",
                        "value": "zr4UhOu0npbupe6D"
                    },
                    {
                        "id": 7,
                        "question_id": 3,
                        "type": "OPTION_OPEN",
                        "value": "9HMq4KzEEuetTd8u"
                    },
                    {
                        "id": 8,
                        "question_id": 3,
                        "type": "OPTION_CLOSED",
                        "value": "frvloZJ1sYN8N1k9"
                    }
                ],
                "question": "6fm1wqEXZzUs6KvY",
                "survey_id": 1,
                "type": "CNT"
            }
        ]
    }
}
```

* `description`: Cadena. Descripción de la encuesta.
* `id`: Entero. ID de la encuesta.
* `name`: Cadena. Nombre de la encuesta.
* `questions`: Arreglo. Preguntas de la encuesta.
* `questions.*.id`: Entero. ID de la pregunta.
* `questions.*.question`: Cadena. La pregunta.
* `questions.*.type`: Cadena. Tipo de pregunta.
    * `CNT`: Cuantitativa.
    * `CTL`: Cualitativa.
* `questions.*.survey_id`: Entero. ID de la encuesta a la que la pregunta pertenece.
* `questions.*.options`: Arreglo. Opciones posibles para la respuesta.
* `questions.*.options.*.id`: Entero. ID de la opción.
* `questions.*.options.*.question_id`: Entero. ID de la pregunta a la que la opción pertenece.
* `questions.*.options.*.type`: Cadena. Tipo de opción.
    * `OPTION_CLOSED`: Cerrada.
    * `OPTION_OPEN`: Abierta.
* `questions.*.options.*.value`: Cadena. Texto de la opción.

Respuesta 404: No se encontró una encuesta con ese id.

### Responder una encuesta

Guarda las respuestas a una encuesta.

Headers: Ambos.

```
POST /api/surveys/{id}/answers
```

Petición:

```json
{
    "answers": [
        {
            "option_id": 1,
            "other": "texto"
        },
        {
            "option_id": 20,
            "other": null
        }
    ]
}
```

* `answers`: Requerido. Arreglo. Contiene las opciones seleccionadas.
* `answers.*.option_id`: Requerido. Entero. El ID de una opción de una pregunta de una encuesta.
* `answers.*.other`: Opcional pero la propiedad siempre debe estar presente. Cadena. Máximo 255 caracteres. Algunas preguntas permiten introducir texto.

Respuesta 201: La respuesta no incluye un cuerpo. Las respuestas se han guardado.

Respuesta 422:

* `survey_id`: El ID de la encuesta no existe.
* `answers`: No está presente. No es un arreglo.
* `answers.*.option_id`: No está presente. No es un entero. No es un ID de una opción. No es una opción de la encuesta.
* `answers.*.other`: No está presente. No es una cadena o `null`. Son más de 255 caracteres.

### Eliminar un usuario

Elimina un usuario por ID (no `client_id`). Este endpoint solo está disponible en ambientes no productivos.

Headers: Ninguno.

```
DELETE /api/users/{id}
```

Petición: La petición no requiere datos en el cuerpo.

Respuesta 204: La respuesta no incluye un cuerpo.

Respuesta 404: No se encontró el usuario.

### Eliminar usuarios

Elimina uno o más usuarios por id, customer_id (client_id) o dui. Este endpoint solo está disponible en ambientes no productivos.

Headers: Ninguno.

```
DELETE /api/users?id={id}&customer_id[]={customer_id_1}&customer_id[]={customer_id_2}&dui={dui}
```

Parámetros:

* `id`: Entero o arreglo de enteros, opcional. ID del usuario.
* `customer_id`: Entero o arreglo de enteros, opcional. ID externo del usuario.
* `dui`: Cadena o arreglo de cadenas, opcional. DUI del usuario.

Los parámetros se pueden combinar y repetir. Todos los siguientes son ejemplos válidos:

* `DELETE /api/users?id=1`. Individual.
* `DELETE /api/users?id[]=1&id[]=2`. Como arreglo.
* `DELETE /api/users?dui[]=98112790-7&dui[]=981127907`. Ambos apuntan al mismo usuario.
* `DELETE /api/users?id[]=1&id[]=1`. Duplicados.
* `DELETE /api/users?id=1&customer_id[]=2&customer_id[]=3&dui=98112790-7`. Combinando más de uno.

Petición: La petición no requiere datos en el cuerpo.

Respuesta 200:

```json
{
    "data": [
        {
            "id": 1,
            "customer_id": 2,
            "dui": "7983241-3"
        },
        {
            "id": 2,
            "customer_id": 3,
            "dui": "4983121-3"
        },
    ]
}
```

Los usuarios eliminados. Si no encuentra algún usuario, simplemente se ignora ese dato.

### Eliminar un dispositivo

Elimina un dispositivo del usuario.

Headers: Usuario registrado.

```
DELETE /api/devices/{id}
```

Parámetros:

* `id`: Cadena. Requerido. ID de un dispositivo del usuario, excepto el del dispositivo que actualmente está haciendo la petición.

Petición: La petición no requiere datos en el cuerpo.

Respuesta 204: La respuesta no incluye un cuerpo.

Respuesta 422:

* `id`:
    * El ID del dispositivo no se encuentra registrado bajo el usuario actual. El dispositivo a eliminar es el que se está usando actualmente.

### Mantenimiento

Devuelve un mensaje y fechas de mantenimiento si el sistema no se encuentra disponible.

Headers: Ninguno.

```
GET /api/maintenance-periods
```

Petición:

Respuesta 200:

```json
{
    "data": {
        "message": "some message"
    }
}
```

* `message`: Cadena. Un mensaje para el usuario.

En éste modo el sistema no se encuentra disponible. Otros endpoints no responderan de manera positiva. Se puede verificar el estado llamando éste mismo endpoint más tarde.

Respuesta 204: La respuesta no incluye un cuerpo. El sistema se encuentra funcional.

### Beneficios

Devuelve todos los beneficios disponibles.

Headers: Biometría.

```
GET /api/benefits?format={format}
```

Parámetros:

* `format`: Cadena, opcional. `markdown`: devuelve la descripción en formato Markdown. De lo contrario la devuelve en HTML.

Petición: Vacía.

Respuesta 200:

```json
{
    "data":
    [
        {
            "title": "Viajes",
            "description": "Por cada dólar en compras te damos 1.5 puntos para canjear por millas Lifemiles. Puedes acumular hasta 15,000 puntos mensuales ¡Tus puntos nunca vencen!",
            "example": "Ej: Si gastas $5,000 por mes recibes los 7,500 puntos",
            "main": "0",
            "order": "3",
            "longcode": "VIAJES",
            "icon": "<URL>/imagery/1",
            "storehouse_id": "1"
        },
        {
            "title": "Compras",
            "description": "Por cada dólar en compras te damos 3 puntos. Puedes acumular hasta 30,000 puntos mensuales ¡Tus puntos nunca vencen!",
            "example": "Ej: Si gastas $5,000 en el mes acumulas 15,000 puntos",
            "main": "1",
            "order": "1",
            "longcode": "COMPRAS",
            "icon": "<URL>/imagery/2",
            "storehouse_id": "2"
        }
    ]
}
```

## Endpoints externos

El sistema también funciona como middleware para otros sistemas externos. En general, el funcionamiento con estas rutas es el siguiente:

1. Se valida el token (header `Authorization`) con un servicio externo. Si está presente, no todos los endpoints lo requieren.
2. Se sustituye el header `Authorization` original con `TokenAuth`, el valor es el mismo. Si está presente.
3. Se valida el header `APIKey`. Debe estar encriptado con RSA. De estar presente, no todos los endpoints lo requieren.
4. Se sustituye el valor del header `APIKey` por uno que se utiliza internamente para comunicarse con otros servicios.
5. Se agrega el header `IpAddressAuth` con la IP de la petición original.
6. Se agregan los siguientes headers si se encuentran presentes en la petición original: `DeviceAuth`, `DuiAuth`, `BiometryAuth`, `UserAuth`.
7. Se agregan el cuerpo y parámetros de la petición original.
8. Con esta información, se hace la petición al servicio externo.
9. Se toma el resultado de la petición y el estado HTTP de la misma y se devuelve tal cual a la fuente de la petición original.

Para información sobre como interactuar con estos endpoints, consúltese la documentación de tales servicios externos.

### Mapa de endpoints externos

Headers: API key.

* `PUT /api/token` -> `PUT /TokenAuthorization`.
