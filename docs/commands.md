# Comandos

Los siguientes comandos se pueden correr desde la terminal usando `php artisan {comando} {argumentos}`. Estos comandos no verifican el ambiente, así que se deben correr con cuidado.

## Encriptar una cadena

```
php artisan encrypt {data}
```

Encripta la cadena `data` usando la llave RSA configurada en la DB. Devuelve el resultado codificado como base 64.

Ejemplo:

```
php artisan encrypt abc

WYwFVMIPpXJMwETXIxhwcSqebePPsOgMfH7zTDGH9nHa5qIosMgEJp6UgSmsBClLXW3hS2WX5aiMXdNqH6FbfZFLK5nI3fvqZNRiSMKuYIrY2qsuI/AZotsPLSKf9vA1nSBgdxNHydl2nZFEONgQ2HxxqHR3qOllvmoZF8q+A/hoq0A61KduwsSUAzWcwRJe1GYLUMOPii4tAaWNnTE68l1ALoGP8cfqwALgSoL8xHBcNxZCHMUBAvxUhrU0ERs74ks9j92yavlHg3bhcZ/3LUGwIVtHYidUN9OI1WOhUzXipu2JaNAJ7eecDcDglAxNjIu3LUxHk1F9JMMHU5r+PGxBoonbJCyzqSGpgq6b3qpF4s8kXr2gk7frALPdIUdN6+fnaPwq5gxME8Pw4aB/COw5nanPTuFLefmHzn7n7an1taEhCI0yYQDc7KZt2SIbH24+iByNfFlTp6ZezxRdbvTIsMMgp37WbPgqhKQDZSkOkzkqhDjGpd/+y4AALG1vHLajWrTWQQzVVZU+amcIockgbDHMa2IQ9PqNRtd5ODsxMi+rpB9uIDXgKQLJ7nxToNHUoJsxFgSXbXuhyJHkFEmlG//hrjOWUhUHJEMl7665uW3XfWm3b3x9pCsc0MMsI0OUa/xhvJoABz9FSxZBX338iwwBWGdSXCr/kXFKrGo=
```

## Desencriptar una cadena

```
php artisan decrypt {data}
```

Desencripta la cadena `data` en base 64 usando la llave RSA configurada en la DB.

Ejemplo:

```
php artisan decrypt StPHLNFQeSLPucZJaEWCFY1HLG0M4uf9kW9qGsyUn5hxOg5LutwI4ywVFz+UgBZwXFZjwSZbXK/h/5bDf0dvO2uqJpraENbSwdIxdInEuVg+6BNgDr/XPlvstGgb4KyybF8bjrh6kxPs3VH0QkeqcLLiEPcTC/shbAYA4dKw7efUU1dEmRt9CGHRGk+LG69Phkc5iHmMzbMnN+9QbGWZtYMOORgOEZGFxv2Y7yX55yGlTbTBry1zdPJUxiQBTkanTB0sr7Mr00RbNyHBiBxGACbSlYydWBpyXi526eLgqq6kn305ZkJWcssUzWsykMmZW1Oi7ZcEVKtQ4HArN/5Tk4k7vUzQydk/T2BVqHkmlrjDSV66Sm/bPk873H4tB2u3k4zCEsJcgagGFY/q+14fpYwuMHwvG9z6Jq2yi7hqlN+sZt2NYaYQqaWm6MvCbVXq4OqH4aRvWSNOT+Ax92m3xNVc2zG8SzL7AHrCV/0E2XJMHbJRiHcGcB2MLB3lW3XOlLXM7y83TQ5mTjco/bOmNg3LMwjj3pJeXt8GC1CGfg1xTW9aE2iviY7ttlyOAdKdeD/2dUCQF6saQiPELN5Jdi52Kgc95+3MoBKeYW5AEE922C2zF7Dxev1jGh5+GuCEHSqT2nXNH+CybVV0EyZnRerr+MWn0AAQZFiWd3o+nBI=

abc
```

## Crear un cliente

```
php artisan make:client
```

Crea un nuevo cliente en DB y devuelve información sobre el mismo.

Ejemplo:

```
php artisan make:client

ID: 113
Nickname: blxXhOsj4T5YOFNd
Password: 123456
Device ID: 00bfe117-94df-3781-84a9-cfdc64e24059
Device name: riqj
Encrypted nickname: Mti/TA1kXB0sxAXz
Encrypted password: TA1kXB0sxAXz
```

## Eliminar clientes

```
php artisan delete:client {--id=} {--dui=} {--customer-id=}
```

Elimina un cliente de la DB y algunas de sus relaciones. Busca por id, dui o customer_id. Se puede proveer un listado separado por coma y combinar opciones.

Ejemplos:

Si se proveen datos que no se encuetran, simplemente se ignoran.

```
php artisan delete:client --id=1,2 --dui=55779856-0,66361263-4,71277431-1

Deleted users:
id: 1, customer_id: 1560051340, dui: 92807034-5
id: 2, customer_id: 1202928037, dui: 26416962-2
```

Al llamarse sin argumentos no hay un error.

```
php artisan delete:client

No users to delete
```

## Crear un par de llaves RSA

```
php artisan make:keys
```

Crea un nuevo par de llaves RSA y las devuelve. No se registran en el sistema.

Ejemplo:

```
php artisan make:keys

Public key:
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApY+KzFfT1cc4WeokbnmU
CtlJ4TkxpUj6vVUABV9KklDDd9VfCv0mwM3ZYb9WqFZvGRzR51Ky7FEbHaZKDDl1
Cl3Opa2tFeKIb4fH59Cb7VO9XJv+RpCxk3HcbrOneDwqCzwUUCwO8ljeugaSkZ4g
yM9UW6NiqsS2BbUzh5vu/zZ5C6Im+SEPJc8/i7Y3GQQyueyWKfmuEqvK7bBfbz4E
9CIndVm42tZuqxGrx8j49oGf/qx6QoNW+3VyDq0q4XL7RqcNlJESTYO+39KdNsZi
M7FDN5QNa5N2P/fSzA1XdS6u0Uo6TgqRyF9qH/5c4guJPxSiAQk0VCPAvJ4Hn949
twIDAQAB
-----END PUBLIC KEY-----

Private key:
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```

## Firmar con llave privada

```
php artisan sign {data} {file}
```

Firma la cadena `data` con la llave privada encontrada en `file`.

Ejemplo:
```
php artisan sign "abc" "private_key_file"

TcMqa6EJSxtVM6sv+Ngt2NpVgfxi+eB+NMWDIHOoC45zq99iBQmG9VME7qsRN7Z3k7LfcSgZYqb9KWZXNxLuqyMItmZyW8U2Q0L6keLYbngt+ZlIvmr3VYlKrkAMMnB6pPfbghnhEnMXACaHDx9NhHkLE6Vih9xPmh9nw0pCr+sQWLbfaIyrZd0h9hr4AY4p84W6oCh8RnwCVojB6yn6N3zvoJgvMo6sBJKXP6zyxp0W8NU4a7DgzLMTtJyaRZwb9+D51VruFeu90FyUU3+kTpl4+xwYW8M/jYa0ZznvhWTlVB6fkfOCot/134PY1cUMEssm/jHRAHb+Ohu1YJ2itQ==
```
