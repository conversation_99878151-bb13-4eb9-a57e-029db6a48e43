# Architecture

![](bin/architecture.png)

In general, the project should follow the MVC pattern with a clear domain logic layer (services, tasks) defined. For anything not described here, feel free to introduce a layer between the general components or, even better, use the many tools that the Laravel framework provides.

## Components

### Controller and other callers

A controller (and other callers like listeners) should only call the domain layer components, like services and tasks, and classes to construct the HTTP response, like Laravel resources and other HTTP helper classes. That is, a controller merely calls the domain layer and passes the result, if any, to a resource class. A controller may call one or more tasks or services and compose the result into a response, but if the number is too high it is better to put that logic into a service and just take the result, feed it to a resource—for example.

### Service

A service is a composite domain component. It calls multiple tasks and composes their results. A service may even call other services or trigger Laravel events. In general, reserve the use of services for complex or long logic that spans multiple tasks or classes.

### Task

A task is a simple class that receives state and actuates on it, maybe producing a result. A task is a simpler version of a service and handles domain logic exclusively. It may request database information (or from other sources) but only through repositories, Laravel models or other classes that encapsulate the retrieving that data. It may also make use of any other help or utility classes.

### Model and other data entities

This is where data retrieval and storage is handled. SQL and Eloquent's methods may be called freely here so that the classes can expose simple methods that return or store the information.
