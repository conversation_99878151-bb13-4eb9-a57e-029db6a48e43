PASOS MANTENIMIENTO DE UNA TABLA

1- 

abra el archivo "config\nova-permissions.php", (ejemplo en carpeta) 
e ingrese la configuracion de permisos para el nuevo recurso ejemplo para "JobTitles" 

		'view JobTitles' => [
            'display_name' => 'Ver Cargo laboral',
            'description'  => 'Can view JobTitles',
            'group'        => 'Administraci�n Cargo laboral',
        ],

        'create JobTitles' => [
            'display_name' => 'Crear Cargo laboral',
            'description'  => 'Can create JobTitles',
            'group'        => 'Administraci�n Cargo laboral',
        ],

        'edit JobTitles' => [
            'display_name' => 'Editar Cargo laboral',
            'description'  => 'Can edit JobTitles',
            'group'        => 'Administraci�n Cargo laboral',
        ],
        
        'delete JobTitles' => [
            'display_name' => 'Eliminar Cargo laboral',
            'description'  => 'Can delete JobTitles',
            'group'        => 'Administraci�n Cargo laboral',
        ],
		
2- 

ejecute los siguientes comandos (para crear recurso, modelo, migracion y politica de acceso)	

php artisan nova:resource JobTitles;
php artisan make:model JobTitles;
php artisan make:migration create_job_titles_table;
php artisan make:policy JobTitlesPolicy;


3-
modficaci�n de men�, abra el archivo "\app\Providers\NovaServiceProvider.php" (ejemplo en carpeta)
y defina el recurso en la ubicacion de men� deseada, revise la l�nea 203, ejemplo: 

NovaResource::make(\App\Nova\JobTitles::class)->label(__('Mantenimiento de Cargos Laborales')),

4- 
defina las politicas de acceso utilizando la clase "App\Helpers\Permission" en archivo 
"\app\Policies\JobTitlesPolicy.php" (ejemplo en carpeta), para los m�todos viewAny, view, create, update y delete, seg�n 
la configuraci�n de permisos, ejemplo:

    public function viewAny(User $user)
    {   
        return Permission::CheckPermission('view JobTitles');
    }

5-
en el archivo "\database\migrations\2021_03_01_151828_create_job_titles_table.php" (ejemplo en carpeta) 
defina los campos para la tabla lineas 14 a 21, donde se define tamb�en "timestamps" para fecha de creacion y modificaci�n y 
softDeletes para softDeletes

5.1-
en el archivo "\app\Models\JobTitles.php" defina los campos que se llenar�n y los campos en los que se hara logs
ejemplo

class JobTitles extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    
    protected $fillable = ['name'];
    
    protected static $logAttributes = ['name'];

    protected static $logName = 'JobTitles';

    protected static $logOnlyDirty = true;

    public function getDescriptionForEvent(string $eventName): string
    {
        return $eventName;
    }
}

6- 
ejecute comando: php artisan migrate, para actualizar base de datos

7- 
abra el recurso "\app\nova\JobTitles.php", (ejemplo en carpeta) para definir c�mo se va a mostrar el recurso, 
la funcion "label" para nombre en plural y la funcion "singularLabel" para nombre en singular, de la l�nea 49 a 
la 57, funcion "fields" para definir los campos a mostrar. 

8- 
ejecute los siguientes comandos: 
php artisan cache:clear;
php artisan route:clear;
php artisan config:clear;
php artisan view:clear;


