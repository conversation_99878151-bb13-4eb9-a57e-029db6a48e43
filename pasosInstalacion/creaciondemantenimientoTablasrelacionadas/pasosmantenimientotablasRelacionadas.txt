PASOS MANTENIMIENTO DE TABLAS RELACIONADAS

1- 

abra el archivo "config\nova-permissions.php", (ejemplo en carpeta) 
e ingrese la configuracion de permisos para el nuevo recurso ejemplo para "EconomicSectors" y "EconomicSectorCompanies" 

'view EconomicSectors' => [
            'display_name' => 'Ver Sectores econ�micos',
            'description'  => 'Can view economic_sectors',
            'group'        => 'Administraci�n Sectores econ�micos',
        ],

        'create EconomicSectors' => [
            'display_name' => 'Crear Sectores econ�micos',
            'description'  => 'Can create economic_sectors',
            'group'        => 'Administraci�n Sectores econ�micos',
        ],

        'edit EconomicSectors' => [
            'display_name' => 'Editar Sectores econ�micos',
            'description'  => 'Can edit economic_sectors',
            'group'        => 'Administraci�n Sectores econ�micos',
        ],
        
        'delete EconomicSectors' => [
            'display_name' => 'Eliminar Sectores econ�micos',
            'description'  => 'Can delete economic_sectors',
            'group'        => 'Administraci�n Sectores econ�micos',
        ],
		'view EconomicSectorCompanies' => [
            'display_name' => 'Ver Empresas bajo sector econ�mico',
            'description'  => 'Can view economic_sectors_companies',
            'group'        => 'Administraci�n Empresas bajo sector econ�mico',
        ],

        'create EconomicSectorCompanies' => [
            'display_name' => 'Crear Empresas bajo sector econ�mico',
            'description'  => 'Can create economic_sectors_companies',
            'group'        => 'Administraci�n Empresas bajo sector econ�mico',
        ],

        'edit EconomicSectorCompanies' => [
            'display_name' => 'Editar Empresas bajo sector econ�mico',
            'description'  => 'Can edit economic_sectors_companies',
            'group'        => 'Administraci�n Empresas bajo sector econ�mico',
        ],
        
        'delete EconomicSectorCompanies' => [
            'display_name' => 'Eliminar Empresas bajo sector econ�mico',
            'description'  => 'Can delete economic_sectors_companies',
            'group'        => 'Administraci�n Empresas bajo sector econ�mico',
        ],
		
2- 

ejecute los siguientes comandos (para crear recurso, modelo, migracion y politica de acceso)	

php artisan nova:resource EconomicSectors;
php artisan make:model EconomicSectors;
php artisan make:migration create_economic_sectors_table;
php artisan make:policy EconomicSectorsPolicy;

php artisan nova:resource EconomicSectorCompanies;
php artisan make:model EconomicSectorCompanies;
php artisan make:migration create_EconomicSectorCompanies_table;
php artisan make:policy EconomicSectorCompaniesPolicy;


3-
modficaci�n de men�, abra el archivo "\app\Providers\NovaServiceProvider.php" (ejemplo en carpeta)
y defina el recurso en la ubicacion de men� deseada, revise la l�nea 204 y 205, ejemplo: 

NovaResource::make(\App\Nova\EconomicSectors::class)->label(__('Mantenimiento Sectores Econ�micos')),
NovaResource::make(\App\Nova\EconomicSectorCompanies::class)->label(__('Mantenimiento Empresas bajo sector econ�mico')),

4- 
defina las politicas de acceso utilizando la clase "App\Helpers\Permission" en los archivos 
"\app\Policies\EconomicSectorsPolicy.php" y "\app\Policies\EconomicSectorCompaniesPolicy.php" (ejemplos en carpeta), para los m�todos viewAny, view, create, update y delete, seg�n 
la configuraci�n de permisos, ejemplo:

    public function viewAny(User $user)
    {   
        return Permission::CheckPermission('view JobTitles');
    }

5-
en el archivo "\database\migrations\2021_03_01_125853_create_economic_sectors_table" y  "\database\migrations\2021_03_01_132535_create_economic_sectors_companies_table" (ejemplos en carpeta) 
defina los campos para las tablas, donde se define tamb�en "timestamps" para fecha de creacion y modificaci�n y 
softDeletes para softDeletes

5.1-
en el archivo "\app\Models\EconomicSectors.php" defina los campos que se llenar�n y los campos en los que se hara logs, 
adem�s defina la funci�n "Economic_Sectors_Companies" (nombre seg�n modelo), y dentro de la funci�n haga HasMany al modelo, 
"Economic_Sectors_Companies" 
ejemplo

class EconomicSectors extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    
    protected $fillable = ['code','description'];
    
    protected static $logAttributes = ['code','description'];

    protected static $logName = 'EconomicSectors';

    protected static $logOnlyDirty = true;

    public function getDescriptionForEvent(string $eventName): string
    {
        return $eventName;
    }
    public function Economic_Sectors_Companies()
    {
        return $this->hasMany(EconomicSectorCompanies::class);
    }
    
}

5.1-
en el archivo "\app\Models\EconomicSectorCompanies.php" defina los campos que se llenar�n y los campos en los que se hara logs, 
adem�s defina la funci�n "Economic_Sectors" (nombre seg�n modelo), y dentro de la funci�n haga belongsTo al modelo, 
"Economic_Sectors" 
ejemplo

class EconomicSectorCompanies extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;

    
    protected $fillable = ['name'];
    
    protected static $logAttributes = ['name'];

    protected static $logName = 'EconomicSectorCompanies';

    protected static $logOnlyDirty = true;

    public function getDescriptionForEvent(string $eventName): string
    {
        return $eventName;
    }

    public function Economic_Sectors()
    {
        return $this->belongsTo(EconomicSectors::class);
    }
    
}


6- 
ejecute comando: php artisan migrate, para actualizar base de datos

7- 
abra el recurso "\app\nova\EconomicSectors.php", (ejemplo en carpeta) para definir c�mo se va a mostrar el recurso, 
la funcion "label" para nombre en plural y la funcion "singularLabel" para nombre en singular, de la l�nea 54 a 
la 66, funcion "fields" para definir los campos a mostrar.

7.1- 
abra el recurso "\app\nova\EconomicSectorCompanies.php", (ejemplo en carpeta) para definir c�mo se va a mostrar el recurso, 
la funcion "label" para nombre en plural y la funcion "singularLabel" para nombre en singular, de la l�nea 55 a 
la 66, funcion "fields" para definir los campos a mostrar, l�nea 64 para definir relaci�n con sectores econ�micos: 

BelongsTo::make('Sector Econ�mico','economic_sectors',\app\Nova\EconomicSectors::class)->searchable()

8- 
ejecute los siguientes comandos: 
php artisan nova:publish
php artisan view:clear
php artisan serve

asignese los permisos adecuados para ver el recurso
