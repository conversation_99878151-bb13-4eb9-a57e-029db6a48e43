# Authenticating requests

> **NOTE**: This section provides a high-level overview of authentication for the API Gateway. Each endpoint in the documentation will specify its required headers and parameters to ensure successful requests.

This API requires authentication for most endpoints. There are different authentication requirements depending on the endpoint type and security group.

## Base Authentication

All API requests must include an encrypted API key in the request headers:

```
APIKey: your-encrypted-api-key
```

## Security Groups

### In-App Endpoints

For endpoints used within the mobile application after user login, you must include:

**Required Headers:**

-   `APIKey`: Encrypted API key (always required)
-   `Authorization`: Bearer token obtained from login
-   `UserAuth`: Customer ID of the authenticated user
-   `DeviceAuth`: Unique identifier of the device making the request

### Onboarding Endpoints

For endpoints used during the customer onboarding process (before full registration), you must include:

**Required Headers:**

-   `APIKey`: Encrypted API key (always required)
-   `DuiAuth`: DUI (National ID) of the customer
-   `BiometryAuth`: Biometry token obtained from the biometry consultation endpoint
-   `DeviceAuth`: Unique identifier of the device making the request
