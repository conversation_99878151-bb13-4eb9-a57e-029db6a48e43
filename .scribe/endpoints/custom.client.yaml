- httpMethods:
      - GET
  uri: api/deviceslist
  metadata:
      groupName: Client
      title: "GET /api/deviceslist"
      description: "Fetches a list of devices associated with the customer account, including device identifiers and relevant metadata."
      authenticated: true
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
  responseFields:
      data:
          type: object
          description: "Device information."
      data.0.device_id:
          type: string
          description: "Unique identifier for the device."
      data.0.device_name:
          type: string
          description: "Name of the device."
      data.0.ultimate_longitud:
          type: integer
          description: "Last known longitude of the device."
      data.0.ultimate_latitud:
          type: integer
          description: "Last known latitude of the device."
      data.0.ultimate_conexion:
          type: string
          description: "Timestamp of the last device connection."
      data.0.online:
          type: integer
          description: "Device online status (1 for online, 0 for offline)."
      additional:
          type: object
          description: "Additional response metadata."
      additional.status:
          type: integer
          description: "HTTP status code of the response."
  responses:
      - status: 200
        description:
        content:
            data:
                device_id: "50feb8b9-7c41-383c-80e2-f12af5328da7"
                device_name: test-device
                ultimate_longitud: 78
                ultimate_latitud: 90
                ultimate_conexion: "2025-08-04T12:55:51.180Z"
                online: 1
            additional:
                status: 200

- httpMethods:
      - DELETE
  uri: api/devices/{device-id}
  metadata:
      groupName: Client
      title: "DELETE /api/devices/{id}"
      description: "Deletes the device specified by the device-id URL parameter if it exists."
      authenticated: true
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
  urlParameters:
      device-id:
          name: device-id
          description: Unique identifier for the device.
          required: true
          example: 50feb8b9-7c41-383c-80e2-f12af5328da7
          type: string
  responses:
      - status: 204

- httpMethods:
      - GET
  uri: api/profile
  metadata:
      groupName: Client
      title: "GET /api/profile"
      description: "Retrieves the profile and background images associated with the authenticated client."
      authenticated: true
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
  responseFields:
      customer_id:
          type: string
          description: "Unique identifier for the customer."
      creditcard_application_id:
          type: integer
          description: "ID of the customer's credit card application."
      nickname:
          type: string
          description: "Nickname of the customer."
      first_name:
          type: string
          description: "First name of the customer."
      second_name:
          type: string
          nullable: true
          description: "Second name of the customer, if any."
      first_surname:
          type: string
          description: "First surname of the customer."
      second_surname:
          type: string
          nullable: true
          description: "Second surname of the customer, if any."
      married_surname:
          type: string
          nullable: true
          description: "Married surname of the customer, if any."
      dui:
          type: string
          description: "DUI of the customer."
      nit:
          type: string
          description: "NIT of the customer."
      email:
          type: string
          description: "Email address of the customer."
      phone_number:
          type: string
          description: "Phone number of the customer."
      status:
          type: integer
          description: "HTTP status code of the response."
  responses:
      - status: 200
        description: "Successfully retrieves the profile information for the authenticated client."
        content:
            customer_id: "7332398988435033162"
            creditcard_application_id: 1582
            nickname: "Vo0xALiRxvhqveUv"
            first_name: "sed"
            second_name: null
            first_surname: "aut"
            second_surname: null
            married_surname: null
            dui: "00253852-0"
            nit: "00253852-0"
            email: "<EMAIL>"
            phone_number: "567314892"
            status: 200
