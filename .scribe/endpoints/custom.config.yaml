- httpMethods:
      - GET
  uri: api/maintenance-periods
  metadata:
      groupName: Configuration
      title: "GET /api/maintenance-periods"
      description: "Checks if system is under maintenance. Returns maintenance details if active, otherwise HTTP 204."
      authenticated: false
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
  responses:
      - status: 204
        description: Indicates that the services are currently available and not under maintenance.
      - status: 200
        content:
            message: Actualmente nos encontramos en mantenimiento. Por favor, vuelve a intentar más tarde.
        description: Returned when an ongoing maintenance period is detected. The 'message' field contains details about the maintenance.
  responseFields:
      message:
          type: string
          description: "Details about the maintenance period."

- httpMethods:
      - GET
  uri: api/maintenance-mode
  metadata:
      groupName: Configuration
      title: "GET /api/maintenance-mode"
      description: "Verifies if the application is in maintenance mode and returns appropriate status with encrypted API key."
      authenticated: false
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
  responses:
      - status: 200
        description: "Services are available and not under maintenance"
        content:
            result: "Ok"
            apikey: "encoded_api_key_string"
            status: 200
            code: 0
      - status: 503
        description: "System is currently under maintenance"
        content:
            result: "Actualmente nos encontramos en mantenimiento. Por favor, vuelve a intentar más tarde."
            apikey: "encoded_api_key_string"
            status: 503
            code: 1
      - status: 500
        description: "Internal server error"
        content:
            status: 500
            code: 5000
            message: "Server error message"
  responseFields:
      result:
          type: string
          description: "Result message indicating system status or maintenance message"
      apikey:
          type: string
          description: "Double-encoded API key for client authentication"
      status:
          type: integer
          description: "HTTP status code"
      code:
          type: integer
          description: "Application-specific code (0 for success, 1 for maintenance, 5000 for errors)"
      message:
          type: string
          description: "Error message (only present in error responses)"

- httpMethods:
      - GET
  uri: api/configuration
  metadata:
      groupName: Configuration
      title: "GET /api/configuration"
      description: "Retrieve specific configuration value by its code if allowed"
      authenticated: true
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
      DuiAuth: DUI of the customer.
      BiometryAuth: Biometry ID.
  queryParameters:
      code:
          name: code
          type: string
          description: "Configuration code to retrieve"
          required: true
          location: query
  responses:
      - status: 200
        description: "Successfully retrieved configuration (if exists and allowed)"
        content:
            result: "configuration_value_or_object"
            status: 200
      - status: 200
        description: "Configuration not found or not allowed"
        content:
            result: "No se encontró ninguna"
            status: 200
      - status: 500
        description: "Internal server error"
        content:
            status: 500
            code: 5000
            message: "Server error message"
      - status: 400
        description: "Bad request - database constraint violation"
        content:
            status: 400
            code: 5000
            message: "Database error message"
  responseFields:
      result:
          type: mixed
          description: "Configuration value (can be string, object, array) or error message if not found/allowed"
      status:
          type: integer
          description: "HTTP status code"
