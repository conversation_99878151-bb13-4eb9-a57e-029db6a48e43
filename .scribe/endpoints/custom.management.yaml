- httpMethods:
      - GET
  uri: api/benefits
  metadata:
      groupName: Management
      title: "GET /api/benefits"
      description: "Retrieves the list of benefits a customer can choose when opening their account."
      authenticated: true
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
      DuiAuth: DUI of the customer.
      BiometryAuth: Biometry ID.
  queryParameters:
      format:
          name: format
          type: string
          description:
              'Specifies the format in which the benefits should be returned.
              Value must be either "html" or "markdown", if not provided it will default to "html"'
          required: false
          example: "html"
  location: query
  responseFields:
      data:
          type: array
          description: "Array of benefit objects returned to the customer."
      data.0.title:
          type: string
          description: "Display name of the benefit shown to the customer."
      data.0.description:
          type: string
          description: "Detailed explanation of the benefit, returned in either HTML or Markdown format."
      data.0.example:
          type: string
          description: "Illustrative example demonstrating how the benefit can be used."
      data.0.main:
          type: boolean
          description: "Indicates if this benefit is the default or primary option for customers."
      data.0.order:
          type: integer
          description: "Specifies the sequence in which benefits are presented to the customer."
      data.0.longcode:
          type: string
          description: "Unique identifier code assigned to the benefit."
      data.0.icon:
          type: string
          description: "Reference to the icon (name or path) used to visually represent the benefit."
      data.0.storehouse_id:
          type: integer
          description: "Identifier linking the benefit to an associated image or resource."
  responses:
      - status: 200
        description: "Successfully retrieved benefits"
        content:
            data:
                - title: "Viajes"
                  description: "<div>Por cada dólar en compras te damos <strong>1.5 puntos</strong> para canjear <strong>por millas Lifemiles</strong>. Podés acumular hasta 15,000 puntos mensuales ¡Tus puntos nunca vencen!</div>"
                  example: "Ej: Si gastas $5,000 por mes recibes los 7,500 puntos"
                  main: false
                  order: 3
                  longcode: "VIAJES"
                  icon: "http://localhost:3000/api/imagery/1"
                  storehouse_id: 1
                - title: "Compras"
                  description: "<div>Por cada dólar en compras te damos <strong>3 puntos</strong>. Podés acumular hasta 30,000 puntos mensuales ¡Tus puntos nunca vencen!</div>"
                  example: "Ej: Si gastas $5,000 en el mes acumulas 15,000 puntos"
                  main: true
                  order: 1
                  longcode: "COMPRAS"
                  icon: "http://localhost:3000/api/imagery/2"
                  storehouse_id: 2
                - title: "Ahorro"
                  description: "<div>Por cada dólar en compras te regresamos <strong>$0.015 (1.5% de cashback)</strong> con un límite máximo de $150 mensuales en tu cuenta de ahorro</div>"
                  example: "Ej: Si en un mes tus compras suman $5,000 te devolvemos $75"
                  main: false
                  order: 2
                  longcode: "CASHBACK"
                  icon: "http://localhost:3000/api/imagery/3"
                  storehouse_id: 3

- httpMethods:
      - GET
  uri: api/parameterizables
  metadata:
      groupName: Management
      title: "GET /api/parameterizables"
      description: "Retrieve parameterizable configuration items filtered by group"
      authenticated: true
  headers:
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
      DuiAuth: DUI of the customer.
      BiometryAuth: Biometry ID.
  queryParameters:
      group:
          name: group
          type: string
          description: "Group name to filter parameterizables"
          required: true
          location: query
  responses:
      - status: 200
        description: "Successfully retrieved parameterizables"
        content:
            data:
                - code: "PARAM_001"
                  parameter_type: "string"
                  parameter: "example_value"
                  group: "general"
                  storehouse_id: 1
                - code: "PARAM_002"
                  parameter_type: "boolean"
                  parameter: "true"
                  group: "general"
                  storehouse_id: 1
            additional:
                status: 200
      - status: 500
        description: "Internal server error"
        content:
            status: 500
            code: 5000
            message: "Server error message"
      - status: 400
        description: "Bad request - database constraint violation"
        content:
            status: 400
            code: 5000
            message: "Database error message"
  responseFields:
      data:
          type: array
          description: "Array of parameterizable items"
          items:
      data.0.code:
          type: string
          description: "Unique parameter code"
      data.0.parameter_type:
          type: string
          description: "Type of the parameter (string, boolean, integer, etc.)"
      data.0.parameter:
          type: string
          description: "Parameter value"
      data.0.group:
          type: string
          description: "Group classification of the parameter"
      data.0.storehouse_id:
          type: integer
          description: "Associated storehouse identifier"
      additional:
          type: object
          description: "Additional response metadata"
      additional.status:
          type: integer
          description: "HTTP status code"

- httpMethods:
      - GET
  uri: api/surveys
  metadata:
      groupName: Management
      title: "GET /api/surveys"
      description:
          "Retrieves a single survey by its code using the 'name' query parameter. Returns the survey's ID,
          name, description, and questions. Use this endpoint to look up a specific survey a customer can participate in."
      authenticated: true
  headers:
      Accept: application/json
      Content-Type: application/json
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
      DuiAuth: DUI of the customer.
      BiometryAuth: Biometry ID.
  queryParameters:
      name:
          name: name
          description: "Survey code (e.g., PRPF) used to look up the survey by name."
          type: string
          required: true
          example: "PRPF"
  responseFields:
      data:
          type: object
          description: "Survey details including questions and options."
      data.id:
          type: integer
          description: "Unique identifier of the survey."
      data.name:
          type: string
          description: "Name of the survey."
      data.description:
          type: string
          description: "Survey description or prompt."
      data.questions:
          type: array
          description: "List of questions in the survey."
      data.questions.0.id:
          type: integer
          description: "Unique identifier of the question."
      data.questions.0.survey_id:
          type: integer
          description: "Identifier of the survey this question belongs to."
      data.questions.0.question:
          type: string
          description: "Text of the question."
      data.questions.0.type:
          type: string
          description: "Type of question (e.g., CTL For Cualitative, CNT For Cuantitative)."
      data.questions.0.options:
          type: array
          description: "Available options for the question."
      data.questions.0.options.0.id:
          type: integer
          description: "Unique identifier of the option."
      data.questions.0.options.0.question_id:
          type: integer
          description: "Identifier of the question this option belongs to."
      data.questions.0.options.0.value:
          type: string
          description: "Option value or text."
      data.questions.0.options.0.type:
          type: string
          description: "Type of option (e.g., OPTION_OPEN, OPTION_CLOSED)."
  responses:
      - status: 200
        description: "Successfully retrieved survey details and questions"
        content:
            data:
                id: 5
                name: "PRPF"
                description: "¿Con qué probabilidad recomendarías este proceso de solicitud a tus amigos o familiares?"
                questions:
                    - id: 1
                      survey_id: 5
                      type: "OPEN"
                      question: "Test"
                      options:
                          - id: 1
                            question_id: 1
                            value: "Test"
                            type: "OPEN"

- httpMethods:
      - GET
  uri: api/surveys/{id}
  metadata:
      groupName: Management
      title: "GET /api/surveys/{id}"
      description:
          "Fetches detailed information about a specific survey, including its questions and available options
          for each question. Useful for displaying survey structure and choices to customers."
      authenticated: true
  headers:
      Accept: application/json
      Content-Type: application/json
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
      DuiAuth: DUI of the customer.
      BiometryAuth: Biometry ID.
  urlParameters:
      id:
          name: id
          description: "Unique identifier of the survey to retrieve details for."
          type: integer
          required: true
  responseFields:
      data:
          type: object
          description: "Survey details including questions and options."
      data.id:
          type: integer
          description: "Unique identifier of the survey."
      data.name:
          type: string
          description: "Name of the survey."
      data.description:
          type: string
          description: "Survey description or prompt."
      data.questions:
          type: array
          description: "List of questions in the survey."
      data.questions.0.id:
          type: integer
          description: "Unique identifier of the question."
      data.questions.0.survey_id:
          type: integer
          description: "Identifier of the survey this question belongs to."
      data.questions.0.question:
          type: string
          description: "Text of the question."
      data.questions.0.type:
          type: string
          description: "Type of question (e.g., CTL For Cualitative, CNT For Cuantitative)."
      data.questions.0.options:
          type: array
          description: "Available options for the question."
      data.questions.0.options.0.id:
          type: integer
          description: "Unique identifier of the option."
      data.questions.0.options.0.question_id:
          type: integer
          description: "Identifier of the question this option belongs to."
      data.questions.0.options.0.value:
          type: string
          description: "Option value or text."
      data.questions.0.options.0.type:
          type: string
          description: "Type of option (e.g., OPTION_OPEN, OPTION_CLOSED)."
  responses:
      - status: 200
        description: "Successfully retrieved survey details and questions"
        content:
            data:
                id: 5
                name: "PRPF"
                description: "¿Con qué probabilidad recomendarías este proceso de solicitud a tus amigos o familiares?"
                questions:
                    - id: 1
                      survey_id: 5
                      type: "OPEN"
                      question: "Test"
                      options:
                          - id: 1
                            question_id: 1
                            value: "Test"
                            type: "OPEN"

- httpMethods:
      - POST
  uri: api/surveys/{id}/answers
  metadata:
      groupName: Management
      title: "POST /api/surveys/{id}/answers"
      description: "Submit and save customer responses for a specific survey identified by its ID."
      authenticated: true
  headers:
      Accept: application/json
      Content-Type: application/json
      APIKey: "Encrypted string used for authenticating requests to the API."
      Authorization: Bearer token
      UserAuth: Customer ID.
      DeviceAuth: Device ID.
      DuiAuth: DUI of the customer.
      BiometryAuth: Biometry ID.
  urlParameters:
      id:
          name: id
          description: "Unique identifier of the survey for which answers are being submitted."
          type: integer
          required: true
  bodyParameters:
      answers:
          name: answers
          type: array
          description: "List of answers submitted for the survey."
          required: true
          example:
              - optionId: 1
                other: null
              - optionId: 2
                other: "Some text"
      answers.0.optionId:
          name: optionId
          type: integer
          description: "ID referencing the selected option for a specific question."
          required: true
          example: 1
      answers.0.other:
          name: other
          type: string
          description: "Open answer provided by the user, if applicable."
          required: false
          example: "Some text"
      answers.1.optionId:
          name: optionId
          type: integer
          description: "ID referencing the selected option for a specific question."
          required: true
          example: 1
      answers.1.other:
          name: other
          type: string
          description: "Open answer provided by the user, if applicable."
          required: false
  responses:
      - status: 201
        description: "Survey answers successfully submitted"
        content: {}
      - status: 422
        description: "Validation error - missing or invalid answers"
        content:
            message: "El campo answers es obligatorio."
            errors:
                answers:
                    - "El campo answers es obligatorio."
