drop table if exists action_events
go

drop table if exists activity_log
go

drop table if exists advertisings
go

drop table if exists agencies
go

drop table if exists answers
go

drop table if exists apareances
go

drop table if exists api_keys_endpoints
go

drop table if exists api_keys
go

drop table if exists benefits
go

drop table if exists clients_notifications
go

drop table if exists clients_systems
go

drop table if exists color_theme_apps
go

drop table if exists data_notifications
go

drop table if exists devices
go

drop table if exists endpoints
go

drop table if exists endpoints_log
go

drop table if exists error_logs
go

drop table if exists external_services_monitor
go

drop table if exists faico_histories
go

drop table if exists failed_jobs
go

drop table if exists faqs
go

drop table if exists log_access
go

drop table if exists log_notifications
go

drop table if exists maintenance_modes
go

drop table if exists nickname_log
go

drop table if exists notification_variables
go

drop table if exists notifications
go

drop table if exists notifications_histories
go

drop table if exists notifications_templates
go

drop table if exists nova_settings
go

drop table if exists options
go

drop table if exists otp_log
go

drop table if exists parameterizables
go

drop table if exists password__histories
go

drop table if exists password_resets
go

drop table if exists perfil_images
go

drop table if exists clients
go

drop table if exists question
go

drop table if exists register_clients_app
go

drop table if exists role_permission
go

drop table if exists role_user
go

drop table if exists roles
go

drop table if exists services_router
go

drop table if exists sessions
go

drop table if exists slider_benefits_details
go

drop table if exists slider_benefits
go

drop table if exists storehouses
go

drop table if exists subject_emails
go

drop table if exists survey
go

drop table if exists systems
go

drop table if exists users
go

drop table if exists userschats
go

drop table if exists videos
go

drop table if exists otp_black_list
go

