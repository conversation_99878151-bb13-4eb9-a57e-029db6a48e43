/****** Object:  Table [dbo].[action_events]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[action_events](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [batch_id] [nchar](36) NOT NULL,
    [user_id] [bigint] NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [actionable_type] [nvarchar](255) NOT NULL,
    [actionable_id] [bigint] NOT NULL,
    [target_type] [nvarchar](255) NOT NULL,
    [target_id] [bigint] NOT NULL,
    [model_type] [nvarchar](255) NOT NULL,
    [model_id] [bigint] NULL,
    [fields] [nvarchar](max) NOT NULL,
    [status] [nvarchar](25) NOT NULL,
    [exception] [nvarchar](max) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [original] [nvarchar](max) NULL,
    [changes] [nvarchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[activity_log]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[activity_log](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [log_name] [nvarchar](255) NULL,
    [description] [nvarchar](max) NOT NULL,
    [subject_type] [nvarchar](255) NULL,
    [subject_id] [bigint] NULL,
    [causer_type] [nvarchar](255) NULL,
    [causer_id] [bigint] NULL,
    [properties] [nvarchar](max) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[advertisings]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[advertisings](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [body] [nvarchar](255) NOT NULL,
    [color] [nvarchar](255) NULL,
    [url] [nvarchar](255) NOT NULL,
    [image] [nvarchar](255) NOT NULL,
    [active] [bit] NOT NULL,
    [group] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [storehouse_id] [bigint] NULL,
    [start_date] [date] NULL,
    [finish_date] [date] NULL,
    [active_url] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[agencies]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[agencies](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [latitude] [nvarchar](255) NOT NULL,
    [longitude] [nvarchar](255) NOT NULL,
    [telephone] [nvarchar](255) NOT NULL,
    [email] [nvarchar](255) NOT NULL,
    [address] [nvarchar](255) NOT NULL,
    [departament_id] [int] NOT NULL,
    [municipality_id] [int] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [type] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[answers]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[answers](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [option_id] [int] NOT NULL,
    [client_id] [int] NOT NULL,
    [other_answers] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[apareances]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[apareances](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [primary_button_text] [nvarchar](255) NOT NULL,
    [text_link] [nvarchar](255) NOT NULL,
    [bar_text_menu] [nvarchar](255) NOT NULL,
    [border_link_bar] [nvarchar](255) NOT NULL,
    [alert_background] [nvarchar](255) NOT NULL,
    [success_background] [nvarchar](255) NOT NULL,
    [warning_background] [nvarchar](255) NOT NULL,
    [info_background] [nvarchar](255) NOT NULL,
    [table_footer] [nvarchar](255) NOT NULL,
    [table_head] [nvarchar](255) NOT NULL,
    [background] [nvarchar](255) NOT NULL,
    [table_border] [nvarchar](255) NOT NULL,
    [table_link_button] [nvarchar](255) NOT NULL,
    [icons] [nvarchar](255) NOT NULL,
    [brigth_icons] [nvarchar](255) NOT NULL,
    [logo_backgorund] [nvarchar](255) NOT NULL,
    [gradient_menu_top] [nvarchar](255) NOT NULL,
    [gradient_menu_bottom] [nvarchar](255) NOT NULL,
    [status] [bit] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[api_keys]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[api_keys](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [apikey] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [syncup] [bit] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[api_keys_endpoints]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[api_keys_endpoints](
    [api_keys_id] [bigint] NOT NULL,
    [endpoints_id] [bigint] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
 CONSTRAINT [api_keys_endpoints_api_keys_id_endpoints_id_primary] PRIMARY KEY CLUSTERED 
(
    [api_keys_id] ASC,
    [endpoints_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[app_clients]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[app_clients](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [email] [nvarchar](255) NOT NULL,
    [nickname] [nvarchar](32) NOT NULL,
    [password] [nvarchar](255) NOT NULL,
    [device_id] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[benefits]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[benefits](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [title] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [example] [nvarchar](255) NOT NULL,
    [icon] [nvarchar](255) NOT NULL,
    [main] [bit] NOT NULL,
    [order] [nvarchar](255) NOT NULL,
    [longcode] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [storehouse_id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[client_addresses]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[client_addresses](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [clients_id] [int] NOT NULL,
    [location_latitude] [nvarchar](255) NOT NULL,
    [location_longitude] [nvarchar](255) NOT NULL,
    [label] [nvarchar](255) NOT NULL,
    [complete_address] [nvarchar](255) NOT NULL,
    [departament_id] [int] NOT NULL,
    [municipality_id] [int] NOT NULL,
    [street_avenue] [nvarchar](255) NOT NULL,
    [house_apartament] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[clients]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[clients](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [mode] [nvarchar](255) NOT NULL,
    [client_id] [nvarchar](255) NOT NULL,
    [first_name] [nvarchar](255) NOT NULL,
    [second_name] [nvarchar](255) NULL,
    [first_surname] [nvarchar](255) NOT NULL,
    [second_surname] [nvarchar](255) NULL,
    [married_surname] [nvarchar](255) NULL,
    [dui] [nvarchar](10) NOT NULL,
    [nit] [nvarchar](17) NOT NULL,
    [email] [nvarchar](255) NOT NULL,
    [phone_number] [nvarchar](9) NOT NULL,
    [password] [nvarchar](500) NOT NULL,
    [nickname] [nvarchar](255) NULL,
    [credolab_id] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [group] [nvarchar](50) NULL,
    [send_notification] [int] NULL,
    [creditcard_application_id] [int] NULL,
    [status] [smallint] NOT NULL,
 CONSTRAINT [PK__clients__3213E83FCBA22BF6] PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[clients_notifications]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[clients_notifications](
    [notifications_id] [bigint] NOT NULL,
    [clients_id] [bigint] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
 CONSTRAINT [clients_notifications_notifications_id_clients_id_primary] PRIMARY KEY CLUSTERED 
(
    [notifications_id] ASC,
    [clients_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[clients_systems]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[clients_systems](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [client_id] [bigint] NOT NULL,
    [system_id] [bigint] NOT NULL,
    [deleted_at] [datetime] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[color_theme_apps]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[color_theme_apps](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [notifications_bar] [nvarchar](255) NOT NULL,
    [bar] [nvarchar](255) NOT NULL,
    [principal_background] [nvarchar](255) NOT NULL,
    [secondary_background] [nvarchar](255) NOT NULL,
    [primary_text] [nvarchar](255) NOT NULL,
    [md_button] [nvarchar](255) NOT NULL,
    [md_button_text] [nvarchar](255) NOT NULL,
    [md_button_border] [nvarchar](255) NOT NULL,
    [secondary_text] [nvarchar](255) NOT NULL,
    [accent] [nvarchar](255) NOT NULL,
    [primary_title_text] [nvarchar](255) NOT NULL,
    [navigation_text] [nvarchar](255) NOT NULL,
    [subtitle_text] [nvarchar](255) NOT NULL,
    [description_text] [nvarchar](255) NOT NULL,
    [secondary_color_button] [nvarchar](255) NOT NULL,
    [secondary_text_color_button] [nvarchar](255) NOT NULL,
    [secondary_border_color_button] [nvarchar](255) NOT NULL,
    [primary_color_button] [nvarchar](255) NOT NULL,
    [primary_text_color_button] [nvarchar](255) NOT NULL,
    [primary_border_color_button] [nvarchar](255) NOT NULL,
    [error_color] [nvarchar](255) NOT NULL,
    [place_holder] [nvarchar](255) NOT NULL,
    [progress_bar] [nvarchar](255) NOT NULL,
    [background_progress_bar] [nvarchar](255) NOT NULL,
    [control] [nvarchar](255) NOT NULL,
    [label_tag] [nvarchar](255) NOT NULL,
    [chat_background_color] [nvarchar](255) NOT NULL,
    [chat_icon_color] [nvarchar](255) NOT NULL,
    [chat_border_color] [nvarchar](255) NOT NULL,
    [button_disabled_background_color] [nvarchar](255) NOT NULL,
    [button_disabled_border_color] [nvarchar](255) NOT NULL,
    [button_disabled_text_color] [nvarchar](255) NOT NULL,
    [textbox_border_radius] [nvarchar](255) NOT NULL,
    [button_stepper_background_color] [nvarchar](255) NOT NULL,
    [button_stepper_border_color] [nvarchar](255) NOT NULL,
    [button_stepper_text_color] [nvarchar](255) NOT NULL,
    [button_list_background_color] [nvarchar](255) NOT NULL,
    [button_list_border_color] [nvarchar](255) NOT NULL,
    [button_list_text_color] [nvarchar](255) NOT NULL,
    [secondary_background_color_button] [nvarchar](255) NOT NULL,
    [textbox_color_state] [nvarchar](255) NOT NULL,
    [textbox_color_empty_state] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[data_notifications]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[data_notifications](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [notifications_id] [bigint] NOT NULL,
    [type] [nvarchar](255) NOT NULL,
    [title] [nvarchar](255) NOT NULL,
    [content] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [image] [nvarchar](255) NULL,
 CONSTRAINT [PK__data_not__3213E83FBA6DEAF4] PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[departaments]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[departaments](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[devices]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[devices](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [client_id] [bigint] NOT NULL,
    [active] [bit] NOT NULL,
    [device_id] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [device_name] [nvarchar](255) NULL,
    [firebase_token] [nvarchar](255) NULL,
    [ultimate_longitud] [nvarchar](128) NULL,
    [ultimate_latitud] [nvarchar](128) NULL,
    [online] [int] NULL,
    [biometric_public_key] [nvarchar](max) NULL,
    [verification_challenge] [nvarchar](max) NULL,
    [huawei] [bit] NULL,
    [latest_activity] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[economic_sectors]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[economic_sectors](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [code] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[economic_sectors_companies]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[economic_sectors_companies](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [economic__sectors_id] [bigint] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[endpoints]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[endpoints](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [uri] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[endpoints_log]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[endpoints_log](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [endpoint_name] [nvarchar](255) NULL,
    [status_response] [nvarchar](255) NOT NULL,
    [system_code] [nvarchar](255) NULL,
    [result] [nvarchar](512) NULL,
    [ip] [nvarchar](255) NULL,
    [deleted_at] [datetime] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[error_logs]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[error_logs](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [message] [nvarchar](255) NOT NULL,
    [deleted_at] [datetime] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[external_services_monitor]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[external_services_monitor](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [url] [nvarchar](255) NOT NULL,
    [active] [nvarchar](255) NULL,
    [last_run] [nvarchar](255) NULL,
    [deleted_at] [datetime] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[faico_histories]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[faico_histories](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [credolab_id] [nvarchar](255) NOT NULL,
    [solicitude_faico_id] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[failed_jobs]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[failed_jobs](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [uuid] [nvarchar](255) NOT NULL,
    [connection] [nvarchar](max) NOT NULL,
    [queue] [nvarchar](max) NOT NULL,
    [payload] [nvarchar](max) NOT NULL,
    [exception] [nvarchar](max) NOT NULL,
    [failed_at] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[faqs]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[faqs](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [question] [nvarchar](255) NOT NULL,
    [answer] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[institute_peps]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[institute_peps](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[job_titles]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[job_titles](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[log_access]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[log_access](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [login_date_time] [datetime] NOT NULL,
    [logout_date_time] [datetime] NULL,
    [user_id] [int] NOT NULL,
    [ip] [nvarchar](255) NOT NULL,
    [user_agent] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[log_notifications]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[log_notifications](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [ccapplication_id] [nvarchar](255) NOT NULL,
    [token] [nvarchar](255) NOT NULL,
    [huawei] [nvarchar](255) NOT NULL,
    [status] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[maintenance_modes]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[maintenance_modes](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [date_time_maintenance_start] [datetime] NOT NULL,
    [date_time_maintenance_end] [datetime] NOT NULL,
    [reason] [nvarchar](255) NOT NULL,
    [client_message] [nvarchar](255) NOT NULL,
    [user_id] [int] NOT NULL,
    [user_approval_id] [int] NULL,
    [previous_block] [int] NOT NULL,
    [status] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[municipalities]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[municipalities](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [departament_id] [bigint] NOT NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[nickname_log]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[nickname_log](
    [key] [nvarchar](1024) NULL,
    [client_id] [int] NULL,
    [created_at] [datetime] NOT NULL,
    [updated_at] [datetime] NOT NULL,
    [deleted_at] [datetime] NULL,
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [faceauthentication] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[notification_variables]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[notification_variables](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [variable] [nvarchar](255) NOT NULL,
    [notifications_templates_id] [bigint] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [type] [nchar](20) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[notifications]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[notifications](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [title] [nvarchar](32) NOT NULL,
    [content] [nvarchar](255) NOT NULL,
    [transmitter_id] [int] NULL,
    [user_approval_id] [int] NULL,
    [job] [datetime2](6) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [sent] [nvarchar](255) NULL,
    [status] [bit] NULL,
    [group] [nvarchar](50) NULL,
    [parameter_type] [nvarchar](50) NULL,
    [time] [time](7) NULL,
    [week_day] [smallint] NULL,
    [active] [bit] NULL,
    [job_type] [smallint] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[notifications_histories]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[notifications_histories](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [client_id] [bigint] NOT NULL,
    [type] [nvarchar](255) NOT NULL,
    [message] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[notifications_templates]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[notifications_templates](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [title] [nvarchar](255) NOT NULL,
    [content] [nvarchar](max) NOT NULL,
    [status] [bit] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[nova_settings]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[nova_settings](
    [key] [nvarchar](255) NOT NULL,
    [value] [varchar](max) NULL,
 CONSTRAINT [nova_settings_key_primary] PRIMARY KEY CLUSTERED 
(
    [key] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[options]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[options](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [question_id] [int] NOT NULL,
    [value] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [type] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[otp_log]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[otp_log](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [sharedkey] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [identifier] [nvarchar](255) NULL,
    [type] [nvarchar](255) NULL,
    [value] [nvarchar](255) NULL,
    [otpcode] [nvarchar](255) NULL,
    [verified] [int] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[parameterizables]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[parameterizables](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [code] [nvarchar](255) NOT NULL,
    [parameter_type] [nvarchar](255) NOT NULL,
    [parameter] [nvarchar](255) NOT NULL,
    [group] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [storehouse_id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[password__histories]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[password__histories](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [user_id] [int] NOT NULL,
    [password] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[password_resets]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[password_resets](
    [email] [nvarchar](255) NOT NULL,
    [token] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[perfil_images]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[perfil_images](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [client_id] [nvarchar](255) NOT NULL,
    [image] [nvarchar](255) NOT NULL,
    [type] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [storehouse_id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[question]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[question](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [survey_id] [int] NOT NULL,
    [type] [nvarchar](255) NOT NULL,
    [question] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[register_clients_app]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[register_clients_app](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [endpoint_name] [nvarchar](255) NULL,
    [status_response] [nvarchar](255) NOT NULL,
    [client_id] [nvarchar](255) NULL,
    [device_id] [nvarchar](255) NULL,
    [result] [nvarchar](512) NULL,
    [ip] [nvarchar](255) NULL,
    [deleted_at] [datetime] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[role_permission]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[role_permission](
    [role_id] [bigint] NOT NULL,
    [permission_slug] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
 CONSTRAINT [role_permission_role_id_permission_slug_primary] PRIMARY KEY CLUSTERED 
(
    [role_id] ASC,
    [permission_slug] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[role_user]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[role_user](
    [role_id] [bigint] NOT NULL,
    [user_id] [bigint] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
 CONSTRAINT [role_user_role_id_user_id_primary] PRIMARY KEY CLUSTERED 
(
    [role_id] ASC,
    [user_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[roles]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[roles](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [slug] [nvarchar](255) NOT NULL,
    [name] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[services_router]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[services_router](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [service_name] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NULL,
    [exposed_service_url] [nvarchar](255) NOT NULL,
    [third_parties_url] [nvarchar](255) NOT NULL,
    [group] [nvarchar](255) NULL,
    [has_token] [bit] NOT NULL,
    [token] [nvarchar](255) NULL,
    [method] [nvarchar](255) NOT NULL,
    [deleted_at] [datetime] NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [security] [nchar](10) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sessions]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sessions](
    [id] [nvarchar](255) NOT NULL,
    [user_id] [bigint] NULL,
    [ip_address] [nvarchar](45) NULL,
    [user_agent] [nvarchar](max) NULL,
    [payload] [nvarchar](max) NOT NULL,
    [last_activity] [int] NOT NULL,
 CONSTRAINT [sessions_id_primary] PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[slider_benefits]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[slider_benefits](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [title] [nvarchar](255) NOT NULL,
    [image] [nvarchar](255) NOT NULL,
    [button_text] [nvarchar](255) NOT NULL,
    [button_parameter] [nvarchar](255) NOT NULL,
    [active] [bit] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [storehouse_id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[slider_benefits_details]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[slider_benefits_details](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [title] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [slider_benefits_id] [int] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[storehouses]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[storehouses](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [data] [nvarchar](max) NOT NULL,
    [extension] [nvarchar](20) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
 CONSTRAINT [PK_storehouses] PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[subject_emails]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[subject_emails](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [template_code] [nvarchar](100) NOT NULL,
    [subject] [nvarchar](250) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[survey]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[survey](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[systems]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[systems](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [system_code] [nvarchar](255) NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [email] [nvarchar](255) NOT NULL,
    [email_verified_at] [datetime] NULL,
    [remember_token] [nvarchar](100) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
    [user_type] [nvarchar](255) NULL,
    [organizational_unit] [nvarchar](255) NULL,
    [password] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[userschats]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[userschats](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [client_app_id] [int] NULL,
    [chat_id] [nvarchar](255) NULL,
    [phone] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [dui] [nvarchar](10) NULL,
    [name] [nvarchar](255) NULL,
    [lastname] [nvarchar](255) NULL,
    [email] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[videos]    Script Date: 1/12/2023 2:39:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[videos](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [name] [nvarchar](255) NOT NULL,
    [description] [nvarchar](255) NOT NULL,
    [parameter_type] [nvarchar](255) NOT NULL,
    [video] [nvarchar](255) NULL,
    [video_youtube] [nvarchar](255) NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
    [deleted_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[otp_black_list]    Script Date: 17/04/2024 09:45:00 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[otp_black_list](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [emitter] [nchar](150) NOT NULL,
    [block_time_start] [datetime] NOT NULL,
    [block_time_end] [datetime] NOT NULL,
    [created_at] [datetime] NULL,
    [updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[action_events] ADD  DEFAULT ('running') FOR [status]
GO
ALTER TABLE [dbo].[clients] ADD  DEFAULT ((1)) FOR [status]
GO
ALTER TABLE [dbo].[failed_jobs] ADD  DEFAULT (getdate()) FOR [failed_at]
GO
ALTER TABLE [dbo].[advertisings]  WITH NOCHECK ADD  CONSTRAINT [advertisings_storehouse_id_foreign] FOREIGN KEY([storehouse_id])
REFERENCES [dbo].[storehouses] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[advertisings] CHECK CONSTRAINT [advertisings_storehouse_id_foreign]
GO
ALTER TABLE [dbo].[api_keys_endpoints]  WITH NOCHECK ADD  CONSTRAINT [api_keys_endpoints_api_keys_id_foreign] FOREIGN KEY([api_keys_id])
REFERENCES [dbo].[api_keys] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[api_keys_endpoints] CHECK CONSTRAINT [api_keys_endpoints_api_keys_id_foreign]
GO
ALTER TABLE [dbo].[api_keys_endpoints]  WITH NOCHECK ADD  CONSTRAINT [api_keys_endpoints_endpoints_id_foreign] FOREIGN KEY([endpoints_id])
REFERENCES [dbo].[endpoints] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[api_keys_endpoints] CHECK CONSTRAINT [api_keys_endpoints_endpoints_id_foreign]
GO
ALTER TABLE [dbo].[benefits]  WITH NOCHECK ADD  CONSTRAINT [FK_benefits_storehouses] FOREIGN KEY([storehouse_id])
REFERENCES [dbo].[storehouses] ([id])
GO
ALTER TABLE [dbo].[benefits] CHECK CONSTRAINT [FK_benefits_storehouses]
GO
ALTER TABLE [dbo].[clients_notifications]  WITH NOCHECK ADD  CONSTRAINT [clients_notifications_clients_id_foreign] FOREIGN KEY([clients_id])
REFERENCES [dbo].[clients] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[clients_notifications] CHECK CONSTRAINT [clients_notifications_clients_id_foreign]
GO
ALTER TABLE [dbo].[clients_notifications]  WITH NOCHECK ADD  CONSTRAINT [clients_notifications_notifications_id_foreign] FOREIGN KEY([notifications_id])
REFERENCES [dbo].[notifications] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[clients_notifications] CHECK CONSTRAINT [clients_notifications_notifications_id_foreign]
GO
ALTER TABLE [dbo].[clients_systems]  WITH NOCHECK ADD  CONSTRAINT [clients_systems_client_id_foreign] FOREIGN KEY([client_id])
REFERENCES [dbo].[clients] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[clients_systems] CHECK CONSTRAINT [clients_systems_client_id_foreign]
GO
ALTER TABLE [dbo].[clients_systems]  WITH NOCHECK ADD  CONSTRAINT [clients_systems_system_id_foreign] FOREIGN KEY([system_id])
REFERENCES [dbo].[systems] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[clients_systems] CHECK CONSTRAINT [clients_systems_system_id_foreign]
GO
ALTER TABLE [dbo].[data_notifications]  WITH NOCHECK ADD  CONSTRAINT [data_notifications_notifications_id_foreign] FOREIGN KEY([notifications_id])
REFERENCES [dbo].[notifications] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[data_notifications] CHECK CONSTRAINT [data_notifications_notifications_id_foreign]
GO
ALTER TABLE [dbo].[devices]  WITH NOCHECK ADD  CONSTRAINT [devices_client_id_foreign] FOREIGN KEY([client_id])
REFERENCES [dbo].[clients] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[devices] CHECK CONSTRAINT [devices_client_id_foreign]
GO
ALTER TABLE [dbo].[notification_variables]  WITH NOCHECK ADD  CONSTRAINT [notification_variables_notifications_templates_id_foreign] FOREIGN KEY([notifications_templates_id])
REFERENCES [dbo].[notifications_templates] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[notification_variables] CHECK CONSTRAINT [notification_variables_notifications_templates_id_foreign]
GO
ALTER TABLE [dbo].[notifications_histories]  WITH NOCHECK ADD  CONSTRAINT [notifications_histories_client_id_foreign] FOREIGN KEY([client_id])
REFERENCES [dbo].[clients] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[notifications_histories] CHECK CONSTRAINT [notifications_histories_client_id_foreign]
GO
ALTER TABLE [dbo].[parameterizables]  WITH NOCHECK ADD  CONSTRAINT [parameterizables_storehouse_id_foreign] FOREIGN KEY([storehouse_id])
REFERENCES [dbo].[storehouses] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[parameterizables] CHECK CONSTRAINT [parameterizables_storehouse_id_foreign]
GO
ALTER TABLE [dbo].[role_permission]  WITH NOCHECK ADD  CONSTRAINT [role_permission_role_id_foreign] FOREIGN KEY([role_id])
REFERENCES [dbo].[roles] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[role_permission] CHECK CONSTRAINT [role_permission_role_id_foreign]
GO
ALTER TABLE [dbo].[role_user]  WITH NOCHECK ADD  CONSTRAINT [role_user_role_id_foreign] FOREIGN KEY([role_id])
REFERENCES [dbo].[roles] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[role_user] CHECK CONSTRAINT [role_user_role_id_foreign]
GO
ALTER TABLE [dbo].[role_user]  WITH NOCHECK ADD  CONSTRAINT [role_user_user_id_foreign] FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[role_user] CHECK CONSTRAINT [role_user_user_id_foreign]
GO
ALTER TABLE [dbo].[slider_benefits]  WITH NOCHECK ADD  CONSTRAINT [FK_slider_benefits_storehouses] FOREIGN KEY([storehouse_id])
REFERENCES [dbo].[storehouses] ([id])
GO
ALTER TABLE [dbo].[slider_benefits] CHECK CONSTRAINT [FK_slider_benefits_storehouses]
GO

ALTER TABLE dbo.action_events WITH NOCHECK ADD CONSTRAINT action_events_FK FOREIGN KEY (user_id) REFERENCES dbo.users(id)

GO
CREATE INDEX activity_log_causer_id_IDX ON dbo.activity_log (causer_id, causer_type)

GO
CREATE INDEX activity_log_subject_type_IDX ON dbo.activity_log (subject_type, subject_id)

GO
CREATE INDEX advertisings_group_IDX ON dbo.advertisings ([group])

GO
CREATE INDEX advertisings_start_date_IDX ON dbo.advertisings (start_date, finish_date)

GO
CREATE INDEX agencies_type_IDX ON dbo.agencies ([type])

GO
ALTER TABLE dbo.clients_systems ADD CONSTRAINT clients_systems_FK FOREIGN KEY (client_id) REFERENCES dbo.clients(id)

GO
CREATE INDEX devices_active_IDX ON dbo.devices (active)

GO
CREATE INDEX devices_device_name_IDX ON dbo.devices (device_name)

GO
CREATE INDEX devices_firebase_token_IDX ON dbo.devices (firebase_token)

GO
CREATE INDEX devices_online_IDX ON dbo.devices (online)

GO
CREATE INDEX devices_huawei_IDX ON dbo.devices (huawei)

GO
CREATE INDEX devices_device_id_IDX ON dbo.devices (device_id)

GO
CREATE UNIQUE INDEX services_router_service_name_IDX ON dbo.services_router (service_name)

GO
CREATE INDEX services_router_exposed_service_url_IDX ON dbo.services_router (exposed_service_url)

GO
ALTER TABLE dbo.password__histories ALTER COLUMN user_id bigint NOT NULL

GO
ALTER TABLE dbo.password__histories WITH NOCHECK ADD CONSTRAINT password__histories_FK FOREIGN KEY (user_id) REFERENCES dbo.clients(id)

GO
CREATE INDEX endpoints_uri_IDX ON dbo.endpoints (uri)

GO
CREATE INDEX api_keys_apikey_IDX ON dbo.api_keys (apikey)

GO
CREATE UNIQUE INDEX nova_settings_key_IDX ON dbo.nova_settings ([key])

GO
ALTER TABLE dbo.perfil_images ALTER COLUMN client_id bigint NOT NULL

GO
ALTER TABLE dbo.perfil_images WITH NOCHECK ADD CONSTRAINT perfil_images_FK FOREIGN KEY (client_id) REFERENCES dbo.clients(id) ON DELETE CASCADE

GO
CREATE INDEX perfil_images_type_IDX ON dbo.perfil_images ([type]);

GO
ALTER TABLE dbo.municipalities ADD CONSTRAINT municipalities_FK FOREIGN KEY (departament_id) REFERENCES dbo.departaments(id) ON DELETE CASCADE

GO
ALTER TABLE dbo.nickname_log ALTER COLUMN client_id bigint NULL

GO
ALTER TABLE dbo.nickname_log WITH NOCHECK ADD CONSTRAINT nickname_log_FK FOREIGN KEY (client_id) REFERENCES dbo.clients(id) ON DELETE CASCADE

GO
CREATE INDEX nickname_log_faceauthentication_IDX ON dbo.nickname_log (faceauthentication)

GO
ALTER TABLE dbo.client_addresses ALTER COLUMN clients_id bigint NOT NULL

GO
ALTER TABLE dbo.client_addresses ADD CONSTRAINT client_addresses_FK FOREIGN KEY (clients_id) REFERENCES dbo.clients(id) ON DELETE CASCADE

GO
CREATE INDEX otp_log_type_IDX ON dbo.otp_log ([type])

GO
CREATE INDEX otp_log_identifier_IDX ON dbo.otp_log (identifier)

GO
CREATE INDEX otp_log_value_IDX ON dbo.otp_log (value)

GO
CREATE INDEX otp_log_verified_IDX ON dbo.otp_log (verified)

GO
CREATE INDEX otp_log_otpcode_IDX ON dbo.otp_log (otpcode)

GO
CREATE INDEX parameterizables_group_IDX ON dbo.parameterizables ([group])

GO
ALTER TABLE dbo.slider_benefits_details ALTER COLUMN slider_benefits_id bigint NOT NULL

GO
ALTER TABLE dbo.slider_benefits_details ADD CONSTRAINT slider_benefits_details_FK FOREIGN KEY (slider_benefits_id) REFERENCES dbo.slider_benefits(id) ON DELETE CASCADE

GO
ALTER TABLE dbo.[options] ALTER COLUMN question_id bigint NOT NULL

GO
ALTER TABLE dbo.[options] WITH NOCHECK ADD CONSTRAINT options_FK FOREIGN KEY (question_id) REFERENCES dbo.question(id) ON DELETE CASCADE;

GO
CREATE INDEX options_type_IDX ON dbo.[options] ([type])

GO
ALTER TABLE dbo.question ALTER COLUMN survey_id bigint NOT NULL

GO
ALTER TABLE dbo.question WITH NOCHECK ADD CONSTRAINT question_FK FOREIGN KEY (survey_id) REFERENCES dbo.survey(id) ON DELETE CASCADE

GO
CREATE INDEX survey_name_IDX ON dbo.survey (name)

GO
ALTER TABLE dbo.notifications ALTER COLUMN transmitter_id bigint NULL

GO
ALTER TABLE dbo.notifications ALTER COLUMN user_approval_id bigint NULL

GO
ALTER TABLE dbo.notifications WITH NOCHECK ADD CONSTRAINT notifications_FK FOREIGN KEY (transmitter_id) REFERENCES dbo.users(id)

GO
ALTER TABLE dbo.notifications WITH NOCHECK ADD CONSTRAINT notifications_FK_user_approve FOREIGN KEY (user_approval_id) REFERENCES dbo.users(id)

GO
ALTER TABLE dbo.answers ALTER COLUMN client_id bigint NOT NULL

GO
ALTER TABLE dbo.answers ALTER COLUMN option_id bigint NOT NULL

GO
ALTER TABLE dbo.answers WITH NOCHECK ADD CONSTRAINT answers_client_FK FOREIGN KEY (client_id) REFERENCES dbo.clients(id) ON DELETE CASCADE

GO
ALTER TABLE dbo.answers WITH NOCHECK ADD CONSTRAINT answers_options_FK FOREIGN KEY (option_id) REFERENCES dbo.[options](id) ON DELETE CASCADE

GO
CREATE INDEX faico_histories_solicitude_faico_id_IDX ON dbo.faico_histories (solicitude_faico_id, credolab_id)

GO
CREATE INDEX log_notifications_ccapplication_id_IDX ON dbo.log_notifications (ccapplication_id)

GO
CREATE INDEX log_notifications_status_IDX ON dbo.log_notifications (status)

GO
ALTER TABLE dbo.log_access ALTER COLUMN user_id bigint NOT NULL

GO
ALTER TABLE dbo.log_access WITH NOCHECK ADD CONSTRAINT log_access_FK FOREIGN KEY (user_id) REFERENCES dbo.users(id)

GO
ALTER TABLE dbo.register_clients_app ALTER COLUMN client_id bigint NULL

GO
CREATE INDEX register_clients_app_client_id_IDX ON dbo.register_clients_app (client_id)

GO
CREATE INDEX register_clients_app_device_id_IDX ON dbo.register_clients_app (device_id)

GO
CREATE INDEX register_clients_app_endpoint_name_IDX ON dbo.register_clients_app (endpoint_name)

GO
CREATE INDEX register_clients_app_status_response_IDX ON dbo.register_clients_app (status_response)

GO
ALTER TABLE dbo.userschats ALTER COLUMN client_app_id bigint NULL

GO
ALTER TABLE dbo.maintenance_modes ALTER COLUMN user_id bigint NOT NULL

GO
ALTER TABLE dbo.maintenance_modes ALTER COLUMN user_approval_id bigint NULL

GO
ALTER TABLE dbo.maintenance_modes ADD CONSTRAINT maintenance_modes_FK FOREIGN KEY (user_id) REFERENCES dbo.users(id)

GO
ALTER TABLE dbo.maintenance_modes ADD CONSTRAINT maintenance_modes_FK_1 FOREIGN KEY (user_approval_id) REFERENCES dbo.users(id)


GO
DROP TABLE dbo.app_clients

GO
DROP TABLE dbo.municipalities

GO
DROP TABLE dbo.departaments

GO
DROP TABLE dbo.economic_sectors_companies

GO
DROP TABLE dbo.economic_sectors

GO
DROP TABLE dbo.job_titles

GO
DROP TABLE dbo.institute_peps

GO
DROP TABLE dbo.client_addresses

GO
