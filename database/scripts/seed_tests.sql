INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'AD_DC', N'dc=atlantida,dc=com, dc=sv')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'AD_OU', N'Usuarios de Pruebas')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_APP', N'Administración APP:')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_COMERCIAL', N'Administración Comercial')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_DE_ENDPOINTS', N'Administración de endpoints')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_DE_VÍDEOS', N'Administración de vídeos')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_IMAGENES_DE_PROMOCIONES', N'Administración Imágenes de promociones')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_PARA_ENVIO_NOTIFICACIONES_PUSH', N'Administración para envio Notificaciones Push')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRACIÓN_SISTEMAS', N'Administración Sistemas:')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ADMINISTRCIÓN_DE_API_KEYS', N'Administración de api Keys')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'APARIENCIA_APLICACION', N'Apariencia de aplicación')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'API_KEY', N'4a3c573098e112afebc389473aacc202')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ASUNTOS_EMAIL', N'Asuntos de Email')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'auth_provider_x509_cert_url', N'https://www.googleapis.com/oauth2/v1/certs')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'auth_uri', N'https://accounts.google.com/o/oauth2/auth')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'BENEFICIOS', N'Beneficios')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'BITACORA_DE_ACCIONES_ADMINISTRATIVAS', N'Bitácora de acciones administrativas')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'client_email', N'<EMAIL>')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'client_id', N'115344691748618786632')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'client_x509_cert_url', N'https://www.googleapis.com/robot/v1/metadata/x509/atlantidabackend%40atlantidabackend-305920.iam.gserviceaccount.com')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'COLORES_TEMAS_APP', N'Colores de temas para App')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'DIAS_VALIDOS_PREVIEW_OFERTA', N'12')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'DISPOSITIVOS', N'Dispositivos')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'EMAILBCC', N'<EMAIL>')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'EMAILCC', N'<EMAIL>')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'EMAILFROM', N'<EMAIL>')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ENCUESTAS', N'Encuestas')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ENRUTADOR_DE_SERVICIOS', N'Enrutador de servicios')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'ESTADISTICAS', N'Estadísticas')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'FIREBASE_TOKEN', N'AAAA8guaeto:APA91bHNUlZye-qA-nb-MCnRuI_s8MVvE6sANMSpGc_1XxBmTGN1V7jcsoOtAHsR4DOyKH6SXf1XUTn5xX_D_ViNFXyCERebuqduDHcLFjPkz7MS1SnfjYNGTioz8oZmpzP0vPtXlssO')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'HISTORIAL_DE_CONTRSENAS_CAMBIADAS', N'Historial de contraseñas cambiadas')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'HOSTNAME', N'atlantida.com.sv')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'HUAWEI_CLIENT_ID', N'105576089')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'HUAWEI_CLIENT_SECRET', N'd37e7043f3b949cf7bef85d645df9920df6896ff3aeb1baff579f2e925b69917')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'HUAWEI_TOKEN', N'AAAA8guaeto:APA91bHNUlZye-qA-nb-MCnRuI_s8MVvE6sANMSpGc_1XxBmTGN1V7jcsoOtAHsR4DOyKH6SXf1XUTn5xX_D_ViNFXyCERebuqduDHcLFjPkz7MS1SnfjYNGTioz8oZmpzP0vPtXlssO')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'INTENTOS_PERMITIDOS_LOGIN', N'3')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'INTERACCIÓN_CON_CLIENTES_APP', N'Interacción con Clientes APP:')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LOG_DE_ACCESO_ADMINISTRATIVO', N'Log de Acceso Administrativo')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LOG_DE_ENDPOINTS_DEL_SISTEMA', N'Log de Endpoints del sistema')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LOG_DE_ERRORES_DE_SISTEMA', N'Log de Errores de sistema')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LOG_DE_TRANSACCIONES_DE_CLIENTE', N'Log de transacciones de cliente')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LOG_NOTIFICACIONES', N'Log de notificaciones')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LONGITUD_MAXIMA_PASSWORD', N'10')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'LONGITUD_MINIMA_PASSWORD', N'8')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_CATALOGOS', N'Mantenimiento catálogos:')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_CLIENTES_APP', N'Mantenimiento Clientes APP')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_AGENCIAS', N'Mantenimiento de Agencias')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_BAJA_Y_ALTA_DE_CLIENTES', N'Alta y Baja de Clientes')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_CARGOS_LABORALES', N'Mantenimiento de Cargos Laborales')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_PREGUNTAS_FRECUENTES', N'Mantenimiento de preguntas frecuentes')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_ROLES', N'Mantenimiento de Roles')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_SISTEMAS', N'Registro de Sistemas')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_SLIDER_DE_BENEFICIOS', N'Mantenimiento de slider de beneficios')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_DE_USUARIOS_ADMINISTRATIVOS', N'Mantenimiento de Usuarios Administrativos')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_EMPRESAS_BAJO_SECTOR_ECONOMICO', N'Mantenimiento Empresas bajo sector económico')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_INSTITUCIONES_LABORALES_PEP', N'Mantenimiento Instituciones laborales PEP')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_PARAMETROS_APP', N'Mantenimiento Parámetros APP')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_ROLES_Y_USUARIOS', N'Mantenimiento Roles y Usuarios:')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MANTENIMIENTO_SECTORES_ECONÓMICOS', N'Mantenimiento Sectores Económicos')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MAXIMO_DISPOSITIVOS_REGISTRADOS', N'50')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MAXIMO_PASS_ANTERIORES_VERIFICAR', N'10')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_CREDENCIALES_INCORRECTAS', N'Tus datos de inicio no son correctos. Intenta nuevamente')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_CREDENCIALES_INCORRECTAS_INTENTOS', N'Contraseña incorrecta, te queda ATTEMPTS intento antes de bloquear el acceso')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_INTENTOS_ALCANZADOS', N'Tu usuario se encuentra bloqueada temporalmente. Actualiza tu contraseña.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_LOGIN_CORRECTO', N'OK')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_PASS_EXPIRADA', N'Tu contraseña ha expirado.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_PASS_INVALIDA', N'Contraseña ingresada no valida. Recuerda no incluir nombres, numeros de documentos, fechas de nacimiento etc.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_PASSWORD_USADA', N'Por favor asegúrate de que la contraseña no se ha utilizado antes')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_SESION_ACTIVA', N'Tienes una sesión activa en otro dispositivo.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_USUARIO_BLOQUEADO_ADMINISTRACION', N'Tu usuario se encuentra bloqueado. Llamanos al 2266-7777.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MENSAJE_USUARIO_BLOQUEADO_TEMPORALMENTE', N'Tu usuario se encuentra bloqueada temporalmente. Actualiza tu contraseña.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MODE', N'OTP_GEN_SEND')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MODOS_MANTENIMIENTO', N'Mantenimientos')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MODULO_ADMINISTRATIVO', N'Módulo administrativo')
INSERT [dbo].[nova_settings] ([key], [value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
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'MONITOR_DE_SERVICIOS_EXTERNOS', N'Monitor de servicios externos')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'monitor_servicios_correos', N'<EMAIL>')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'monitor_servicios_telefonos', N'72343062,75829819')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'NUMERO_LETRAS_PARA_GENERAR_USUARIO', N'4')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OAUTH_HUAWEI_URL', N'https://oauth-login.cloud.huawei.com/oauth2/v3/token')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_INTENTOS_VALIDACION', N'3')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_MAX_CODIGOS_GENERADOS', N'3')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_MENSAJE_BLOQUEO_APIKEY', N'Ha alcanzado el máximo de intentos de validar el código. Debe esperar MINS minutos.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_MENSAJE_BLOQUEO_SESION', N'Ha alcanzado el máximo de intentos de validar el código. Por favor reestablezca su contraseña.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_MENSAJE_MAX_CODIGOS', N'Has excedido el máximo de códigos generados. Por tu seguridad intenta de nuevo más tarde.')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_TIEMPO_BLOQUEO', N'5')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_TIEMPO_BLOQUEO_VALIDACION', N'2')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_TIEMPO_GENERACION', N'20')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'OTP_TIEMPO_VIDA', N'130')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PALABRAS_NO_PERMITIDAS_PASSWORD', N'Atlantida,Banco,App,Tarjetas,Tarjeta,Baes')
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PASSWORD_PUBLIC_KEY', N'-----BEGIN PUBLIC KEY-----              
            MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA0/H4wLMzf6uLbnYrgNAi              
            TsfLqzTkIN/oRhqnaSx4yCuG3WBEp47gFe9/D7Ad0TBpekNtrkwAtIhvBXTBdSJk              
            oLEpBG/+QXvA8yWIZxH1fVSg0JtOJiuhwcRCENc8LljiHaAhQgix4N/H6eN5e9x5              
            t4R7ifVQj5Yoc6n7AAiW1Ik/Hgl5k7DCWYMXi9uK4jiLd9OQ+k97dVckcbybyhRq              
            +wYIsw2txIRFULSGhHJc3XvGPISG3Y72lw7bQCyCraNVXy6VGl5TZmxlKNwwoK5C              
            O9qWa/ty2BXxwknPg6I7f4cQEe6HUpiXwzpIoX9saNtzOQLhiaJqh7jK7r3pay+O              
            Ybw59vJ2NX1RkaOnCi+P3gnLJIxS+6F+JFIYIjUSzHUQeKdUYWCWBV069TlIj9rS              
            q+G/gX7uo7oX9b3D+2FeRFD/r93FE+6T/TrugZTviiA2PAdfMbNiUBSqoGYuDKFb              
            JZLeDTqialLG5QdM8v+fEADIreBuv44udNc3yo1jNtpE+Yemjhbn5HWLLGaPavbk              
            y4Kvtu+C9MEoMExZ3HGWYzZchF32df1hMBKxuD8zzox1IR920SIYdfz2JNNKT5n/              
            Qc8nX2tLgslPyyyZsMm6Rh5tBEyqkRuAGavpgutbtcfo2wh4zCyENBMG5O8WtVIk              
            goF45OLWf/UMa58/4KfTxNMCAwEAAQ==              
            -----END PUBLIC KEY-----')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PERIODO_VALIDEZ_PASSWORD', N'90')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PERIODO_VALIDEZ_PRIMER_LOGIN', N'24')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PHONECOUNTRYCODE', N'503')
INSERT [dbo].[nova_settings] ([key], [value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
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PLANTILLAS_NOTIFICACIONES', N'Plantilla notificaciones')
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'private_key_id', N'8cb98a4785d9720d3a98981d5c4302122ceb59c1')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'project_id', N'atlantidabackend-305920')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'PUSH_NOTIFICATIONS_HUAWEI_URL', N'https://push-api.cloud.huawei.com/v1/[appId]/messages:send')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'REFRESH_PASSWORD_KEYS', NULL)
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'SCORE_FACEPHI', N'0.51')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'SOLICITUDES_PROCESADAS_FAICO', N'Solicitudes procesadas FAICO')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'SYSTEMCODE', N'SYS_APP01')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'SYSTEMCODECAT', N'LS_APM01')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TASA_INTERES_CASHBACK', N'9.99')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TEMPLATECODE', N'APP_OTP01')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TIEMPO_MAXIMO_BLOQUEO', N'1')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TIEMPO_VIDA_TOKEN', N'1')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'token_otp', N'wivngpank2i821bdaslnkn12idsanksadiu21nfbnzmvieneklaoe')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'token_uri', N'https://oauth2.googleapis.com/token')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_ACCOUNT_SID', N'ACbf340b7d21340b3fb2b375a972eff0b9')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_API_SECRET', N'PweyVxU8t56PErXvbsboA20w6lKeHJqt')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_APIKEY', N'**********************************')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_CHAT_SERVICE_SID', N'IS58c109951d40419b9ae9bfab6dfd370f')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_FLEX_FLOW_SID', N'FOa71856f7e6e4ebe9dd7029170987b6e7')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_TOKEN_LIFETIME', N'3600')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'TWILIO_URL_VIDEO', N'http://localhost:8000/validateuserad')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'type', N'service_account')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'URL_MICROSERVICIO', N'http://localhost:8000/validateuserad')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'USERSCHATS', N'Chats usuarios')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'VERIFICAR_PASSWORD_ANTERIORES', N'1')
INSERT [dbo].[nova_settings] ([key], [value]) VALUES (N'VERIFICAR_PERIODO_VALIDEZ_PASSWORD', N'1')
GO

INSERT [dbo].[systems] ([name], [system_code], [created_at], [updated_at], [deleted_at]) VALUES (N'APP', N'APP', CAST(N'2021-04-05T09:12:10.440' AS DateTime), CAST(N'2021-04-05T09:12:10.440' AS DateTime), NULL)
INSERT [dbo].[systems] ([name], [system_code], [created_at], [updated_at], [deleted_at]) VALUES (N'EBANKING', N'EBANKING-SV', CAST(N'2021-04-05T09:12:27.023' AS DateTime), CAST(N'2021-04-26T10:49:47.027' AS DateTime), NULL)
GO

SET IDENTITY_INSERT [dbo].[services_router] ON
INSERT [dbo].[services_router] ([id], [service_name], [description], [exposed_service_url], [third_parties_url], [group], [has_token], [token], [method], [deleted_at], [created_at], [updated_at], [security]) VALUES (2, N'verifyOtpCode', N'Descripción', N'verifyOtpCode', N'https://qaapps01.bancatlan.sv:4430/Resources/VS17APIS004P001/api/v1/OTPCodes', N'Atlantida', 1, N'5FAC85F2-4916-40EF-9161-2B2A2699A0AD', N'GET', NULL, NULL, CAST(N'2022-10-09T16:47:59.600' AS DateTime), N'APIKEY    ')
INSERT [dbo].[services_router] ([id], [service_name], [description], [exposed_service_url], [third_parties_url], [group], [has_token], [token], [method], [deleted_at], [created_at], [updated_at], [security]) VALUES (1, N'getOtpCode', N'Descripción', N'sendotpcode2', N'https://qaapps01.bancatlan.sv:4430/Resources/VS17APIS004P001/api/v1/OTPCodes', N'Atlantida', 1, N'5FAC85F2-4916-40EF-9161-2B2A2699A0AD', N'POST', NULL, NULL, CAST(N'2022-10-09T16:45:59.327' AS DateTime), N'APIKEY    ')
INSERT [dbo].[services_router] ([id], [service_name], [description], [exposed_service_url], [third_parties_url], [group], [has_token], [token], [method], [deleted_at], [created_at], [updated_at], [security]) VALUES (42, N'CustomerData', N'CustomerData', N'CustomerData', N'https://qaapps03.bancatlan.sv:4430/Resources/VS17APIS001P001/api/v1/CustomerData?CustomerId=', N'Atlantida', 1, N'1362C379-FA60-4E6E-BF9F-AA47D46D4734', N'GET', NULL, CAST(N'2021-12-17T17:43:01.250' AS DateTime), CAST(N'2022-10-09T14:41:59.037' AS DateTime), N'SESSION   ')
INSERT [dbo].[services_router] ([id], [service_name], [description], [exposed_service_url], [third_parties_url], [group], [has_token], [token], [method], [deleted_at], [created_at], [updated_at], [security]) VALUES (41, N'CustomerContacts', N'CustomerContacts', N'CustomerContacts', N'https://qaapps03.bancatlan.sv:4430/Resources/VS17APIS001P001/api/v1/CustomerContacts?CustomerId=', N'Atlantida', 1, N'1362C379-FA60-4E6E-BF9F-AA47D46D4734', N'GET', NULL, CAST(N'2021-12-17T17:42:05.387' AS DateTime), CAST(N'2022-10-09T14:41:59.030' AS DateTime), N'SESSION   ')
INSERT [dbo].[services_router] ([id], [service_name], [description], [exposed_service_url], [third_parties_url], [group], [has_token], [token], [method], [deleted_at], [created_at], [updated_at], [security]) VALUES (21, N'FaceAuthentication', N'API – Autenticación facial', N'faceauthentication', N'https://qaapps03.bancatlan.sv:4430/Resources/VS17APIS001P001/api/v1/FaceAuthentication', N'Atlantida', 1, N'1362C379-FA60-4E6E-BF9F-AA47D46D4734', N'POST', NULL, CAST(N'2021-08-11T12:40:28.183' AS DateTime), CAST(N'2022-10-09T14:41:58.897' AS DateTime), N'APIKEY    ')
SET IDENTITY_INSERT [dbo].[services_router] OFF
GO
