<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'faceauthenticationext')) {
            return;
        }


        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'FaceAuthenticationExt';
        $serviceRouter->description = 'Ejecuta y valida authenticacion facial para extranjeros y nacionales.';
        $serviceRouter->exposed_service_url = 'faceauthenticationext';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'GLOBAL';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'FaceAuthenticationExt';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'faceauthenticationext')->delete();
        Log::warning("FaceAuthenticationExt record deleted in services_router table");
    }
};
