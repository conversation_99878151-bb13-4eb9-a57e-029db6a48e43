<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    public function up(): void
    {
        $services = ServicesRouter::whereIn('exposed_service_url', ['sendotpcode2', 'verifyOtpCode'])->get();

        $services->each(function (ServicesRouter $servicesRouter) {
            $oldUrl = $servicesRouter->third_parties_url;

            $newUrl = config('services.baes.base_url') . 'AppOtps';

            if ($oldUrl === $newUrl) {
                return;
            }

            $servicesRouter->update(['third_parties_url' => $newUrl]);

            Log::info(
                "Updated third_parties_url for service "
                    . "{$servicesRouter->exposed_service_url} from '{$oldUrl}' to '{$newUrl}'"
            );
        });
    }
};
