<?php

use App\Enums\SecurityType;
use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'ValidateOCR')) {
            $endpoint->security = SecurityType::GLOBAL->value;
            $endpoint->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'ValidateOCR')) {
            $endpoint->security = SecurityType::APIKEY->value;
            $endpoint->save();

            Log::warning("ValidateOCR service security reverted to APIKEY");
        }
    }
};
