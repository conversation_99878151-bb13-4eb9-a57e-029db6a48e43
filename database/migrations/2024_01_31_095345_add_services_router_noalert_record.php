<?php

use App\Models\ServicesRouter;
use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'NoAlert')) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'NoAlert';
        $serviceRouter->description = 'Envío de confirmación de Alerta que se muestra una única vez';
        $serviceRouter->exposed_service_url = 'NoAlert';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'SESSION';
        $serviceRouter->created_at = Carbon::now();
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'NoAlert';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'NoAlert')->delete();
        Log::warning("NoAlert record deleted in services_router table");
    }
};
