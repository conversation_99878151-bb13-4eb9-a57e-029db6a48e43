<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private const ENDPOINT = 'GetBalance';

    private const EXPOSED_SERVICE_URL = 'punto-express/get-balance';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', self::EXPOSED_SERVICE_URL)) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = self::EXPOSED_SERVICE_URL;
        $serviceRouter->description = 'Servicio para obetener el balance de una referencia (si existe información de la referencia o parámetro ingresado).';
        $serviceRouter->exposed_service_url = self::EXPOSED_SERVICE_URL;
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'GLOBAL';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . self::ENDPOINT;
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', self::EXPOSED_SERVICE_URL)->delete();
        Log::warning(self::EXPOSED_SERVICE_URL . " record deleted in services_router table");
    }
};
