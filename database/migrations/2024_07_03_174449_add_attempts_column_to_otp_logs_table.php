<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('otp_log', function (Blueprint $table) {
            if (Schema::hasColumn('otp_log', 'attempts')) {
                return;
            }

            $table->smallInteger('attempts')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('otp_log', function (Blueprint $table) {
            $table->dropColumn('attempts');
        });
    }
};
