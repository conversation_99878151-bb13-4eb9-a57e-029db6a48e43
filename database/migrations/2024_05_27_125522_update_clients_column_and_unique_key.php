<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $keys = [
            'clients_client_id_IDX',
            'clients_client_id_unique',
            'clients_dui_unique',
            'clients_nit_unique',
            'clients_email_unique',
            'clients_nickname_unique',
        ];
    
        foreach ($keys as $key) {
            try {
                Schema::table('clients', function (Blueprint $table) use ($key) {
                    $table->dropIndex($key);
                });
            } catch (Exception $_) {
                Log::warning("Index {$key} in Clients table does not exist");
            }
        }

        Schema::table('clients', function (Blueprint $table) {
            $table->string('first_name', 50)->nullable()->change();
            $table->string('second_name', 50)->nullable()->change();
            $table->string('first_surname', 50)->nullable()->change();
            $table->string('second_surname', 50)->nullable()->change();
            $table->string('married_surname', 50)->nullable()->change();
            $table->string('nit', 17)->nullable()->change();
            $table->string('email', 255)->nullable()->index('IDX_clients_email')->change();
            $table->string('phone_number', 9)->nullable()->index('IDX_clients_phone_number')->change();
            $table->string('nickname', 100)->nullable(false)->change();
            $table->smallInteger('status')->nullable(false)->default('1')->change();

            $table->index('client_id', 'IDX_clients_client_id');
            $table->index('dui', 'IDX_clients_dui');
        });

        DB::statement('ALTER TABLE [dbo].[clients] ADD CONSTRAINT UK_clients_nickname UNIQUE (nickname);');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('ALTER TABLE [dbo].[clients] DROP CONSTRAINT UK_clients_nickname;');

        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex('IDX_clients_dui');
            $table->dropIndex('IDX_clients_email');
            $table->dropIndex('IDX_clients_phone_number');
            $table->dropIndex('IDX_clients_client_id');
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->string('first_name', 255)->nullable(false)->change();
            $table->string('second_name', 255)->nullable()->change();
            $table->string('first_surname', 255)->nullable(false)->change();
            $table->string('second_surname', 255)->nullable()->change();
            $table->string('married_surname', 255)->nullable()->change();
            $table->string('nit', 17)->nullable(false)->unique('clients_nit_unique')->change();
            $table->string('email', 255)->nullable()->unique('clients_email_unique')->change();
            $table->string('nickname', 255)->nullable()->unique('clients_nickname_unique')->change();
            $table->smallInteger('status')->nullable()->change();

            $table->unique('client_id', 'clients_client_id_IDX');
            $table->unique('dui', 'clients_dui_unique');
        });
    }
};
