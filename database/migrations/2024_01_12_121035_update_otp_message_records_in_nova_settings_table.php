<?php

use App\Models\Config;
use Illuminate\Database\Migrations\Migration;

class UpdateOtpMessageRecordsInNovaSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Config::where('key', 'OTP_MENSAJE_MAX_CODIGOS')
            ->update(['value' => 'Has excedido el máximo de códigos generados. Por tu seguridad intenta de nuevo mas tarde.']);
        Config::where('key', 'OTP_MENSAJE_BLOQUEO_SESION')
            ->update(['value' => 'Has excedido el máximo de intentos. Por tu seguridad intenta de nuevo en 5 minutos.']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Config::where('key', 'OTP_MENSAJE_MAX_CODIGOS')
            ->update(['value' => 'Ha excedido el máximo de códigos generados. Espere unos minutos.']);
        Config::where('key', 'OTP_MENSAJE_BLOQUEO_SESION')
            ->update(['value' => 'Ha alcanzado el máximo de intentos de validar el código. Por favor reestablezca su contraseña.']);
    }
}
