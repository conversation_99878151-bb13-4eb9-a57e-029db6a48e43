<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'DeliveryAddresses')) {
            $endpoint->security = 'GLOBAL';
            $endpoint->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'DeliveryAddresses')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();

            Log::warning("DeliveryAddresses service security reverted to APIKEY");
        }
    }
};
