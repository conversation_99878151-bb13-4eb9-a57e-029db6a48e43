<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE = 'api_keys';

    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('api_keys', function (Blueprint $table) {
            if (!Schema::hasColumn(self::TABLE, 'maintenance_mode')) {
                $table->boolean('maintenance_mode')->default(false);
            }

            if (!Schema::hasColumn(self::TABLE, 'type')) {
                $table->smallInteger('type')->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('api_keys', function (Blueprint $table) {
            $table->dropColumn('maintenance_mode');
            $table->dropColumn('type');
        });
    }
};
