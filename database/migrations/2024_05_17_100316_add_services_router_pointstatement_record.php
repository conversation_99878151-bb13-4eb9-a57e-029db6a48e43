<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'PointStatement')) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'PointStatement';
        $serviceRouter->description = 'Obtener información de beneficio: Megapuntos';
        $serviceRouter->exposed_service_url = 'PointStatement';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'GET';
        $serviceRouter->security = 'SESSION';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'PointStatement';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'PointStatement')->delete();
        Log::warning("PointStatement record deleted in services_router table");
    }
};
