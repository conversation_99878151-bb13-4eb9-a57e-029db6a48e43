<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'Authorization')) {
            $endpoint->security = 'NINGUNO';
            $endpoint->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'Authorization')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();

            Log::warning("Authorization service security reverted to APIKEY");
        }
    }
};
