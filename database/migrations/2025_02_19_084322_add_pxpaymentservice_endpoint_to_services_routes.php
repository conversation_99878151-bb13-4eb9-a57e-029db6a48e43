<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private const ENDPOINT = 'PxPayment';

    private const EXPOSED_SERVICE_URL = 'punto-express/payment-service';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', self::EXPOSED_SERVICE_URL)) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = self::EXPOSED_SERVICE_URL;
        $serviceRouter->description = 'Attempts to perform the service\'s payment and returns the result.';
        $serviceRouter->exposed_service_url = self::EXPOSED_SERVICE_URL;
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'SESSION';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . self::ENDPOINT;
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', self::EXPOSED_SERVICE_URL)->delete();
        Log::warning(self::EXPOSED_SERVICE_URL . " record deleted in services_router table");
    }
};
