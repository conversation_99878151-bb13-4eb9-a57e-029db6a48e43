<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;

class UpdateServicesRouterGetdatesSecurity extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'GetDates')) {
            $endpoint->security = 'GLOBAL';
            $endpoint->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'GetDates')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();
        }
    }
}
