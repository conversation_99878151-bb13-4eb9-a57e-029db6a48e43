<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
   /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'ManCloseAccount')) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'ManCloseAccount';
        $serviceRouter->description = 'Crear gestión para eliminación de cuenta';
        $serviceRouter->exposed_service_url = 'ManCloseAccount';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'SESSION';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'ManCloseAccount';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'ManCloseAccount')->delete();
        Log::warning("ManCloseAccount record deleted in services_router table");
    }
};
