<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'ValidateForbiddenWords')) {
            return;
        }


        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'ValidateForbiddenWords';
        $serviceRouter->description = 'Validar palabras no permitidas.';
        $serviceRouter->exposed_service_url = 'ValidateForbiddenWords';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'GET';
        $serviceRouter->security = 'APIKEY';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'ValidateForbiddenWords';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'ValidateForbiddenWords')->delete();
        Log::warning("ValidateForbiddenWords record deleted in services_router table");
    }
};
