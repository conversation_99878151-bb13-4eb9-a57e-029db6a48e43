<?php

use App\Models\Benefit;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $benefitPurchases = Benefit::where('title', 'Compras')->first();
        $benefitPurchases?->update(['description' => '<div>Por cada dólar en compras te damos <strong>3 puntos</strong>. Podés acumular hasta 30,000 puntos mensuales ¡Tus puntos nunca vencen!</div>']);

        $benefitSavings = Benefit::where('title', 'Ahorro')->first();
        $benefitSavings?->update(['description' => '<div>Por cada dólar en compras te regresamos <strong>$0.015 (1.5% de cashback)</strong> con un límite máximo de $150 mensuales en tu cuenta de ahorro</div>']);

        $benefitTravel = Benefit::where('title', 'Viajes')->first();
        $benefitTravel?->update(['description' => '<div>Por cada dólar en compras te damos <strong>1.5 puntos</strong> para canjear <strong>por millas Lifemiles</strong>. Podés acumular hasta 15,000 puntos mensuales ¡Tus puntos nunca vencen!</div>']);
    }
};
