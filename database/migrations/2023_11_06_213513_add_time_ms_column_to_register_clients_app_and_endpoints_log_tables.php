<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTimeMsColumnToRegisterClientsAppAndEndpointsLogTables extends Migration
{
    public function up()
    {
        Schema::table('register_clients_app', function (Blueprint $table) {
            $table->dropColumn('updated_at');
            $table->integer('response_time_ms')->nullable();
        });

        Schema::table('endpoints_log', function (Blueprint $table) {
            $table->dropColumn('updated_at');
            $table->integer('response_time_ms')->nullable();
        });
    }

    public function down()
    {
        Schema::table('register_clients_app', function (Blueprint $table) {
            $table->dropColumn('response_time_ms');
            $table->timestamp('updated_at')->nullable();
        });

        Schema::table('endpoints_log', function (Blueprint $table) {
            $table->dropColumn('response_time_ms');
            $table->timestamp('updated_at')->nullable();
        });
    }
}
