<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'ValidateCustomerContact')) {
            return;
        }


        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'ValidateCustomerContact';
        $serviceRouter->description = 'Validar información de contacto del cliente.';
        $serviceRouter->exposed_service_url = 'ValidateCustomerContact';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'APIKEY';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'ValidateCustomerContact';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'ValidateCustomerContact')->delete();
        Log::warning("ValidateCustomerContact record deleted in services_router table");
    }
};
