<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private const ENDPOINT = 'FaceAuthenticationPublic';

    private const EXPOSED_ENDPOINT_URL = 'faceauthentication-open';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', self::ENDPOINT)) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = self::ENDPOINT;
        $serviceRouter->description = 'Autenticación facial sin token.';
        $serviceRouter->exposed_service_url = self::EXPOSED_ENDPOINT_URL;
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'APIKEY';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . self::ENDPOINT;
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', self::EXPOSED_ENDPOINT_URL)->delete();
        Log::warning(self::ENDPOINT . " record deleted in services_router table");
    }
};
