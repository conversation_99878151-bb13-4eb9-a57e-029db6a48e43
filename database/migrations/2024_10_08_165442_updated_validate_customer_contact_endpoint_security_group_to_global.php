<?php

use App\Enums\SecurityType;
use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private const ENDPOINT = 'ValidateCustomerContact';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        ServicesRouter::where('service_name', self::ENDPOINT)
            ->where('exposed_service_url', self::ENDPOINT)
            ->update(['security' => SecurityType::GLOBAL->value]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::where('service_name', self::ENDPOINT)
            ->where('exposed_service_url', self::ENDPOINT)
            ->update(['security' => SecurityType::APIKEY->value]);
    }
};
