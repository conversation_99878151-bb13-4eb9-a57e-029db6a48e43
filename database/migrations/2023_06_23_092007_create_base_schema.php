<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;

class CreateBaseSchema extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $schemaScript = file_get_contents('database/scripts/schema_base.sql');
        $seedScript = file_get_contents('database/scripts/seed_base.sql');
        if (App::environment('testing')) {
            $schemaScript = file_get_contents('database/scripts/schema_tests.sql');
            $seedScript = file_get_contents('database/scripts/seed_tests.sql');
        }

        foreach (explode("\nGO", $schemaScript . "\n" . $seedScript) as $script) {
            DB::statement($script);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $sqlScript = file_get_contents('database/scripts/schema_base_drop.sql');
        foreach (explode("\ngo", $sqlScript) as $script) {
            DB::statement($script);
        }
    }
}
