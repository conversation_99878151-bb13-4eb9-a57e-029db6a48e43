<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;

class UpdateServicesRouterBamessagessendmessageRecord extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'BaMessagesSendMessage')) {
            $endpoint->third_parties_url = 'https://qaapps03.bancatlan.sv:4430/Resources/UAT_JWT/VS17APIS001P001/api/v1/AppBaMessages';
            $endpoint->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'BaMessagesSendMessage')) {
            $endpoint->third_parties_url = 'https://qaapps01.bancatlan.sv:4430/Resources/VS17APIS004P001/api/v1/BaMessages';
            $endpoint->save();
        }
    }
}
