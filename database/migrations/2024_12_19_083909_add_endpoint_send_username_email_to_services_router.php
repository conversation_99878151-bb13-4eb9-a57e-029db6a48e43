<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private const ENDPOINT = 'SendUsernameByEmail';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', self::ENDPOINT)) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = self::ENDPOINT;
        $serviceRouter->description = 'Enviar correo de recuperación de usuario.';
        $serviceRouter->exposed_service_url = self::ENDPOINT;
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'SESSION';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . self::ENDPOINT;
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', self::ENDPOINT)->delete();
        Log::warning(self::ENDPOINT . " record deleted in services_router table");
    }
};
