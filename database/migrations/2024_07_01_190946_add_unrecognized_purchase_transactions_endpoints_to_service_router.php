<?php

use App\Models\ServicesRouter;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'TransactionUnrecognizedPurchase')) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'TransactionUnrecognizedPurchase';
        $serviceRouter->description = 'Enviar transacciones de compras no reconocidas.';
        $serviceRouter->exposed_service_url = 'TransactionUnrecognizedPurchase';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'POST';
        $serviceRouter->security = 'SESSION';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'TransactionUnrecognizedPurchase';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'TransactionUnrecognizedPurchase')->delete();
        Log::warning("TransactionUnrecognizedPurchase record deleted in services_router table");
    }
};
