<?php

use App\Models\Answer;
use App\Models\Client;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class ChangeClientIdInAnswersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // The foreign key may not be there.
        try {
            Schema::table('answers', function (Blueprint $table) {
                $table->dropForeign('answers_client_FK');
            });
        } catch (Exception $_) {
            Log::warning("Foreign Key (answers_client_FK) does not exist");
        }

        // Rename the column.
        Schema::table('answers', function (Blueprint $table) {
            $table->renameColumn('client_id', 'customer_id');
        });

        // Map the old client_id that points to id in clients to
        // client_id; the ID provided by an external system.
        $clientIds = Answer::select('customer_id')
            ->get()
            ->pluck('customer_id')
            ->unique();
        $users = Client::select(['id', 'client_id'])
            ->whereIn('id', $clientIds)
            ->get();

        $clientIdToCustomerIdMap = collect();
        $nonMapped = collect();
        foreach ($clientIds as $id) {
            if ($user = $users->firstWhere('id', $id)) {
                $clientIdToCustomerIdMap->put($id, $user->client_id);
                continue;
            }

            $nonMapped->push($id);
        }

        DB::transaction(function () use ($clientIdToCustomerIdMap) {
            foreach ($clientIdToCustomerIdMap as $clientId => $customerId) {
                $answer = Answer::firstWhere('customer_id', $clientId);
                $answer->update(['customer_id' => $customerId]);
                Log::info("Mapped answers.id={$answer->id}: from client_id={$clientId} to customer_id={$customerId}");
            }
        });

        if ($nonMapped->isNotEmpty()) {
            Log::info('Non-mapped answers.customer_id = ' . $nonMapped->join(','));
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('answers', function (Blueprint $table) {
            // Data may be missing due to mapping. Also, it may not be possible to
            // add the foreign key.
            $table->renameColumn('customer_id', 'client_id');
        });
    }
}
