<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (ServicesRouter::firstWhere('exposed_service_url', 'biometry-verification')) {
            return;
        }

        $serviceRouter = new ServicesRouter();
        $serviceRouter->service_name = 'biometry-verification';
        $serviceRouter->description = 'Verificación de biometría';
        $serviceRouter->exposed_service_url = 'biometry-verification';
        $serviceRouter->has_token = true;
        $serviceRouter->method = 'GET';
        $serviceRouter->security = 'APIKEY';
        $serviceRouter->group = 'Atlantida';
        $serviceRouter->third_parties_url = config('services.baes.base_url') . 'BiometryVerification';
        $serviceRouter->token = config('services.baes.key');
        $serviceRouter->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ServicesRouter::firstWhere('exposed_service_url', 'biometry-verification')->delete();
        Log::warning("biometry-verification record deleted in services_router table");
    }
};
