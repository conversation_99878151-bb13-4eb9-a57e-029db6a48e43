<?php

use App\Models\ServicesRouter;
use Illuminate\Database\Migrations\Migration;

class UpdateServicesRouterGlobalSecurityRecords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'catalogs')) {
            $endpoint->security = 'GLOBAL';
            $endpoint->save();
        }
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'faceauthentication')) {
            $endpoint->security = 'GLOBAL';
            $endpoint->save();
        }
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'Documents')) {
            $endpoint->security = 'GLOBAL';
            $endpoint->save();
        }
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'GeoZones')) {
            $endpoint->security = 'GLOBAL';
            $endpoint->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'catalogs')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();
        }
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'faceauthentication')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();
        }
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'Documents')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();
        }
        if ($endpoint = ServicesRouter::firstWhere('exposed_service_url', 'GeoZones')) {
            $endpoint->security = 'APIKEY';
            $endpoint->save();
        }
    }
}
