<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        dropConstraintIfExists('clients', 'UK_clients_nickname');

        Schema::table('clients', function (Blueprint $table) {
            $table->string('nickname')
                ->nullable()
                ->change();
            $table->string('nickname_vbank')
                ->after('nickname')
                ->nullable();
        });

        createUniqueFilteredIndex('clients', 'UK_clients_nickname', 'nickname');
        createUniqueFilteredIndex('clients', 'UK_clients_nickname_vbank', 'nickname_vbank');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        dropConstraintIfExists('clients', 'UK_clients_nickname_vbank');

        Schema::table('clients', function (Blueprint $table) {
            $table->dropColumn('nickname_vbank');
        });
    }
};
