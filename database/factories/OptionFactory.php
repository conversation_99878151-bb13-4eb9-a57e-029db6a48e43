<?php

namespace Database\Factories;

use App\Models\Option;
use App\Models\Question;
use Illuminate\Database\Eloquent\Factories\Factory;

class OptionFactory extends Factory
{
    public function definition(): array
    {
        return [
            'question_id' => Question::factory(),
            'value' => function (array $attributes) {
                return Question::find($attributes['question_id'])->type === Question::QUANTITATIVE
                    ? strval($this->faker->numberBetween(1, 10))
                    : $this->faker->words(rand(1, 3), true);
            },
            'type' => $this->faker->randomElement([Option::OPEN, Option::CLOSE]),
        ];
    }
}
