<?php

namespace Database\Factories;

use App\Models\OtpLogs;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class OtpLogsFactory extends Factory
{
    public function definition(): array
    {
        return [
            'sharedkey' => Str::random(),
            'identifier' => Str::uuid()->toString(),
            'type' => $this->faker->randomElement(['EMAIL', 'PHONENUMBER']),
            'value' => function (array $attributes) {
                return $attributes['type'] === 'EMAIL'
                    ? $this->faker->email()
                    : $this->faker->phoneNumber();
            },
            'otpcode' => 1,
            'verified' => OtpLogs::NOT_VERIFIED,
            'created_at' => now(),
        ];
    }
}
