<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\Device;
use Illuminate\Database\Eloquent\Factories\Factory;
use Tests\Traits\CreatesRsaKeys;

class DeviceFactory extends Factory
{
    use CreatesRsaKeys;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        [, $publicKey] = $this->createKeyPair();

        return [
            'client_id' => Client::factory(),
            'active' => $this->faker->boolean(),
            'device_id' => $this->faker->uuid(),
            'device_name' => $this->faker->lexify(),
            'online' => $this->faker->randomElement([Device::ONLINE, Device::OFFLINE]),
            'biometric_public_key' => $publicKey,
            'verification_challenge' => $this->faker->lexify(),
            'huawei' => false,
        ];
    }

    /**
     * Sets device as online.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function online()
    {
        return $this->state(fn () => ['online' => Device::ONLINE]);
    }

    /**
     * Sets device as offline.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function offline()
    {
        return $this->state(fn () => ['online' => Device::OFFLINE]);
    }

    /**
     * Sets as null the public key.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function emptyPublicKey()
    {
        return $this->state(fn () => ['biometric_public_key' => null]);
    }

    /**
     * Sets device as active.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(fn () => ['active' => true]);
    }

    /**
     * Sets as null the verification challenge.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function emptyChallenge()
    {
        return $this->state(fn () => ['verification_challenge' => null]);
    }
}
