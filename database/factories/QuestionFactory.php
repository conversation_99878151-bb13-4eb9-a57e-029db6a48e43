<?php

namespace Database\Factories;

use App\Models\Question;
use App\Models\Survey;
use Illuminate\Database\Eloquent\Factories\Factory;

class QuestionFactory extends Factory
{
    public function definition(): array
    {
        return [
            'survey_id' => Survey::factory(),
            'type' => $this->faker->randomElement([Question::QUANTITATIVE, Question::QUALITATIVE]),
            'question' => '¿' . $this->faker->sentence() . '?',
        ];
    }
}
