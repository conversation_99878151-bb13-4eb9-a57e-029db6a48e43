<?php

namespace Database\Factories;

use App\Models\MaintenanceModes;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class MaintenancePeriodFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MaintenanceModes::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $yesterday = now()->subDay();

        return [
            'date_time_maintenance_start' => $yesterday,
            'date_time_maintenance_end' => $yesterday,
            'reason' => Str::random(),
            'client_message' => Str::random(),
            'user_id' => User::factory()->create()->id,
            'user_approval_id' => User::factory()->create()->id,
            'previous_block' => 1,
            'status' => MaintenanceModes::ACTIVE,
        ];
    }

    /**
     * Sets period as inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(fn () => ['status' => MaintenanceModes::INACTIVE]);
    }
}
