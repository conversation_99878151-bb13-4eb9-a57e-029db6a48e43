<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\PasswordHistories;
use Illuminate\Database\Eloquent\Factories\Factory;

class PasswordLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PasswordHistories::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'user_id' => Client::factory()->create()->id,
            'password' => bcrypt('123456'),
        ];
    }
}
