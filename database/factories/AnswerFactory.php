<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\Option;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class AnswerFactory extends Factory
{
    public function definition(): array
    {
        return [
            'option_id' => Option::factory(),
            'customer_id' => Client::factory(),
            'other_answers' => Arr::random([null, $this->faker->sentence()]),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }
}
