<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\Systems;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClientSystemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'client_id' => Client::factory(),
            'system_id' => Systems::factory(),
        ];
    }
}
