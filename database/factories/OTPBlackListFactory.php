<?php

namespace Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OTPBlackList>
 */
class OTPBlackListFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'emitter' => (string) $this->faker->randomNumber(9, true),
            'block_time_start' => Carbon::now(),
            'block_time_end' => Carbon::now()->addMinutes(5),
        ];
    }
}
