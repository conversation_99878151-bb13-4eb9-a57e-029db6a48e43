<?php

namespace Database\Factories;

use App\Models\Endpoints;
use Illuminate\Database\Eloquent\Factories\Factory;

class EndpointFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Endpoints::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->word(),
            'uri' => $this->faker->word(),
            'description' => $this->faker->word(),
        ];
    }
}
