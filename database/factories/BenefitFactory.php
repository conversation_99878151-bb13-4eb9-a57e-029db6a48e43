<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class BenefitFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'title' => Str::random(),
            'description' => Str::random(),
            'example' => Str::random(),
            'icon' => $this->faker->uuid() . '.png',
            'main' => $this->faker->boolean(),
            'order' => $this->faker->randomDigit(),
            'longcode' => Str::random(),
            'storehouse_id' => null,
        ];
    }
}
