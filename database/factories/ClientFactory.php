<?php

namespace Database\Factories;

use App\Models\Client;
use App\Services\Dui;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ClientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Client::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $dui = app(Dui::class)->generate();

        return [
            'mode' => $this->faker->lexify(),
            'client_id' => random_int(0, PHP_INT_MAX),
            'first_name' => $this->faker->word(),
            'first_surname' => $this->faker->word(),
            'dui' => $dui,
            'nit' => $dui,
            'email' => $this->faker->email(),
            'phone_number' => (string) $this->faker->randomNumber(9, true),
            'password' => bcrypt('123456'),
            'nickname' => Str::random(),
            'status' => Client::ENABLE,
            'creditcard_application_id' => strval(random_int(0, 9999)),
            'homologated' => false
        ];
    }

    /**
     * Sets user as blocked by OTP.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function blockedByOtp()
    {
        return $this->state(fn() => ['status' => Client::BLOCKOTP]);
    }

    /**
     * Sets user as blocked by face auth.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function blockedByFaceAuth()
    {
        return $this->state(fn() => ['status' => Client::BLOCK_BY_FACE_AUTH]);
    }

    /**
     * Sets user as disabled.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function disabled()
    {
        return $this->state(fn() => ['status' => Client::DISABLE]);
    }

    /**
     * Fills baes_token related fields.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withBaesToken()
    {
        return $this->state(fn() => [
            'baes_token' => Str::random(100),
            'baes_refresh_token' => Str::random(100),
        ]);
    }
}
