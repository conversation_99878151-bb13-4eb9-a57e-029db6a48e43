<?php

namespace Database\Factories;

use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PerfilImages>
 */
class PerfilImagesFactory extends Factory
{
    public function definition(): array
    {
        return [
            'client_id' => Client::factory(),
            'image' => $this->faker->word() . '.jpg',
            'type' => $this->faker->randomElement(['BackgroundImage', 'ProfileImage'])
        ];
    }
}
