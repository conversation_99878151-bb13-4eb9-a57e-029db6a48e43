<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Answer;
use App\Models\Option;
use App\Models\Question;
use App\Models\Survey;
use Illuminate\Database\Seeder;

class SurveySeeder extends Seeder
{
    const FAKE_SURVEYS = 2;
    const FAKE_QUESTIONS_PER_SURVEY = 2;
    const FAKE_OPTIONS_PER_QUESTION = 2;
    const FAKE_ANSWERS_PER_OPTION = 30;

    public function run(): void
    {
        $surveys = Survey::factory(self::FAKE_SURVEYS)->create();

        $surveys->each(function (Survey $survey) {
            // Create questions for the survey
            $questions = $survey->questions()->saveMany(Question::factory(self::FAKE_QUESTIONS_PER_SURVEY)->make(['survey_id' => $survey->getKey()]));
            $questions->each(
                fn (Question $question) => $question->options()->saveMany(
                    Option::factory(self::FAKE_OPTIONS_PER_QUESTION)
                        ->make(['question_id' => $question->getKey()])
                )
            );

            // Create answers for each question's options
            $options = $questions->map(fn (Question $question) => $question->options)->flatten();
            $options->each(
                fn (Option $option) =>
                $option->answers()->saveMany(Answer::factory(self::FAKE_ANSWERS_PER_OPTION)->make(['option_id' => $option->getKey()]))
            );
        });
    }
}
