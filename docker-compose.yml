networks:
  tc-network:
    name: tc_network

services:
  php:
    build:
      dockerfile: Dockerfile.local
      context: ./cicd
      args:
        UID: ${HOST_UID}
    container_name: tc_api_php
    volumes:
      - .:/var/www/html
      - ./cicd/apache/000-default.conf:/etc/apache2/sites-available/000-default.conf
      - ./cicd/php:/usr/local/etc/php
    networks:
      - tc-network
    ports:
      - 3000:80
    environment:
      - PHP_CS_FIXER_IGNORE_ENV=1
  db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    container_name: db
    restart: unless-stopped
    networks:
      - tc-network
    ports:
      - 1433:1433
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${DB_PASSWORD}