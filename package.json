{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --config=node_modules/laravel-mix/setup/webpack.config.js", "build-Dashboard": "cd nova-components/Dashboard && npm run dev", "build-Dashboard-prod": "cd nova-components/Dashboard && npm run prod", "build-welcome": "cd nova-components/Welcome && npm run dev", "build-welcome-prod": "cd nova-components/Welcome && npm run prod", "build-Inicio": "cd nova-components/Inicio && npm run dev", "build-Inicio-prod": "cd nova-components/Inicio && npm run prod", "build-inicio": "cd nova-components/Inicio && npm run dev", "build-inicio-prod": "cd nova-components/Inicio && npm run prod", "build-custom-resource-toolbar": "cd nova-components/CustomResourceToolbar && npm run dev", "build-custom-resource-toolbar-prod": "cd nova-components/CustomResourceToolbar && npm run prod", "build-custom-index-toolbar": "cd nova-components/CustomIndexToolbar && npm run dev", "build-custom-index-toolbar-prod": "cd nova-components/CustomIndexToolbar && npm run prod", "build-my-index-toolbar-btn": "cd nova-components/MyIndexToolbarBtn && npm run dev", "build-my-index-toolbar-btn-prod": "cd nova-components/MyIndexToolbarBtn && npm run prod", "build-FilterCard": "cd nova-components/FilterCard && npm run dev", "build-FilterCard-prod": "cd nova-components/FilterCard && npm run prod", "build-analyticscountries": "cd nova-components/Analyticscountries && npm run dev", "build-analyticscountries-prod": "cd nova-components/Analyticscountries && npm run prod", "build-analyticsvisits": "cd nova-components/Analyticsvisits && npm run dev", "build-analyticsvisits-prod": "cd nova-components/Analyticsvisits && npm run prod", "build-analyticspagesmostvisited": "cd nova-components/Analyticspagesmostvisited && npm run dev", "build-analyticspagesmostvisited-prod": "cd nova-components/Analyticspagesmostvisited && npm run prod", "build-analyticsos": "cd nova-components/Analyticsos && npm run dev", "build-analyticsos-prod": "cd nova-components/Analyticsos && npm run prod", "build-analyticstimeonsite": "cd nova-components/Analyticstimeonsite && npm run dev", "build-analyticstimeonsite-prod": "cd nova-components/Analyticstimeonsite && npm run prod", "build-stripe-inspector": "cd nova-components/StripeInspector && npm run dev", "build-stripe-inspector-prod": "cd nova-components/StripeInspector && npm run prod", "build-price-tracker": "cd nova-components/PriceTracker && npm run dev", "build-price-tracker-prod": "cd nova-components/PriceTracker && npm run prod"}, "devDependencies": {"axios": "^0.21.1", "cross-env": "^7.0", "laravel-mix": "^5.0.1", "lodash": "^4.17.19", "resolve-url-loader": "^3.1.0", "vue-template-compiler": "^2.6.12"}, "dependencies": {"apexcharts": "^3.26.0", "filepond": "^4.26.0", "puppeteer": "^6.0.0", "puppeteer-core": "^7.0.0", "tailwindcss": "^2.0.2"}}